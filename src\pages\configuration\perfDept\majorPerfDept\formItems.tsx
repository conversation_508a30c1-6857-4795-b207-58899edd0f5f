export const MajorPerfDeptItems = (totalList) => [
  {
    name: 'Code',
    title: '绩效大科代码',
    dataType: 'text',
    rules: [
      { required: true },
      ({ getFieldValue }) => ({
        validator(_, value) {
          if (totalList.findIndex((d) => d.Code === value) > -1) {
            return Promise.reject(new Error('该编码已存在！'));
          }
          return Promise.resolve();
        },
      }),
    ],
    visible: true,
  },
  {
    name: 'Name',
    title: '绩效大科名称',
    dataType: 'text',
    rules: [{ required: true }],
    visible: true,
  },
  {
    name: 'IsValid',
    title: '是否有效',
    dataType: 'switch',
    initialValue: true,
  },
];
