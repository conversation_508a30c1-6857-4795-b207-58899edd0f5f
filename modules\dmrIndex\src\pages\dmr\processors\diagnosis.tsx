import {
  DmrInsuranceHqmsIcdeItem,
  IcdeOperResp,
  IcdeResp,
  IcdeRespItem,
} from '@/pages/dmr/network/interfaces';
import { getIcdeInfoWithStrictMode } from '@/pages/dmr/network/get';
import { icdeExtraMap } from '@uni/grid/src/components/icde-oper-input/input';
import {
  filterIcdeCodeEmpty,
  filterIdAddAndAllCellEmptyRow,
  icdeOperationTableNoDataAddOne,
} from '@/pages/dmr/processors/processors';
import isNil from 'lodash/isNil';
import { v4 as uuidv4 } from 'uuid';
import { tumorKeys } from '@/pages/dmr/constants';
import cloneDeep from 'lodash/cloneDeep';
import { isEmptyValues } from '@uni/utils/src/utils';

export const isHospitalDiagnosisResponseProcessor = (
  formFieldValue: any,
  icdeItems: IcdeResp,
) => {
  initializeIcdeItemKeysByPrefix(formFieldValue, 'IcdeAdms');
  // 入院诊断
  formFieldValue['IcdeAdmsItem'] = icdeItems?.IcdeAdms?.at(0);
  Object.keys(icdeItems?.IcdeAdms?.at(0) ?? {})?.forEach((key) => {
    formFieldValue[`IcdeAdms${key}`] = icdeItems?.IcdeAdms?.at(0)?.[key];
  });
};

export const isHospitalDiagnosisRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  // 入院诊断
  if (formFieldValues?.IcdeAdmsIcdeCode) {
    data['CardIcdeAdms'] = [
      {
        ...icdeItemKeyValueProcessor(formFieldValues, 'IcdeAdms'),
        IcdeSort: 0,
      },
    ]
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['IcdeCode']);
        } else {
          return true;
        }
      })
      ?.map((item) => {
        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        if (isEmptyValues(item['IsMain'])) {
          item['IsMain'] = false;
        }

        return item;
      });
  } else {
    data['CardIcdeAdms'] = [];
  }
};

export const diagnosisItemResponseProcessor = (
  formFieldValue: any,
  icdeItems: IcdeResp,
) => {
  initializeIcdeItemKeysByPrefix(formFieldValue, 'IcdeOtps');
  // 门急诊诊断
  formFieldValue['IcdeOtpsItem'] = icdeItems?.IcdeOtps?.at(0);
  Object.keys(icdeItems?.IcdeOtps?.at(0) ?? {})?.forEach((key) => {
    formFieldValue[`IcdeOtps${key}`] = icdeItems?.IcdeOtps?.at(0)?.[key];
  });
};

export const diagnosisItemRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  // 门急诊诊断
  if (formFieldValues?.IcdeOtpsIcdeCode) {
    data['CardIcdeOtps'] = [
      {
        ...icdeItemKeyValueProcessor(formFieldValues, 'IcdeOtps'),
        IcdeSort: 0,
      },
    ]
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['IcdeCode']);
        } else {
          return true;
        }
      })
      ?.map((item) => {
        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        if (isEmptyValues(item['IsMain'])) {
          item['IsMain'] = false;
        }

        return item;
      });
  } else {
    data['CardIcdeOtps'] = [];
  }
};

export const injuryPoisonedItemResponseProcessor = (
  formFieldValue: any,
  icdeItems: IcdeResp,
) => {
  initializeIcdeItemKeysByPrefix(formFieldValue, 'IcdeDamgs');
  // 损伤 中毒
  formFieldValue['IcdeDamgsItem'] = icdeItems?.IcdeDamgs?.at(0);
  Object.keys(icdeItems?.IcdeDamgs?.at(0) ?? {})?.forEach((key) => {
    formFieldValue[`IcdeDamgs${key}`] = icdeItems?.IcdeDamgs?.at(0)?.[key];
  });
};

export const injuryPoisonedItemRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  // 损伤、中毒外部原因 编码诊断
  if (formFieldValues?.IcdeDamgsIcdeCode) {
    data['CardIcdeDamgs'] = [
      {
        ...icdeItemKeyValueProcessor(formFieldValues, 'IcdeDamgs'),
        IcdeSort: 0,
      },
    ]
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['IcdeCode']);
        } else {
          return true;
        }
      })
      ?.map((item) => {
        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        if (isEmptyValues(item['IsMain'])) {
          item['IsMain'] = false;
        }

        return item;
      });
  } else {
    data['CardIcdeDamgs'] = [];
  }
};

export const pathologicalDiagnosisItemResponseProcessor = (
  formFieldValue: any,
  icdeItems: IcdeResp,
) => {
  // TODO 肿瘤病理诊断 字段 processor
  initializeIcdeItemKeysByPrefix(formFieldValue, 'IcdePathos');
  // 病理诊断
  formFieldValue['IcdePathosItem'] = icdeItems?.IcdePathos?.at(0);
  Object.keys(icdeItems?.IcdePathos?.at(0) ?? {})?.forEach((key) => {
    if (!key?.toLowerCase().startsWith('tumor') && !tumorKeys?.includes(key)) {
      // 不能是 肿瘤 也不能在  列表里面
      formFieldValue[`IcdePathos${key}`] = icdeItems?.IcdePathos?.at(0)?.[key];
    }
  });
};

export const pathologicalDiagnosisTableResponseProcessor = (
  formFieldValue: any,
  icdeItems: IcdeResp,
) => {
  // 病理诊断 表格
  let pathologyTableData = [];
  for (let item of icdeItems?.IcdePathos) {
    item['id'] = item['IcdeId'] ?? item['Id'];
    item['PathologyIcdeCode'] = item['IcdeCode'];
    item['PathologyIcdeName'] = item['IcdeName'];

    pathologyTableData.push(item);
  }
  formFieldValue['pathological-diagnosis-table'] = pathologyTableData?.sort(
    (a, b) => (a?.IcdeSort ?? 0) - (b?.IcdeSort ?? 0),
  );

  formFieldValue['pathological-diagnosis-table'] =
    icdeOperationTableNoDataAddOne(
      formFieldValue['pathological-diagnosis-table'],
    );

  formFieldValue['pathologicalDiagnosisTable'] = cloneDeep(
    formFieldValue['pathological-diagnosis-table']?.slice(),
  );
};

export const pathologicalDiagnosisItemRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  // TODO 肿瘤病理诊断 字段 processor

  // 病理诊断编码
  // 去除 IcdeCode 限制
  if (formFieldValues?.IcdePathosIcdeCode) {
    data['CardIcdePathos'] = [
      {
        ...icdeItemKeyValueProcessor(formFieldValues, 'IcdePathos'),
        IcdeSort: 0,
      },
    ]
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['IcdeCode']);
        } else {
          return true;
        }
      })
      ?.map((item) => {
        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        if (isEmptyValues(item['IsMain'])) {
          item['IsMain'] = false;
        }

        return item;
      });
  } else {
    data['CardIcdePathos'] = [];
  }
};

export const pathologicalDiagnosisTableRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  let pathologicalTableData = (
    formFieldValues?.['pathological-diagnosis-table'] || []
  )
    ?.slice()
    ?.filter((item) => filterIdAddAndAllCellEmptyRow(item));

  if (pathologicalTableData?.length === 0) {
    data['CardIcdePathos'] = [];
  }

  if (pathologicalTableData?.length > 0) {
    data['CardIcdePathos'] = pathologicalTableData
      .map((item, index) => {
        item['IcdeSort'] = index;

        item['IcdeCode'] = item['PathologyIcdeCode'];
        item['IcdeName'] = item['PathologyIcdeName'];
        return item;
      })
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['IcdeCode']);
        } else {
          return true;
        }
      })
      ?.map((item) => {
        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        return item;
      });
  }
};

export const diagnosisTableResponseProcessor = async (
  formFieldValue: any,
  icdeItems: IcdeResp,
  insuranceIcdeItems: DmrInsuranceHqmsIcdeItem[],
  hqmsIcdeItems: DmrInsuranceHqmsIcdeItem[],
  icdeMetaData?: IcdeOperResp,
) => {
  // 诊断表格
  let icdeTableData = [];

  if (icdeItems?.IcdeDscgs?.length > 0) {
    let icdeInfosWithExtra =
      icdeMetaData ??
      (
        await getIcdeInfoWithStrictMode(
          icdeItems?.IcdeDscgs?.map((item) => item.IcdeCode),
        )
      )?.data;

    for (let item of icdeItems?.IcdeDscgs) {
      item['id'] = item['IcdeId'] ?? item['Id'];

      let icdeInfoWithExtra = icdeInfosWithExtra?.Data?.find(
        (itemWithExtra) => itemWithExtra?.Code === item?.IcdeCode,
      );

      if (icdeInfoWithExtra) {
        // 合并一份出来
        item = {
          ...icdeInfoWithExtra,
          ...item,
        };
      }

      // 医保
      let insuranceIcdeItem = insuranceIcdeItems.find(
        (insuranceItem) => insuranceItem?.UniqueId === item.UniqueId,
      );
      if (insuranceIcdeItem) {
        item['InsurCode'] = insuranceIcdeItem?.IcdeCode;
        item['InsurName'] = insuranceIcdeItem?.IcdeName;
        item['IsMain'] = insuranceIcdeItem?.IsMain;
        item['IsReported'] =
          insuranceIcdeItem?.IsMain == true
            ? true
            : insuranceIcdeItem?.IsReported;
        // 现在就一个这个有用
        item['InsurIsObsolete'] =
          insuranceIcdeItem?.insuranceMetaData?.IsObsolete;
      }

      // hqms
      let hqmsIcdeItem = hqmsIcdeItems.find(
        (hqmsItem) => hqmsItem?.UniqueId === item.UniqueId,
      );
      if (hqmsIcdeItem) {
        item['HqmsCode'] = hqmsIcdeItem?.IcdeCode;
        item['HqmsName'] = hqmsIcdeItem?.IcdeName;
      }

      // icdeExtra 处理一下
      item['IcdeExtra'] = Object.keys(icdeExtraMap)?.filter(
        (key) => item?.[key] ?? false,
      );

      icdeTableData.push(item);
    }
  }

  formFieldValue['diagnosis-table'] = icdeTableData?.sort(
    (a, b) => (a?.IcdeSort ?? 0) - (b?.IcdeSort ?? 0),
  );

  formFieldValue['diagnosis-table'] = icdeOperationTableNoDataAddOne(
    formFieldValue['diagnosis-table'],
  );

  // 为了form item的刷新
  formFieldValue['diagnosisTable'] = cloneDeep(
    formFieldValue['diagnosis-table']?.slice(),
  );
};

export const diagnosisTableRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  let icdeTableData = (formFieldValues?.['diagnosis-table'] || [])
    ?.slice()
    ?.filter((item) => filterIdAddAndAllCellEmptyRow(item));

  if (icdeTableData?.length === 0) {
    data['CardIcdeDscgs'] = [];
  }

  // 诊断
  if (icdeTableData?.length > 0) {
    data['CardIcdeDscgs'] = icdeTableData
      ?.map((item, index) => {
        item['IcdeSort'] = index;

        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        return item;
      })
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['IcdeCode']);
        } else {
          return true;
        }
      });
  }
};

export const tumorItemResponseProcessor = (
  formFieldValue: any,
  icdeItems: IcdeResp,
) => {
  Object.keys(icdeItems?.IcdePathos?.at(0) ?? {})?.forEach((key) => {
    formFieldValue[key] = icdeItems?.IcdePathos?.at(0)?.[key];
  });
};

export const tumorItemRequestParamProcessor = (
  formFieldValues: any,
  data: any,
) => {
  // 大于1 表示 是表格 不填
  if (data['CardIcdePathos']?.length > 1) {
    return;
  }

  // 不存在的话 新建一条
  if (
    data['CardIcdePathos'] === undefined ||
    data['CardIcdePathos']?.at(0) === undefined
  ) {
    data['CardIcdePathos'] = [];
  }

  // tumor开头的都是肿瘤
  Object?.keys(formFieldValues)?.forEach((key) => {
    if (key?.toLowerCase().startsWith('tumor')) {
      if (!isEmptyValues(formFieldValues[key])) {
        if (data['CardIcdePathos'][0] === undefined) {
          data['CardIcdePathos'] = [{}];
        }
        data['CardIcdePathos'][0][key] = formFieldValues[key];
      }
    }
  });

  tumorKeys?.forEach((key) => {
    if (!isEmptyValues(formFieldValues[key])) {
      if (data['CardIcdePathos'][0] === undefined) {
        data['CardIcdePathos'] = [{}];
      }
      data['CardIcdePathos'][0][key] = formFieldValues[key];
    }
  });
};

// 门急诊 表格
export const otpsDiagnosisTableRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  let otpsTableData = (formFieldValues?.['otps-diagnosis-table'] || [])
    ?.slice()
    ?.filter((item) => filterIcdeCodeEmpty(item));

  if (otpsTableData?.length === 0) {
    data['CardIcdeOtps'] = [];
  }

  if (otpsTableData?.length > 0) {
    data['CardIcdeOtps'] = otpsTableData
      .map((item, index) => {
        item['IcdeSort'] = index;
        return item;
      })
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['IcdeCode']);
        } else {
          return true;
        }
      })
      ?.map((item) => {
        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        return item;
      });
  }
};
export const otpsDiagnosisTableResponseProcessor = (
  formFieldValue: any,
  icdeItems: IcdeResp,
) => {
  // 门急诊 表格
  let otpsTableData = [];
  for (let item of icdeItems?.IcdeOtps) {
    item['id'] = item['IcdeId'] ?? item['Id'];

    otpsTableData.push(item);
  }
  formFieldValue['otps-diagnosis-table'] = otpsTableData?.sort(
    (a, b) => (a?.IcdeSort ?? 0) - (b?.IcdeSort ?? 0),
  );

  formFieldValue['otps-diagnosis-table'] = icdeOperationTableNoDataAddOne(
    formFieldValue['otps-diagnosis-table'],
  );

  formFieldValue['otpsDiagnosisTable'] = cloneDeep(
    formFieldValue['otps-diagnosis-table']?.slice(),
  );
};

// 入院诊断表格
export const admsDiagnosisTableRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  let admsTableData = (formFieldValues?.['adms-diagnosis-table'] || [])
    ?.slice()
    ?.filter((item) => filterIcdeCodeEmpty(item));

  if (admsTableData?.length === 0) {
    data['CardIcdeAdms'] = [];
  }

  if (admsTableData?.length > 0) {
    data['CardIcdeAdms'] = admsTableData
      .map((item, index) => {
        item['IcdeSort'] = index;
        return item;
      })
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['IcdeCode']);
        } else {
          return true;
        }
      })
      ?.map((item) => {
        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        return item;
      });
  }
};
export const admsDiagnosisTableResponseProcessor = (
  formFieldValue: any,
  icdeItems: IcdeResp,
) => {
  // 门急诊 表格
  let admsTableData = [];
  for (let item of icdeItems?.IcdeAdms) {
    item['id'] = item['IcdeId'] ?? item['Id'];

    admsTableData.push(item);
  }
  formFieldValue['adms-diagnosis-table'] = admsTableData?.sort(
    (a, b) => (a?.IcdeSort ?? 0) - (b?.IcdeSort ?? 0),
  );

  formFieldValue['adms-diagnosis-table'] = icdeOperationTableNoDataAddOne(
    formFieldValue['adms-diagnosis-table'],
  );

  formFieldValue['admsDiagnosisTable'] = cloneDeep(
    formFieldValue['adms-diagnosis-table']?.slice(),
  );
};

export const icdeItemKeyValueProcessor = (formValues: any, prefix: string) => {
  let icdeItem = {};

  Object.keys(formValues).forEach((key) => {
    // 用于表示 是不是 诊断
    // 一般仅用于这种情况
    if (key.startsWith(prefix)) {
      let actualKey = key.replace(prefix, '');
      if (actualKey !== 'Item') {
        icdeItem[actualKey] = formValues[key];
      }
    }
  });

  return {
    ...(formValues?.[`${prefix}Item`] ?? {}),
    ...icdeItem,
  };
};

export const initializeIcdeItemKeysByPrefix = (
  formValues: any,
  prefix: string,
) => {
  Object.keys(new IcdeRespItem())?.forEach((key) => {
    formValues[`${prefix}${key}`] = '';
  });
};
