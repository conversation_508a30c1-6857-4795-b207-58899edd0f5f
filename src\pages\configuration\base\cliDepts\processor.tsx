import { cliDeptTypes } from '@/pages/configuration/base/cliDepts/constants';
import dayjs from 'dayjs';

export const cliDeptsAddEditRequestProcessor = (values: any) => {
  cliDeptTypes.forEach((item) => {
    values[item?.value] = values?.CliDeptTypes?.includes(item?.value);
  });

  return {
    ...values,
  };
};

export const cliDeptsTypesProcessor = (record) => {
  let types = [];
  Object.keys(record).forEach((key) => {
    if (key !== 'IsValid' && key.indexOf('Is') === 0 && record[key]) {
      types.push(key);
    }
  });

  return types.length ? types : ['null'];
};
