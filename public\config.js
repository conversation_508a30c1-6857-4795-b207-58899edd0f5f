const ExternalConfig = {
  // 搜索配置
  searchConfig: {
    // 主要修改的是首页质控菜单下-科室-多选时输入可保留data
    autoClearSearchValue: false,
  },

  dynamic: {
    componentAlias: {
      DmrExamineReviewer: './pages/review/reviewer/index',
      DmrExamineReviewee: './pages/review/auditee/index',
      DmrExamineManagement: './pages/review/management/index',
      DmrExamineAnalysis: './pages/review/analysis/index',
    },
    headerAlias: {
      reviewDataAnalysis: ['DmrExamineAnalysis'],
    },
  },
  common: {
    externalLogo: '',
    // login / 页面左上角 title
    title: '智慧医保DRG运营平台',
    leftNewTab: false,
    headerNewTab: false,
    chromeDownload: true,
    dataToCharts: true,

    parameterDateMock: false,

    customDefaultSearchParam: {
      // dateType: 'month',
      dateRange: ['2020-01-01', '2024-03-31'],
      // hospCodes: ['0', '2'],
      // hospCode: '0',
    },

    // 知识库显隐
    wikiShow: true,
    wikiInsurShow: true,
    // 站内私信显隐
    personalMessageShow: false,
    // 显示“发送到”按钮
    showSendToBtn: true,

    headerSearchForceModified: false,

    clearTokenOnClose: false,

    hospitalName: '',

    paginationConfig: {
      pageSizeOptions: [10, 50, 100],
      defaultPageSize: 10,
    },

    customShortcuts: {},

    paramCustomizeEnable: false, // 这个默认是false 除了修水是true

    defaultLandingPage: '/qualityControl/main/details/type',

    tableStripeConfig: {
      oddColor: '#f2f2f2',
      evenColor: '#ffffff',
    },

    minChromeVersion: false,

    enableDynamicMenuItem: true,

    extraReportAppCodes: [
      {
        appCode: 'HospReportHighlight',
        routePrefix: '/statsAnalysis/report',
        parentRoute: '/statsAnalysis',
      },
      {
        appCode: 'HighlightChsDrgReport',
        routePrefix: '/chs/analysis',
        parentRoute: '/chs/analysis',
      },
      {
        appCode: 'HighlightDipReport',
        routePrefix: '/chs/dip',
        parentRoute: '/chs/dip',
      },
      {
        appCode: 'HighlightInsurReport',
        routePrefix: '/chs/main',
        parentRoute: '/chs/main',
      },
      {
        appCode: 'HighlightQualityExamineReport',
        routePrefix: '/dmr/examine',
        parentRoute: '/dmr/examine',
      },
    ],
  },
  // 首页相关
  dmr: {
    // 是否更新首页布局|生产环境请严格设定为false ```(当且仅当首次部署设定为true，并于首页打开一次之后修改为false)```
    updateLayout: false,
    // 是否为假的病案调阅（生产环境严格为false）
    fakeMedicalRecord: false,
    // 不修改可保存
    noEditSave: true,
    // 保存时是否顺便质控
    checkOnSave: false,
    // 针对 病案签收 https://devops.aliyun.com/projex/req/DMRDEV-1165# 《登记界面增加按钮支持病案签收》不填默认 false
    signinOnSave: true,
    // input聚焦时是否选中全部|false: input聚焦时选中全部；true: input聚焦时不会选中全部
    inputFocusNotSelectAll: false,
    // 表格新增按钮配置|true：点击表格最后一行之后“新增”文字才会新增一行； false：点击新增整行即可新增一行数据
    tableOnlyAddIconTrigger: true,
    // 左右键切换格子
    leftRightKeySwitch: false,
    // 时间输入框配置，true：按下enter即跳到下一格输入框
    timescapeEnterSwitch: false,
    // 手术表格 复制当前行 需要复制的key|```['OperGroupNo''OprnOprtBegntime','OprnConTime','Operator','Firstasst','Secondasst','AnaType','AnstLvCode','AnaDoc','OprnPatnType','OprnOprtEndtime','OperNote','AnstBegntime','AnstEndtime','OperDept','WoundHealingRateClass',]``` 默认以上字段会复制，修改时请基于以上值追加/去除
    operationCopyKeys: [],
    // 诊断 基本同上
    icdeCopyKeys: [],
    // 是否需要备份(目前还没开发完)
    needDraft: false,
    // 诊断手术表格 是否开启批量选择删除
    icdeOperRowSelection: true,
    // 点击全屏时自动显示医生端首页
    isFullScreenOpenDoctorEmr: true,
    // 点击全屏时自动显示医生端表格
    isFullScreenOpenEmrExtraTable: true,

    dmrSearchParamLabels: {
      OutDate: '',
    },
    dmrSearchRecycleShow: true,

    dmrPageUpDownSwitchModule: false,

    menuViewTop: true,

    tableItemFocusTop: true,

    centerInputActivate: false,

    forceScrollToCenterDistance: 50,

    icdeOperFirstMain: false,

    tableAutoAddNewLine: true,

    icdeCopyFocusKey: '',
    operCopyFocusKey: '',

    readonlyInputClickSelectAll: false,

    inEditRemainScrollPosition: false,

    operDeleteConfirm: false,
    icdeDeleteConfirm: false,

    thirdPartyCheck: false,
    publicModeHideKeys: [],
    dmrRegistrationReadOnly: false,

    forceDmrRightReview: false,
    saveAuditNoErrorAutoNext: false,

    doubleClickPopUp: false,
    doubleClickPopUpFalseKeys: [],

    tableSelectorDropdownHeight: 300,

    // 绍兴定制
    requiredLabelHighlight: {
      color: '#eb5757',
      fontWeight: 'bold',
    },
    tableHighlight: {
      outline: '2px solid #eb5757',
      borderRadius: 4,
    },

    enableBackspaceDelete: false,
    // 表格内 手术诊断下拉框 展示的内容类似一个表格（新版），false则展示List
    enableTableDropdownNG: true,
    // 诊断手术等表格内select 1~9 快捷选择
    enableKeyboardFriendlySelect: false,

    keyboardFriendlyDefaultSelectFirst: true,

    keyboardFlipKeys: {
      prev: [33],
      next: [34],
    },

    preCheckContainerPosition: 'right',

    enableDoubleDeckDoctorEmr: true,

    keyboardFriendlyEnterSelectFirst: false,

    preCardExamineEnable: true,
    preCardExamineType: 'comment',

    operationComboInput: false,
    operationComboDefaultMap: {
      WoundHealingRateClass: '11',
    },
    extraOperationComboRules: ['HasComboItem', 'FirstItem'],

    babyWithExtra: false,

    departmentTransferWard: true,
    examineSummaryShow: true,
  },

  ip: '************', // ************:20012
  port: '5181',

  domain: {},

  chs: {
    updateLayout: true,
    numberSelectItem: true,
    // 是否在表格中展示首页诊断&手术 没有该字段则默认是false
    srcIcdeOperShow: false,
    // 是否显示 第三方质控 这个按钮
    showThirdPartyCheckBtn: false,
  },

  statsAnalysis: {
    version: 3,

    tagBaseStyle: {
      fontSize: 13,
      padding: '2px 7px',
      // fontSize: 16,
      // padding: '4px 12px',
    },
    drawChart: true,

    cascaderDictionaryName: true,

    // 组合查询 输入括号的地方能不能使用数字快捷输入
    bracketInputUseNumber: true,
    // 开启 当且仅当选择了时间即可查询
    expressionEmptySearch: false,
  },

  qualityControl: {
    // 是否使用新版（tree-table形式）的质控审核规则配置界面 不填默认为false
    useNewRuleSetting: true,
    codeValueQuantification: {
      isOpenDateTrend: true, // 是否开启日期趋势
      excludeDrgs: true,
      DrgsName: '绩效DRGs',
    },
  },

  EnergyEfficiencyManagement: {
    // 根据标准时长进行计算方块的长度
    Standard_Duration: 8,
  },

  operationalDataManagement: {
    // 编辑情况下 编辑完成一行时 换行默认焦点 需要跳过的key（一般用于跳过初始化已经生成的字段）
    skipFocusKeys: ['ApprovedBedsNumber', 'SuppliedBedsNumber', 'StayInCnt'],

    // 是否需要自动计算公式 要用的话会根据 下面的formulas 进行判断是通过后端接口计算 还是前端计算
    needAutoFormula: false,

    // 算法 任意值都可以使用自动生成算法 请确保填的字段key是正确的 包括大小写（按照columnDef返回的就不会错） 否则结果可能不符合预期
    formulas: {
      // 住院动态登记（全部）
      in: ['StayingCnt=BackendValidation'],
      // 门急动态登记（全部）
      out: [],
      // 医生门急动态登记（全部）
      outDoctor: [],
      // 观察动态登记（全部）
      obs: [],
    },

    // useV2
    useV2Page: true, // 是否使用v2页面 true的话会把能替换的都替换了（仅针对动态页面）[目前作用： 住院动态床位数管理 + 住院动态校对]
  },

  reportSys: {
    extraAppCodes: ['PublicReport', 'HospReportHighlight'],
  },

  his: {
    useV2: false, // 要不要使用v2接口 true的话会把能替换的都替换了 （仅针对医保页面）

    // stats part 要不要展示组数 & 入组率
    statsShowGrpCnt: true,

    // 费用分析 用于chart的统计 那块特殊需要隐藏的数据
    // 统计类别字典：StatsChargeType，项目类别字典：MedChargeType
    feeCharge: {
      statsItemsHide: [],
      medItemsHide: ['02', '13'], // eg：项目类别内的挂号费，诊察费: ['02', '13']
    },

    // drg 支付明细查询 专用
    drgPayDetail: {
      showStatus: false, // 是否显示病例图标icon
    },

    // 测试时方便debug时用的一些操作/按钮/页面blahblah
    devtool: {
      refresh: true,
      editColumn: true,
    },
  },

  // 默认是false 走 GetExternalCalcRecord 为true时走 ExternalCalcWithHisId 【false直接获取，true后端处理完再吐】
  external: {
    emrWithHisId: false,
    dmrWithHisId: false,

    rightContainerHide: [], // 可选参数：医保预分组，质控审核 【目前已知：上海儿童 需要隐藏医保预分组】
  },
};
