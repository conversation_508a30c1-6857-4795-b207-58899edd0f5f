export const tableToFormKeyAndTableIdMapping = {
  IcdeDragTable: {
    'data.key': 'diagnosisTable',
    'data.props.id': 'diagnosis-table-content',
    'data.props.parentId': 'diagnosisTable',
    'data.props.tableId': 'diagnosis-table-content',
  },
  OperationDragTable: {
    'data.key': 'operationTable',
    'data.props.id': 'operation-table-content',
    'data.props.parentId': 'operationTable',
    'data.props.tableId': 'operation-table-content',
  },
  IcuDragTable: {
    'data.key': 'icuTable',
    'data.props.id': 'icu-table-content',
    'data.props.parentId': 'icuTable',
    'data.props.tableId': 'icu-table-content',
  },
  PathologyIcdeDragTable: {
    'data.key': 'pathologicalDiagnosisTable',
    'data.props.id': 'pathological-diagnosis-table-content',
    'data.props.parentId': 'pathologicalDiagnosisTable',
    'data.props.tableId': 'pathological-diagnosis-table-content',
  },
  AdmsIcdeDragTable: {
    'data.key': 'admsDiagnosisTable',
    'data.props.id': 'adms-diagnosis-table-content',
    'data.props.parentId': 'admsDiagnosisTable',
    'data.props.tableId': 'adms-diagnosis-table-content',
  },
  OtpsIcdeDragTable: {
    'data.key': 'otpsDiagnosisTable',
    'data.props.id': 'otps-diagnosis-table-content',
    'data.props.parentId': 'otpsDiagnosisTable',
    'data.props.tableId': 'otps-diagnosis-table-content',
  },
  TcmIcdeDragTable: {
    'data.key': 'tcmDiagnosisTable',
    'data.props.id': 'tcm-diagnosis-table-content',
    'data.props.parentId': 'tcmDiagnosisTable',
    'data.props.tableId': 'tcm-diagnosis-table-content',
  },
};
