# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
**/node_modules/  
/npm-debug.log*
/yarn-error.log
/yarn.lock
/package-lock.json

# production
/dist

# misc
.DS_Store

# umi
**/.umi*
**/.umi-production
**/.umi-test
/.env.local
package-lock.json
pnpm-lock.yaml

/.umirc.local.ts
/config/config.local.ts

.idea

dist/


*.zip

env/

*.diff

/.roo

*.bak
