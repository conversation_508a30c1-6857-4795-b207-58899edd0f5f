class Constants {
  static title = '智慧病案一体化平台';

  static copyRight = 'Copyright © ';

  static companyName = '上海联众网络信息股份有限公司';

  static FAST_DATE_SELECT_CONSTANTS = {
    YEAR: 'YEAR',
    QUARTER: 'QUARTER',
    MONTH: 'MONTH',
    DAY: 'DAY',
  };

  static DateFormatType = {
    // 小写的表示后端定义的基本类型
    double: 'double',
    int32: 'int32',
    string: 'string',

    DateTime: 'DateTime',
    Date: 'Date',
    Month: 'Month',
    Decimal: 'Decimal',
    Currency: 'Currency',
    Percent: 'Percent',
    Permillage: 'Permillage',
    String: 'String',
    CurrencyWithoutSuffix: 'CurrencyWithoutSuffix',
  };

  static SegmentInputLabel = {
    INFINITY_LABEL: '最大',
  };

  static modules = [
    'Coder',
    'CliDepts',
    'Hospital',
    'Role',
    'RoleWithDesc',
    'DeptType',
    'DateGranularity',
    'ArgValuePickMode',
    'ReportDataSize',
    'TcmIcdeCategory',
    'UdfType',
    'ReportDeptType',
    'DayType',
    'Warehouse',
    'InventoryLocation',
    'SdCode',
    'VersionedSdCode',
    'OperRate',
    'WorkFlowStatus',
    'MajorPerfDepts',
    'UdfTriggers',
    'DynDepts',
    'OtpDepts',
    'DynWards',
    'ObsDepts',
    'TraceStatus',
    'DateSplitMode',
    'Employee',
    'DynDynamicRegistrationStatus',
    'AnaGroupOption',
    'Wards',
    'MsgLevel',
    'MsgType',
    'InsurType',
    'HisMedChrgitmType',
    'WarningLevel',
    'StatsChargeType',
    'MedChargeType',
    'VersionedDrgCode',
    'ScheduleMode',
    'ReviewMode',
    'RevieweeType',
    // 'HospHierarchyAll' // 学科 + 科室
    'Director',
    'Chief',
    'Attending',
    'Chfpdr',
    'RuleApplyMode',
    'QualityScoreLevel',
    'DoctorType',
    'RwRange',
    'SurgeonType',
    'QualityMonitorLevel',
    'QualityExamineStatus',
    'QualityMonitorLevel',
    'InsurType',
    'CenterSettleAppealCategory',
    'CenterSettleAppealCaty',
    'RefundFlag',
    'SignInOperator',
    'DynHospHierarchyType',
    'CliDeptAndCaty', // 特定页面使用
    'QualityCheckCategory',
    'CenterSettleAppealChannel',
    'DmrSignOutOperator',
    'CheckErrorType',
    'CheckErrorLevel',
  ];

  static mrModules = [
    'CliDepts',
    'User',
    'Hierarchy',
    'BorrowPurpose',
    'Employee',
    'WorkFlowStatus',
    'BorrowerOrg',
    'SignInOperator',
    'ApplicantRealtion',
    'ApplicantIdCardType',
    'PrintReason',
    'PrintDocType',
  ];

  static dmrModules = [
    'GJ',
    'Prov',
    'City',
    'Coty',
    'Subd',
    'MZ',
    'GX',
    'LYFS',
    'Coder',
    'YLFKFS',
    'XB',
    'PatIdKind',
    'Job',
    'HY',
    'RYTJ',
    'PatFrom',
    'InCond',
    'RYBQ',
    'SJ',
    'XX',
    'Rh',
    'RecordQuality',
    'MZFS',
    'QKYHLB',
    'SSJB',
    'OperType',
    'IcdeOutcome',
    'Allergy',
    'ReturnPlan',
    'AllergyDrugs',
    'AnstLv',
    'OprnPatnType',
    'CliDepts',
    'IcuCategory',
    'IcuType',
    'IsBloods',
    'NwbAdmType',
    'BloodReaction',
    'IsDaySurgery',
    'TumorDiagEvid',
    'BkupDeg',
    'TumorStaging_T',
    'TumorStaging_M',
    'TumorStaging_N',
    'TumorStagingType',
    'TumorTrtCond',
    'TumorInitialTrtItem',
    'TumorLateralPosition',
    'TumorMultPrimary',
    'AbnFeeType',

    'IsInfects',
    'IsOperComplication',
    'OperAccord',
    'IsFirstCase',
    'IsTeachingCase',
    'IsProblemCase',
    'IsFollowUp',
    'FollowUpDesc',

    'TumorDiagEvid', // 温附二 定制 Module 无所谓 可以 +
    'MedTeams',

    'DiagAccord',
    'TechType',

    // 新生儿
    'IntegrityOfPlacenta',
    'UmbilicalCord',
    'PatBroken',
    'BabyResuMeasure',
    'OutcomeOfDelivery',
    'AmniFluid',
    'ROAMMethod',
    'BirthNumStr',
    'BabyKind',
    'DelayBore',
    'BabyOutType',
    'BabyCardStorage',
    'FetalPosition',

    'BabyAmniFluid',
    'PathoType',
  ];
  static insurModules = [
    'XB',
    'TrtType',
    'RYTJ',
    'MZ',
    'PatIdKind',
    'Job',
    'GX',
    'InsurType',
    'SpPsnType',
    'NwbAdmType',
    'RYBQ',
    'XX',
    'Rh',
    'HiPaymtd',
    'Caty',
    'IptMedType',
    'MZFS',
    'QKYHLB',
    'IcuType',
    'BldCat',
    'Allergy',
    'ReturnPlan',
    'AllergyDrugs',
    'MedChrgitm',
    'LYFS',
    'IcdeOutcome',
    'SSJB',
    'IcuCategory',
    'ADrgType',
    'GroupPaymentType',
    'GroupResultType',
    'Hospital',
    // 'MedTeams',
    // 'VersionedDrgCode',
  ];
}

export default Constants;
