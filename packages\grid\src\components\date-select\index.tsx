import React, { useEffect, useState } from 'react';
import './index.less';
import { DatePicker, DatePickerProps, Form, Input } from 'antd';
import Datepicker from '@uni/components/src/picker/datepicker';
import dayjs, { OpUnitType, QUnitType } from 'dayjs';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { getDeletePressEventKey } from '../../utils';
import { TimeScape } from '@uni/components/src/date-mask/timescape';
import GridItemContext from '@uni/commons/src/grid-context';
import { isEmptyValues } from '@uni/utils/src/utils';

interface DateSelectCalculateItem {
  needCalculateFormKey?: string;
  calculateFromFormKeys?: string[];
  dateCalculateUnits?: QUnitType | OpUnitType;
}

export interface DateSelectProps {
  className?: string;
  formItemId?: string;
  type: 'datePicker' | 'separate' | 'compact';
  bordered?: boolean;
  form?: any;
  formKey: string;

  dataTableIndex?: number;

  datePicker?: boolean;
  showHours?: boolean;

  showMinutes?: boolean;

  showSeconds?: boolean;

  getPopupContainer?: (HTMLElement) => HTMLElement;

  value?: string;

  onChange?: (value: any) => void;

  //这个仅用于 实际住院时间 以及 年龄 的计算
  calculateItems?: DateSelectCalculateItem[];
  calculateRelatedField?: boolean;

  inputIdExtra?: string;
}

interface DateItem {
  key: string;
  label: string;
  class: string;
  value?: (date: string) => number;
}

const dateItems: DateItem[] = [
  {
    key: 'year',
    value: (date) => {
      return dayjs(date).year();
    },
    label: '年',
    class: 'year',
  },
  {
    key: 'month',
    value: (date) => {
      return dayjs(date).month() + 1;
    },
    label: '月',
    class: 'month',
  },
  {
    key: 'day',
    value: (date) => {
      return dayjs(date).date();
    },
    label: '日',
    class: 'day',
  },
];

const hourItem: DateItem = {
  key: 'hour',
  value: (date) => {
    return dayjs(date).hour();
  },
  label: '时',
  class: 'hour',
};
const minuteItem: DateItem = {
  key: 'minute',
  value: (date) => {
    return dayjs(date).minute();
  },
  label: '分',
  class: 'minute',
};
const secondItem: DateItem = {
  key: 'second',
  value: (date) => {
    return dayjs(date).second();
  },
  label: '秒',
  class: 'second',
};

const DateSelect = (props: DateSelectProps) => {
  const [selectedDate, setSelectedDate] = useState('');

  // input date
  // const [inputDate, setInputDate] = useState('');

  let format = 'YYYY-MM-DD';
  let items = [...dateItems];
  if (props?.showSeconds) {
    items.push(hourItem);
    items.push(minuteItem);
    items.push(secondItem);
    format += ' HH:mm:ss';
  } else if (props?.showMinutes && !props?.showSeconds) {
    items.push(hourItem);
    items.push(minuteItem);
    format += ' HH:mm:00';
  } else if (props?.showHours && !props?.showMinutes && !props?.showSeconds) {
    items.push(hourItem);
    format += ' HH:00:00';
  }

  useEffect(() => {
    setSelectedDate(props?.value);
    // setInputDate(props?.value ? dayjs(props?.value).format(format) : '');
  }, [props?.value]);

  useEffect(() => {
    // setInputDate(props?.value ? dayjs(props?.value).format(format) : '');
  }, [props?.value, format]);

  useEffect(() => {
    Emitter.on(getDeletePressEventKey(props?.formKey), (key) => {
      if (key.includes(props?.formKey)) {
        // 表示就是当前组件的formKey
        onChange(null, '');
        // setInputDate('');
      }
    });

    return () => {
      Emitter.off(getDeletePressEventKey(props?.formKey));
    };
  }, []);

  const onChange = (date, dateString) => {
    console.error('date-select onChange', date, dateString);
    setSelectedDate(dateString);

    props?.onChange && props?.onChange(dateString);

    if (props?.calculateItems?.length > 0) {
      props?.calculateItems?.forEach((calculateItem) => {
        if (calculateItem?.needCalculateFormKey) {
          if (calculateItem?.calculateFromFormKeys?.length === 2) {
            let startDate = props?.form?.getFieldValue(
              calculateItem?.calculateFromFormKeys?.at(0),
            );
            let endDate = props?.form?.getFieldValue(
              calculateItem?.calculateFromFormKeys?.at(1),
            );

            if (startDate && endDate) {
              let unit = calculateItem?.dateCalculateUnits ?? 'day';
              let period = dayjs(endDate).diff(dayjs(startDate), unit);

              // 实际住院天数的话 单独计算
              if (calculateItem?.needCalculateFormKey === 'InPeriod') {
                period = dayjs(endDate)
                  .startOf('day')
                  .diff(dayjs(startDate).startOf('day'), unit);

                if (period === 0) {
                  period = 1;
                }
              }

              const ageAio = document.querySelector('input[id*=PatAgeYMD]');
              if (
                calculateItem?.needCalculateFormKey === 'PatAge' &&
                !isEmptyValues(ageAio)
              ) {
                // 重新计算 PatAge NwbAge NwbAgeDay NwbAgeMonth
                let year = dayjs(endDate).diff(dayjs(startDate), 'year');
                let day = dayjs(endDate).diff(dayjs(startDate), 'day');

                if (year < 0 || day < 0) {
                  props?.form.setFieldValue('PatAge', null);
                  props?.form.setFieldValue('NwbAge', null);
                  props?.form.setFieldValue('NwbAgeMonth', null);
                  props?.form.setFieldValue('NwbAgeDay', null);
                  return;
                }

                if (year > 0) {
                  props?.form.setFieldValue('PatAge', year);
                  props?.form.setFieldValue('NwbAge', null);
                  props?.form.setFieldValue('NwbAgeMonth', null);
                  props?.form.setFieldValue('NwbAgeDay', null);
                } else {
                  props?.form.setFieldValue('PatAge', 0);
                  props?.form.setFieldValue('NwbAge', day);
                  props?.form.setFieldValue(
                    'NwbAgeMonth',
                    Math.floor(day / 30),
                  );
                  props?.form.setFieldValue('NwbAgeDay', day % 30);
                }
              } else {
                props?.form?.setFieldValue(
                  calculateItem?.needCalculateFormKey,
                  period < 0 ? undefined : period,
                );
              }
            }
          }
        }
      });
    }
  };

  const renderDatePicker = () => {
    return (
      <Datepicker
        id={props?.formItemId || `${props.formKey}#datePicker`}
        className={'date-picker-container'}
        picker={'date'}
        allowClear={false}
        bordered={false}
        value={dayjs(props?.value)}
        showTime={props?.showHours || props?.showMinutes || props?.showSeconds}
        onFocus={(event) => {
          event.target.click();
        }}
        getPopupContainer={(trigger) =>
          (props?.getPopupContainer && props?.getPopupContainer(trigger)) ||
          document.body
        }
        format={format}
        onChange={onChange}
      />
    );
  };

  const renderSeparateInput = () => {
    return (
      <div className={`date-items-container`}>
        {items.map((item) => {
          return (
            <div className={'flex-row-center'}>
              {props?.datePicker ? (
                <span className={`date-item-container ${item.class}`}>
                  {selectedDate ? item.value(selectedDate) : ''}
                </span>
              ) : (
                <Input
                  id={props?.formItemId || `${props.formKey}#${item.key}`}
                  bordered={props?.bordered || false}
                  className={`date-item-container ${item.class}`}
                />
              )}
              <span>{item.label}</span>
            </div>
          );
        })}
      </div>
    );
  };

  const renderCompactInput = () => {
    let value = props?.value ? dayjs(props?.value).format(format) : '';

    // return (
    //   <DateMaskInput
    //     id={props?.formItemId || `formItem#${props.formKey}#CompactInput`}
    //     mask={format}
    //     value={value}
    //     className={`compact-date-container`}
    //     onValueChange={(dateString) => {
    //       console.log('renderCompactInput', dateString);
    //       // setInputDate(event);
    //       let currentDate = dayjs(dateString, format, true);
    //       if (currentDate.isValid()) {
    //         let formatDate = currentDate.format(format);
    //         if (formatDate !== value) {
    //           // setInputDate(formatDate);
    //           onChange(currentDate, formatDate);
    //         }
    //       }
    //     }}
    //   />
    // );

    return (
      <TimeScape
        {...props}
        container={'DMR'}
        value={value}
        format={format}
        inputIdExtra={props?.inputIdExtra ?? ''}
        formKey={
          props?.dataTableIndex !== undefined
            ? `${props?.formKey}#${props?.dataTableIndex}`
            : props?.formKey
        }
        dateSeparator={'.'}
        onValueChange={(dateString) => {
          console.log('renderCompactInput', dateString);
          // setInputDate(event);
          let currentDate = dayjs(dateString, format, true);
          if (currentDate.isValid()) {
            let formatDate = currentDate.format(format);
            if (formatDate !== value) {
              // setInputDate(formatDate);
              onChange(currentDate, formatDate);
            }
          }
        }}
      />
    );
  };

  return (
    <div className={`date-select-container ${props?.className || ''}`}>
      {props?.type === 'datePicker' && renderDatePicker()}

      {props.type === 'separate' && renderSeparateInput()}

      {props.type === 'compact' && renderCompactInput()}
    </div>
  );
};

export default DateSelect;
