// 保证 item都被填写
import { isEmptyValues } from '@uni/utils/src/utils';

const tableIdToInputItem = {
  InWard: (index, suffix) => {
    return document.querySelector(
      `.department-transfer-modal-container input[id='formItem#InWard#${index}#Input#departmentTransferTable']`,
    );
  },
  OutWard: (index, suffix) => {
    return document.querySelector(
      `.department-transfer-modal-container input[id='formItem#OutWard#${index}#Input#departmentTransferTable']`,
    );
  },
  InCliDept: (index, suffix) => {
    return document.querySelector(
      `.department-transfer-modal-container input[id='formItem#InCliDept#${index}#Input#departmentTransferTable']`,
    );
  },
  OutCliDept: (index, suffix) => {
    return document.querySelector(
      `.department-transfer-modal-container input[id='formItem#OutCliDept#${index}#Input#departmentTransferTable']`,
    );
  },

  TransferInDate: (index, suffix) => {
    return document
      .querySelector(
        `.department-transfer-modal-container input[id='formItem#TransferInDate#${index}#Years#TimeScape${suffix}']`,
      )
      .closest('.date-select-container');
  },
  TransferOutDate: (index, suffix) => {
    return document
      .querySelector(
        `.department-transfer-modal-container input[id='formItem#TransferOutDate#${index}#Years#TimeScape${suffix}']`,
      )
      .closest('.date-select-container');
  },

  InDeptHours: (index, suffix) => {
    return document.querySelector(
      `.department-transfer-modal-container input[id='formItem#InDeptHours#${index}#departmentTransferTable${suffix}']`,
    );
  },
};

export const cliDeptDepartmentTransferValidator = (cliDeptData: any[]) => {
  let valid = true;
  let invalidQueryKeys = [];
  let tableItems = document.querySelectorAll(
    '#departmentTransferTable_CliDept thead th:not(#th-TransferSort):not(:last-child)',
  );
  let tableItemIds = Array.from(tableItems)?.map((item) => {
    return item?.id?.replace('th-', '');
  });

  cliDeptData?.forEach((dataItem, dataIndex) => {
    // 保证每一行的 显示在 table上的 item 都填了东西
    tableItemIds?.forEach((requiredKey) => {
      if (isEmptyValues(dataItem?.[requiredKey])) {
        valid = false;
        invalidQueryKeys.push(
          tableIdToInputItem?.[requiredKey]?.(dataIndex, ''),
        );
      }
    });
  });

  return {
    valid: valid,
    invalidQueryKeys: invalidQueryKeys,
  };
};

export const wardDepartmentTransferValidator = (wardData: any[]) => {
  let valid = true;
  let invalidQueryKeys = [];
  let tableItems = document.querySelectorAll(
    '#departmentTransferTable_Ward thead th:not(#th-TransferSort):not(:last-child)',
  );
  let tableItemIds = Array.from(tableItems)?.map((item) => {
    return item?.id?.replace('th-', '');
  });

  wardData?.forEach((dataItem, dataIndex) => {
    // 保证每一行的 显示在 table上的 item 都填了东西
    tableItemIds?.forEach((requiredKey) => {
      if (isEmptyValues(dataItem?.[requiredKey])) {
        valid = false;
        invalidQueryKeys.push(
          tableIdToInputItem?.[requiredKey]?.(dataIndex, '#Ward'),
        );
      }
    });
  });

  return {
    valid: valid,
    invalidQueryKeys: invalidQueryKeys,
  };
};
