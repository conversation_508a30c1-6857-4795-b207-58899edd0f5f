import DateRangeWithType from '@uni/components/src/date-range-with-type';

export class OptsProps {
  Sdate;
  Edate;
  SearchKeyWord;
  dateRange;
  HospCodes;
  HospCode;
  CliDepts;
  Coder;
  ErrorLevel;
  DateFormatType;
  DateType;
  hospCode;
  hospCodes;
  UseCodeTimeFilter;

  OutType;
}
const externalSearchConfig = (window as any).externalConfig?.['searchConfig'];
const autoClearSearchValue = externalSearchConfig?.['autoClearSearchValue'];

export const SearchItemsOpts = (
  {
    dateRange,
    HospCodes,
    DateFormatType,
    DateType,
    HospCode,
    CliDepts,
    Coder,
    hospCode,
    hospCodes,
  }: OptsProps,
  { hospOpts, deptOpts, coderOpts },
) => [
  {
    dataType: 'text',
    title: '病案标识',
    placeholder: '病案号/姓名/住院号/条形码',
    name: 'SearchKeyWord',
    colProps: { span: 8 },
  },
  {
    title: '日期',
    dataType: 'DateRange.Group',
    radioOpts: ['正常', '登记'],
    name: 'DateRange',
    initialValue: dateRange,
    render: (formRef: any) => {
      return (
        <div
          style={{ padding: '0px 8px', display: 'flex' }}
          className={'ant-form-item-label flex-row-center'}
        >
          <label>时间</label>
          <DateRangeWithType
            useContextFormInstance={true}
            needFormWrapper={false}
            enableDateFormatTypeSelector={true}
            initializeDefaultOption={false}
            initialValues={{
              dateRange: dateRange,
              DateFormatType: DateFormatType ?? 'date',
              DateType: DateType ?? 'OutDate',
            }}
          />
        </div>
      );
    },
  },
  {
    title: '院区',
    dataType: 'select',
    name: 'HospCode',
    initialValue: HospCodes ?? HospCode ?? hospCodes ?? hospCode,
    opts: hospOpts,
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
    },
    colProps: { span: 5 },
  },
  {
    title: '科室',
    dataType: 'select',
    name: 'CliDepts',
    initialValue: CliDepts,
    opts: deptOpts,
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
      autoClearSearchValue: autoClearSearchValue,
    },
    colProps: { span: 8 },
  },
  {
    title: '编码员',
    dataType: 'select',
    name: 'Coder',
    opts: coderOpts,
    initialValue: Coder,
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
    },
    colProps: { span: 5 },
  },
];
