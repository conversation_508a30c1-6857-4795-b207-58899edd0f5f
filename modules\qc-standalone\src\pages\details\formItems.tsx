import {
  ProForm,
  ProFormDateRangePicker,
  ProFormDependency,
  ProFormRadio,
  ProFormSelect,
} from '@uni/components/src/pro-form';
import { Form } from 'antd';
import dayjs from 'dayjs';
import DateRangeWithType from '@uni/components/src/date-range-with-type';
import React from 'react';

export class OptsProps {
  Sdate;
  Edate;
  SearchKeyWord;

  // dateRange;
  HospCode;
  CliDepts;
  Coder;
  OutType;
  MonitorLevel;
  UseCodeTimeFilter;
  ErrorLevel;
}
const externalSearchConfig = (window as any).externalConfig?.['searchConfig'];
const autoClearSearchValue = externalSearchConfig?.['autoClearSearchValue'];

export const SearchItemsOpts = (
  { dateRange, HospCodes, HospCode, CliDepts, Coder, OutType }: OptsProps,
  { hospOpts, deptOpts, coderOpts, outTypeOpts },
  isAdmin?: boolean,
) => [
  {
    dataType: 'text',
    title: '病案标识',
    placeholder: '病案号/姓名/住院号/条形码',
    name: 'SearchKeyWord',
    colProps: { span: 7 },
    // fieldProps: {
    //   suffix: <Search />,
    // },
  },
  {
    title: '日期',
    dataType: 'DateRange.Group',
    radioOpts: ['正常', '登记'],
    name: 'DateRange',
    initialValue: dateRange,
    // transform: (values) => {
    //     console.log(values)
    //   return {
    //     Sdate: values
    //       ? dayjs(values[0]).startOf('M').format('YYYY-MM-DD')
    //       : undefined,
    //     Edate: values
    //       ? dayjs(values[1]).endOf('M').format('YYYY-MM-DD')
    //       : undefined,
    //   };
    // },
    // fieldProps: {
    //   picker: 'month',
    //   format: 'YYYY-MM',
    // },
    render: (formRef: any) => {
      return (
        <div
          style={{ padding: '0px 8px', display: 'flex' }}
          className={'ant-form-item-label flex-row-center'}
        >
          <label>时间：</label>
          <DateRangeWithType
            useContextFormInstance={true}
            needFormWrapper={false}
            enableDateFormatTypeSelector={true}
            initializeDefaultOption={true}
          />
        </div>
      );
    },
  },
  {
    title: '院区',
    dataType: 'select',
    name: 'HospCode',
    initialValue: HospCodes ?? HospCode,
    opts: hospOpts,
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 4,
    },
    colProps: { span: 6 },
  },
  {
    title: '科室',
    dataType: 'select',
    name: 'CliDepts',
    initialValue: CliDepts,
    opts: deptOpts,
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
      enableSelectAll: true,
      autoClearSearchValue: autoClearSearchValue,
    },
    colProps: { span: 6 },
  },
  {
    title: '编码员',
    hidden: isAdmin !== true,
    dataType: 'select',
    name: 'Coder',
    opts: coderOpts,
    initialValue: Coder,
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 4,
    },
    colProps: { span: 6 },
  },
  {
    title: '离院方式',
    dataType: 'select',
    name: 'OutType',
    opts: outTypeOpts,
    initialValue: OutType,
    fieldProps: {},
    colProps: { span: 6 },
  },
];
