import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import {
  UniDmrDragEditOnlyTable,
  UniDragEditTable,
  UniTable,
} from '@uni/components/src';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { icdeColumns, otpsIcdeColumns } from '@/pages/dmr/columns';
import { Form, Modal } from 'antd';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import { tableHotKeys } from '@uni/grid/src/common';
import { useHotkeys } from 'react-hotkeys-hook';
import { mergeColumnsInDmrTable } from '@/pages/dmr/utils';
import {
  calculateNextIndex,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
  triggerFormValueChangeEvent,
  waitFocusElementRefocus,
  waitFocusElementRefocusBySelector,
} from '@uni/grid/src/utils';
import cloneDeep from 'lodash/cloneDeep';
import { getLineArrowUpDownEventKey } from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import { resizeContentById } from '@uni/grid/src/core/custom-utils';
import EmrExtraTable from '@/pages/dmr/components/emr-extra-table';
import { isEmptyValues } from '@uni/utils/src/utils';

interface OptslogyIcdeDragTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];

  underConfiguration?: boolean;

  onChange?: (value: any) => void;

  emrDataTable?: boolean;
  emrTableWidth?: string | number;
  emrColumns?: any[];
}

interface IcdeOtpsItem {
  IcdeId?: number;
  id?: string | number;

  IcdeSort?: number;
  IcdeName?: string;
  IcdeCode?: string;
  PalgNo?: string;
}

const clearKeysMap = {
  // 仅用于联动删除使用
  IcdeCode: ['IcdeName', 'IcdeCode'],
};

const OtpsIcdeDragTable = (props: OptslogyIcdeDragTableProps) => {
  const itemRef = React.useRef<any>();

  const tableOperationRef = React.useRef(null);

  const [form] = Form.useForm();

  const emrContainerRef = useRef(null);

  const otpsIcdeDataSource =
    Form.useWatch('otpsDiagnosisTable', props?.form) ?? [];

  const [tableColumns, setTableColumns] = useState<any[]>([]);

  const [waitFocusId, setWaitFocusId] = useState<string>(undefined);

  const [tableDataSourceSize, setTableDataSourceSize] = useState<number>(
    otpsIcdeDataSource?.length,
  );

  useEffect(() => {
    setTableDataSourceSize(otpsIcdeDataSource?.length);
  }, [otpsIcdeDataSource]);

  React.useImperativeHandle(tableOperationRef, () => {
    return {
      onItemAdd: onItemAdd,
      onItemDelete: onItemDelete,
      onDeleteKeyPress: onDeleteKeyPress,
      onArrowUpDownPress: onArrowUpDownPress,
    };
  });

  const onItemAdd = (focusId?: string) => {
    let rowData = {
      id: Math.round(Date.now() / 1000),
      UniqueId: uuidv4(),
    };
    let tableData = props?.form?.getFieldValue('otps-diagnosis-table');

    tableData.splice(tableData.length, 0, rowData);
    props?.form?.setFieldValue('otps-diagnosis-table', cloneDeep(tableData));

    setWaitFocusId(
      `div[id=otpsDiagnosisTable] tbody > tr:nth-child(${tableData?.length}) > td input`,
    );
    setTableDataSourceSize(tableData?.length);
  };

  const onItemDelete = (index) => {
    if (index > -1) {
      let tableData = props?.form?.getFieldValue('otps-diagnosis-table');
      tableData.splice(index, 1);

      // 更新form
      props?.form?.setFieldValue('otps-diagnosis-table', cloneDeep(tableData));

      // 删除的时候 给出当前那个选中的
      // 当前选中的意思是表格中的上一个存在即是上表格中上一个的最后一个
      // 表格中不存在即写第0个的icdeName 建议写死
      let dataItems = tableData?.filter((item) => item?.id !== 'ADD');
      if (dataItems?.length > 0) {
        setWaitFocusId(
          `div[id=otpsDiagnosisTable] tbody > tr:nth-child(${
            index >= dataItems.length - 1 ? dataItems.length : index + 1
          }) > td input`,
        );
      }
      setTableDataSourceSize(tableData?.length);
    }
  };

  const onDeleteKeyPress = (itemId) => {
    // key 包含 index 和 其他的东西
    let itemIds = itemId?.split('#');
    let index = parseInt(itemIds?.at(2));
    let key = itemIds?.at(1);

    let clearKeys = [key];
    if (clearKeysMap[key]) {
      clearKeys = clearKeysMap[key];
    }
    clearValuesByKeys(clearKeys, index);
  };

  const onArrowUpDownPress = (payload) => {
    const otpsIcdeDataSource = props?.form?.getFieldValue(
      'otps-diagnosis-table',
    );
    let type = payload?.type;
    console.log('payload', payload);
    if (
      payload?.event?.target?.className?.indexOf('ant-select') > -1 &&
      payload?.trigger === 'hotkey'
    ) {
      // 表示是 下拉框 需要定制
      return;
    }

    payload?.event?.stopPropagation();

    let { nextIndex, activePaths } = calculateNextIndex(type);
    if (type === 'UP') {
      if (nextIndex < 0) {
        nextIndex = undefined;
      }
    }

    if (type === 'DOWN') {
      if (nextIndex > otpsIcdeDataSource?.length - 2) {
        nextIndex = undefined;
      }
    }

    if (nextIndex !== undefined) {
      activePaths[2] = nextIndex.toString();
      document.getElementById(activePaths?.join('#'))?.focus();
    }
  };

  const hotKeyToEvents = {
    ADD: (event) => {
      onItemAdd(event.target.id);
    },
    DELETE: (event) => {
      let id = event?.target?.id;
      let indexString = id?.split('#')?.at(2);

      let index = parseInt(indexString);

      if (index > -1) {
        onItemDelete(index);
      }
    },
    SCROLL_LEFT: (event) => {
      let tableBody = document.querySelector(
        "div[id='formItem#otpsDiagnosisTable'] div[class='ant-table-body']",
      );
      tableBody?.scrollBy({
        left: -100,
        behavior: 'smooth',
      });
    },
    SCROLL_RIGHT: (event) => {
      let tableBody = document.querySelector(
        "div[id='formItem#otpsDiagnosisTable'] div[class='ant-table-body']",
      );
      tableBody?.scrollBy({
        left: 100,
        behavior: 'smooth',
      });
    },
    SCROLL_UP: (event) => {
      let tableBody = document.querySelector(
        "div[id='formItem#otpsDiagnosisTable'] div[class='ant-table-body']",
      );
      tableBody?.scrollBy({
        top: -50,
        behavior: 'smooth',
      });
    },
    SCROLL_DOWN: (event) => {
      let tableBody = document.querySelector(
        "div[id='formItem#otpsDiagnosisTable'] div[class='ant-table-body']",
      );
      tableBody?.scrollBy({
        top: 50,
        behavior: 'smooth',
      });
    },

    UP: (event) => {
      onArrowUpDownPress({
        event: event,
        type: 'UP',
        trigger: 'hotkey',
      });
    },
    DOWN: (event) => {
      onArrowUpDownPress({
        event: event,
        type: 'DOWN',
        trigger: 'hotkey',
      });
    },
  };

  const hotKeyRefs = tableHotKeys?.map((item) => {
    return useHotkeys(item.key, { ...hotKeyToEvents }?.[item?.type], {
      preventDefault: true,
      enabled: true,
      enableOnFormTags: true,
      enableOnContentEditable: true,
    });
  });

  useEffect(() => {
    setTimeout(() => {
      waitFocusElementRefocusBySelector(waitFocusId);
    }, 100);
  }, [waitFocusId]);

  useEffect(() => {
    let columns = mergeColumnsInDmrTable(
      props?.columns,
      otpsIcdeColumns(tableOperationRef),
      'OtpsIcdeDragTable',
    );

    setTableColumns(columns);
  }, [props?.columns]);

  useEffect(() => {
    // delete事件
    Emitter.on(getDeletePressEventKey('otpsDiagnosisTable'), (itemId) => {
      onDeleteKeyPress(itemId);
    });

    let resizeObserver = null;
    if (itemRef.current) {
      resizeObserver = new ResizeObserver(() => {
        // Do what you want to do when the size of the element changes
        resizeContentById('otpsDiagnosisTable');
      });
      resizeObserver.observe(itemRef.current);
    }

    return () => {
      resizeObserver?.disconnect();

      Emitter.off(getDeletePressEventKey('otpsDiagnosisTable'));
    };
  }, []);

  const clearValuesByKeys = (keys, index) => {
    const otpsIcdeDataSource = props?.form?.getFieldValue(
      'otps-diagnosis-table',
    );
    let formItemId = otpsIcdeDataSource?.[index]?.id;
    let currentFormValues = cloneDeep(form.getFieldsValue(true));
    keys?.forEach((key) => {
      currentFormValues[formItemId][key] = '';
    });

    form.setFieldsValue({
      ...currentFormValues,
    });

    // 更新form
    let tableData = props?.form?.getFieldValue('otps-diagnosis-table');
    keys?.forEach((key) => {
      tableData[index][key] = '';
    });
    // props?.onChange && props?.onChange(tableData);
    props?.form?.setFieldValue('otps-diagnosis-table', cloneDeep(tableData));
  };

  return (
    // DMRDEV-1081 温附2定制 需要去除 EmrExtraTable
    <div style={{ display: 'flex', flexDirection: 'row' }}>
      {props?.emrDataTable === true && (
        <EmrExtraTable
          prefix={props?.prefix}
          containerRef={emrContainerRef}
          width={props?.emrTableWidth}
          baseColumns={otpsIcdeColumns()}
          emrColumns={props?.emrColumns}
          dmrTableId={'otpsDiagnosisTable'}
          containerForm={props?.form}
          emrItemKey={'IcdeOtps'}
          componentId={'OtpsIcdeDragTable'}
          onVisibleChange={(status: boolean) => {
            console.log('onVisibleChange', status);
            let dmrTable = document.querySelector(
              "#otpsDiagnosisTable div[class~='uni-drag-edit-table-container']",
            );
            if (!isEmptyValues(dmrTable)) {
              requestAnimationFrame(() => {
                itemRef.current.style.width = `calc(100% - ${emrContainerRef?.current?.getWidthStyle()} - 5px)`;
              });
            }
          }}
        />
      )}

      <UniDmrDragEditOnlyTable
        {...props}
        formItemContainerClassName={'form-content-item-container'}
        itemRef={mergeRefs([itemRef, ...hotKeyRefs])}
        form={form}
        key={props?.id}
        id={props?.id}
        tableId={props?.id}
        formKey={'otpsDiagnosisTable'}
        forceColumnsUpdate={props?.underConfiguration ?? false}
        scroll={{
          x: 'max-content',
        }}
        pagination={false}
        className={`table-container ${props?.className || ''}`}
        dataSource={(props?.form?.getFieldValue('otps-diagnosis-table') ?? [])
          ?.filter((item) => item.id !== 'ADD')
          ?.map((item) => {
            if (!item['id']) {
              item['id'] = Math.round(Date.now() / 1000);
            }

            return item;
          })
          ?.concat({
            id: 'ADD',
          })}
        rowKey={'id'}
        onValuesChange={(tableData) => {
          props?.form?.setFieldValue('otps-diagnosis-table', tableData);
          triggerFormValueChangeEvent('otps-diagnosis-table');
        }}
        onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
          props?.form?.setFieldValue(
            'otps-diagnosis-table',
            cloneDeep(tableData),
          );
          if (focusId) {
            setTimeout(() => {
              waitFocusElementRefocus(focusId);
            }, 100);
          }
          triggerFormValueChangeEvent('otps-diagnosis-table');
        }}
        columns={tableColumns}
      />
    </div>
  );
};
export default React.memo(OtpsIcdeDragTable);
