import dayjs from 'dayjs';
import { useState, useEffect } from 'react';
import { reportIcon, flagIcon, searchIcon, report, flag, search } from './icon';
import { VTable, VRender, tools, TYPES } from '@visactor/vtable-gantt';
import { getColumns, getRecords } from './tools';
import { CheckboxChangeEvent } from 'antd/lib/checkbox/Checkbox';
import { filterByWorkCoder } from '../../utils';

const Standard_Duration =
  (window as any).externalConfig?.['EnergyEfficiencyManagement']
    ?.Standard_Duration ?? 8;

export const useGanttService = (personInfo = [], setPersonInfo) => {
  VTable.register.icon('flag', flagIcon);
  VTable.register.icon('report', reportIcon);
  VTable.register.icon('search', searchIcon);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
    if (selectVal?.length) {
      const list = filterByWorkCoder(selectVal, personInfo);
      setPersonInfo(list);
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setSelectVal([]);
    setChecked(false);
  };

  const [option, setOption] = useState<any>({});

  useEffect(() => {
    handleDate();
  }, [personInfo]);

  const handleDate = async () => {
    if (personInfo?.length > 0) {
      const columns = await getColumns(personInfo);
      const records = await getRecords(personInfo);
      const list = personInfo?.map((item) => {
        return {
          value: item?.WorkCoder,
          label: item?.WorkCoderName,
        };
      });
      setSelectOptions(list);
      setOption({
        overscrollBehavior: 'none',
        records: records,
        tasksShowMode: TYPES.TasksShowMode.Sub_Tasks_Inline,
        taskListTable: {
          enableTreeNodeMerge: true,
          columns: [...columns],
          tableWidth: 'auto',
          theme: VTable.themes.ARCO.extends({
            // 表格外边框设置
            frameStyle: {
              borderLineWidth: 0,
              shadowBlur: 0,
            },
            headerStyle: {
              bgColor: '#87cefa19',
              fontSize: 18,
              fontFamily: 'Arial',
              fontWeight: 'normal',
              color: '#000000e5',
              hover: {
                cellBgColor: '#87cefa19',
              },
            },
            bodyStyle: {
              bgColor: '#87cefa19',
              hover: {
                cellBgColor: 'rgba(0,0,0,0.03)',
              },
            },
            tooltipStyle: { color: '#fff', bgColor: '#202328' },
          }),
          tooltip: {
            isShowOverflowTextTooltip: true,
          },
          menu: {
            contextMenuItems: ['编辑'],
          },
          frozenColCount: 1,
        },
        frame: {
          outerFrameStyle: {
            borderLineWidth: 1,
            borderColor: '#e1e3e8',
            cornerRadius: 0,
          },
          verticalSplitLine: {
            lineColor: '#e1e3e8',
            lineWidth: 1,
          },
          horizontalSplitLine: {
            lineColor: '#e1e3e8',
            lineWidth: 1,
          },
          verticalSplitLineMoveable: true,
          verticalSplitLineHighlight: {
            lineColor: 'green',
            lineWidth: 1,
          },
        },
        grid: {
          verticalLine: {
            lineWidth: 1,
            lineColor: '#e1e3e8',
          },
          horizontalLine: {
            lineWidth: 1,
            lineColor: '#e1e3e8',
          },
        },
        headerRowHeight: 40,
        rowHeight: 60,
        taskBar: {
          startDateField: 'start',
          endDateField: 'end',
          progressField: 'progress',
          moveable: false,
          resizable: false,
          labelText: '{WorkOriginName}',
          labelTextStyle: {
            fontFamily: 'Arial',
            fontSize: 16,
            textAlign: 'left',
          },
          barStyle: {
            width: 44,
            /** 任务条的颜色 */
            barColor: `rgb(68 99 244)`,
            /** 已完成部分任务条的颜色 */
            completedBarColor: '#91e8e0',
            /** 任务条的圆角 */
            cornerRadius: 6,
          },
          hoverBarStyle: { cornerRadius: 6 },
          customLayout: (args) => {
            const { width, height, taskRecord } = args;
            const container = new VRender.Group({
              width:
                taskRecord?.WorkAmtAbnType === 'High'
                  ? width
                  : taskRecord.TotalWorkAmt > Standard_Duration
                  ? width
                  : width * (taskRecord.TotalWorkAmt / Standard_Duration),
              height,
              cornerRadius: 6,
              fill:
                taskRecord?.WorkAmtAbnType === 'High'
                  ? 'rgb(233 81 67)'
                  : taskRecord?.WorkAmtAbnType === 'Normal'
                  ? 'rgb(65 170 240)'
                  : 'rgb(197 232 255)',
              display: 'flex',
              boundsPadding: [0, 1, 0, 1],
            });
            const containerLeft = new VRender.Group({
              height,
              width,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start',
              justifyContent: 'center',
              boundsPadding: [0, 0, 0, 3],
            });
            container.add(containerLeft);
            const developer = new VRender.Text({
              text: taskRecord.TotalWorkAmt,
              fontSize: 16,
              fontFamily: 'sans-serif',
              fill: 'white',
              fontWeight: 'bold',
              boundsPadding: [0, 0, 0, 0],
            });
            containerLeft.add(developer);
            return {
              rootContainer: container,
            };
          },
        },
        timelineHeader: {
          verticalLine: {
            lineWidth: 1,
            lineColor: '#e1e3e8',
          },
          horizontalLine: {
            lineWidth: 0,
            lineColor: '#e1e3e8',
          },
          backgroundColor: '#FFF',
          colWidth: 120,
          colHeight: 40,
          scales: [
            {
              unit: 'month',
              step: 1,
              format(date) {
                const day = tools.formatDate(
                  new Date(date.startDate),
                  'yyyy-mm',
                );
                return day;
              },
              style: {
                fontSize: 18,
                color: '#1890ff',
                fontWeight: '500',
                textAlign: 'left',
                textBaseline: 'middle',
              },
            },
            {
              unit: 'day',
              step: 1,
              format(date) {
                const day = tools.formatDate(new Date(date.startDate), 'dd');
                const weekFlag = dayjs(new Date(date.startDate)).format('dddd');
                return day + '(' + weekFlag + ')';
              },
              style: {
                color: '#808080',
                fontSize: 14,
                textAlign: 'left',
                fontWeight: 'normal',
              },
            },
          ],
        },
        markLine: [
          // {
          //   date: tools.formatDate(
          //     new Date(personInfo[0]?.StartCalendar),
          //     'yyyy-mm-dd hh:mm:ss',
          //   ),
          //   scrollToMarkLine: true,
          //   position: 'middle',
          //   style: {
          //     lineColor: 'blue',
          //     lineWidth: 1,
          //     lineDash: [5, 5],
          //   },
          // },
        ],
        minDate: personInfo[0]?.StartCalendar,
        maxDate: personInfo[0]?.FinishCalendar,
        scrollStyle: {
          scrollRailColor: 'RGBA(246,246,246,0.5)',
          visible: 'scrolling',
          width: 6,
          scrollSliderCornerRadius: 2,
          scrollSliderColor: '#c0c0c0',
        },
      });
    }
  };

  const [checked, setChecked] = useState(false);
  const [selectVal, setSelectVal] = useState([]);
  const [selectOptions, setSelectOptions] = useState([]);

  const onSelectChange = (value) => {
    setSelectVal(value);
  };

  const onCheckboxChange = (e: CheckboxChangeEvent) => {
    setChecked(e.target.checked);
    if (e.target.checked) {
      const list = selectOptions.map((item) => item?.value);
      setSelectVal(list);
    } else {
      setSelectVal([]);
    }
  };

  useEffect(() => {
    if (selectVal?.length && selectVal?.length === selectOptions?.length) {
      setChecked(true);
    }
  }, [selectVal]);

  return {
    selectOptions,
    option,
    isModalOpen,
    showModal,
    selectVal,
    setSelectVal,
    handleOk,
    handleCancel,
    onSelectChange,
    checked,
    onCheckboxChange,
  };
};
