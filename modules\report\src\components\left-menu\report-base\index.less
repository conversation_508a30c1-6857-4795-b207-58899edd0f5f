@import '~@uni/commons/src/style/variables.less';

.report-base-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;

  .report-base-query-container {
    display: flex;
    flex-direction: column;
  }

  // spin
  .ant-spin-nested-loading {
    height: 100%;
  }

  .ant-spin-container {
    height: 100%;
  }

  .report-base-items-container {
    //height: calc(100% - 32px - 10px);
    height: 100%;
    overflow-y: auto;

    .report-base-item {
      padding: 10px 12px;
      // font-size: 14px;
      display: flex;
      flex-direction: row;
      align-items: center;
      cursor: pointer;
      border-bottom: 1px solid #f0f0f0;
    }

    .item-container-select {
      // color: @blue-color;
      color: #fff;
      background-color: #25a99c;
      border-radius: 5px;
      //margin: 5px;
    }
  }
}
