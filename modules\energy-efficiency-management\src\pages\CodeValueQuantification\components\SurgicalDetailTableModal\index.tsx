import { RespVO, TableResp } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src/commonService';
import { createContext, useContext, useEffect, useRef, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { DetailColumnItem } from './interface';
import {
  defaultCheckedColumnStateProcessor,
  selectedTableColumnsProcessor,
} from './utils';
import _ from 'lodash';
import { columns } from '../DetailColumnSettingContent/columns';
import { isEmptyValues } from '@uni/utils/src/utils';
import md5 from 'crypto-js/md5';
import {
  Button,
  Divider,
  Form,
  message,
  Modal,
  Space,
  TableProps,
  Tooltip,
} from 'antd';
import { ExportIconBtn, UniTable } from '@uni/components/src/index';
import {
  CloseOutlined,
  RedoOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import './index.less';
import { Emitter } from '@uni/utils/src/emitter';
import DetailColumnSettingContent from '../DetailColumnSettingContent/index';
import {
  columnSettingColumnsProcessor,
  columnsTreeProcessor,
} from '../TableStatistic/utils';
import { useDeepCompareEffect, useLocalStorageState } from 'ahooks';
import { DetailColumnSettingContentConstants } from '../DetailColumnSettingContent/constants';
import { v4 as uuidv4 } from 'uuid';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import DetailColumnsSettingModal from '@uni/components/src/query-detail-columns-settings/index';

const pageSize = 9;

const SurgicalDetailTableModal = ({
  tableParams,
  visible,
  record,
  dictData, // 字典数据
  modalTitle,
  onClose,
}) => {
  // 添加AbortController引用，用于取消请求
  const abortControllerRef = useRef<AbortController | null>(null);
  let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');
  const [detailColumns, setDetailColumns] = useState<DetailColumnItem[]>([]);
  const [detailTableDataSource, setDetailTableDataSource] = useState([]);
  const [columnsState, setColumnState] = useState<any>({});
  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: pageSize,
    pageSizeOptions: ['9', '10', '20', '50', '100'],
    hideOnSinglePage: false,
  });
  const [sorterFilter, setSorterFilter] = useState({});

  // total retColSubject
  const {
    data: retColSubject,
    loading: combineQueryDataColumnLoading,
    run: combineQueryDataColumnReq,
  } = useRequest(
    () =>
      uniCommonService('Api/Analysis/AnaModelDef/GetRetColSubjectList', {
        params: { TableName: 'OmniCard' },
      }),
    {
      manual: true,
      formatResult: (r) =>
        r.code === 0 && r.statusCode === 200
          ? r.data.map((c) => ({
              ...c,
              data: c.name,
              dataIndex: c.name,
              originTitle: c.title,
            }))
          : [],
    },
  );

  // column template 列配置
  const {
    loading: columnsReqLoading,
    run: columnsReq,
    data: columnsData,
  } = useRequest(
    (metricName) => {
      return uniCommonService(`Api/DmrAnalysis/CoderEfficiency/GetTemplate`, {
        method: 'GET',
        params: {
          metricName,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          return response.data;
        }
        return [];
      },
    },
  );

  // 获取表格数据
  const { loading: combineQueryDataLoading, run: combineQueryDataReq } =
    useRequest(
      (reqData, current, pageSize) => {
        // 创建新的AbortController
        const controller = new AbortController();
        abortControllerRef.current = controller;

        let data = {
          DtParam: {
            Draw: 1,
            Start: (current - 1) * pageSize,
            Length: pageSize,
          },
          basicArgs: {
            ...tableParams,
            ...reqData.data,
          },
          OperCode: reqData?.OperCode,
          MetricName: reqData?.MetricName,
          MetricType: reqData?.MetricType,
          outputColumns: reqData?.outputColumns,
        };

        if (!isEmptyValues(sorterFilter)) {
          delete global['tableParameters'];
          data['DtParam'] = {
            ...data['DtParam'],
            ...sorterFilter,
          };
        }

        return uniCommonService(
          'Api/DmrAnalysis/CoderEfficiency/GetOperDrillDownDetails',
          {
            method: 'POST',
            requestType: 'json',
            data: data,
            signal: controller.signal, // 使用AbortController的signal
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<TableResp<any, any>>) => {
          return response;
        },
        onSuccess: (response: RespVO<TableResp<any, any>>, params) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            if (response?.cancelled === true) {
              return;
            }

            if (isEmptyValues(response?.data?.data)) {
              // 跳出 不设定值
              if (params[1] === 1) {
                setDetailTableDataSource([]);
              }
              return;
            }

            setDetailTableDataSource(
              response?.data?.data?.map((d) => ({ ...d, UUID: uuidv4() })),
            );

            setBackPagination({
              ...backPagination,
              current: backPagination?.current,
              total: response?.data?.recordsTotal || 0,
            });
          } else {
            setDetailTableDataSource([]);
            setBackPagination({
              ...backPagination,
              current: 1,
              total: 0,
            });
          }
        },
      },
    );

  // 保存 column template
  const {
    loading: saveColumnsReqLoading,
    run: saveColumnsReq,
    // data: columnsData,
  } = useRequest(
    (data) => {
      return uniCommonService(`Api/DmrAnalysis/CoderEfficiency/SaveTemplate`, {
        method: 'POST',
        data: {
          ...data,
          MetricName: record?.MetricName,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          Emitter.emit(
            `${DetailColumnSettingContentConstants.MODAL_CLOSE}_${record?.MetricName}`,
          );
          columnsReq(record?.MetricName);
        }
      },
    },
  );

  // 处理columns
  useEffect(() => {
    console.log('retColSubject', retColSubject, columnsData);
    if (!retColSubject || retColSubject?.length < 1 || !columnsData) return;
    const processedColumnState = defaultCheckedColumnStateProcessor(
      retColSubject,
      columnsData,
    );
    setColumnState(() => ({ ...processedColumnState }));
    // 再单独处理下columns
    setDetailColumns(
      tableColumnBaseProcessor(
        [],
        retColSubject?.map((col) => {
          return {
            ...col,
            title: processedColumnState?.[col.name]?.title ?? col?.title,
          };
        }),
      ),
    );
  }, [retColSubject, columnsData]);
  // 初始化获取列配置
  useEffect(() => {
    if (record?.MetricName) {
      combineQueryDataColumnReq();
      columnsReq(record?.MetricName);
    }
  }, [record?.MetricName]);

  const getSelectedColumns = (value = null) => {
    return selectedTableColumnsProcessor(value ?? columnsState);
  };

  const backTableOnChange: TableProps<any>['onChange'] = (
    pagi,
    filters,
    sorters,
  ) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    let outputColumns = getSelectedColumns();
    if (outputColumns?.length === 0) {
      message.error('请选择需要展示的列');
      setDetailTableDataSource([]);
      return;
    }
    // 特殊处理 sorter
    let sorterFilter = {
      order: [],
      columns: [],
    };

    if (!isEmptyValues(sorters)) {
      const sorterArray = Array.isArray(sorters) ? sorters : [sorters];
      sorterArray.forEach((sorter) => {
        if (sorter?.order) {
          let orderIndex = outputColumns?.findIndex(
            (item) => item.Name === (sorter?.columnKey || sorter?.field),
          );

          if (orderIndex !== -1) {
            sorterFilter.order.push({
              column: orderIndex,
              dir: sorter?.order === 'ascend' ? 'asc' : 'desc',
            });
          }
        }
      });
    }

    setSorterFilter(sorterFilter);

    setTimeout(() => {
      let data = {};
      if (record?.Coder) {
        data['Coders'] = [record?.Coder];
      } else if (record?.CliDept) {
        data['CliDepts'] = [record?.CliDept];
      } else if (record?.Chief) {
        data['Chiefs'] = [record?.Chief];
      }

      combineQueryDataReq(
        {
          data,
          outputColumns,
          OperCode: record?.OperCode,
          MetricName: record?.MetricName,
          MetricType: record?.MetricType,
        },
        pagi.current,
        pagi.pageSize,
      );
    }, 0);
  };

  // 导出修改
  const getExportCaptionByColumns = (
    columns: any[],
    selectedColumns: any[],
  ) => {
    let exportCaption = selectedColumns?.map((d) => {
      let columnItem = columns?.find((item) => item?.id === d?.Id);

      return {
        ...d,
        columnScale: columnItem?.scale,
        columnPrecision: columnItem?.precision,
        ExportTitle: d?.CustomTitle ?? columnItem?.title ?? '',
      };
    });

    return exportCaption;
  };

  useEffect(() => {
    if (record && record?.MetricName && !isEmptyValues(columnsState)) {
      console.log('record', record, columnsState);
      let pagination = {
        ...backPagination,
        current: 1,
        total: 0,
      };

      let outputColumns = getSelectedColumns();
      if (outputColumns?.length === 0) {
        if (!isEmptyValues(columnsState)) {
          message.error('请选择需要展示的列');
        }
        setDetailTableDataSource([]);
        return;
      }

      console.log('record', record);

      let data = {};
      if (record?.Coder) {
        data['Coders'] = [record?.Coder];
      } else if (record?.CliDept) {
        data['CliDepts'] = [record?.CliDept];
      } else if (record?.Chief) {
        data['Chiefs'] = [record?.Chief];
      }

      combineQueryDataReq(
        {
          data,
          outputColumns,
          OperCode: record?.OperCode,
          MetricName: record?.MetricName,
          MetricType: record?.MetricType,
        },
        pagination.current,
        pagination.pageSize,
      );
      setBackPagination(pagination);
    }
  }, [columnsState, record]);

  // 自定义取消函数
  const customCancelRequest = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  };

  // 保存列配置
  const onSaveCols = (items) => {
    saveColumnsReq({
      Subjects: items?.map((item) => {
        return {
          SubjectId: item.id,
          ColumnSort: item?.order,
          CustomTitle: item?.customTitle,
        };
      }),
    });
  };

  const tableProps = {
    id: 'combine-query-table',
    rowKey: 'UUID',
    scroll: {
      x: 'max-content',
      // y: 400,
    },
    columns: [...columns, ...detailColumns] as any[],
    dataSource: detailTableDataSource,
    loading: combineQueryDataLoading || combineQueryDataColumnLoading,
    clickable: false,
    pagination: backPagination,
    dictionaryData: dictData,
    widthCalculate: true,
    // widthDetectAfterDictionary: true,
    onChange: backTableOnChange,
    bordered: true,
    columnsState: {
      value: columnsState,
    },
    forceColumnsUpdate: true,
    infiniteScroll: false,
    infiniteScrollPageSize: pageSize,
    bottomReachMargin: 200,
    emptyDataYHeight: true,
    enableHeaderNoWrap: true,
    enableMaxCharNumberEllipses: true,
  };

  return (
    <>
      <Modal
        className="detail-modal"
        width={'calc(100% - 50px)'}
        style={{ top: 20 }}
        title={
          <div className="detail-modal-header d-flex">
            <h3 style={{ marginBottom: 0 }}>{`${modalTitle} 病案明细`}</h3>
            <Space.Compact className="btn_space">
              {/* 默认分页 TODO 数据传入 */}
              {(userInfo?.Roles ?? [])?.includes('Admin') && (
                <Tooltip title={'列配置'}>
                  <Button
                    type="text"
                    shape="circle"
                    icon={<SettingOutlined className="infinity_rotate" />}
                    onClick={() => {
                      Emitter.emit(
                        `${DetailColumnSettingContentConstants.MODAL_OPEN}_${record?.MetricName}`,
                        {
                          status: true,
                        },
                      );
                    }}
                  />
                </Tooltip>
              )}

              <ExportIconBtn
                isBackend={true}
                backendObj={{
                  url: 'Api/DmrAnalysis/CoderEfficiency/ExportGetOperDrillDownDetails',
                  method: 'POST',
                  data: {
                    outputColumns: getExportCaptionByColumns(
                      detailColumns,
                      getSelectedColumns(),
                    ), // getSelectedColumns(),
                    basicArgs: {
                      ...tableParams,
                      ...{
                        ...(record?.Coder ? { Coders: [record.Coder] } : {}),
                        ...(record?.CliDept
                          ? { CliDepts: [record.CliDept] }
                          : {}),
                        ...(record?.Chief ? { Chiefs: [record.Chief] } : {}),
                      },
                    },
                    OperCode: record?.OperCode,
                    MetricName: record?.MetricName,
                    MetricType: record?.MetricType,
                    // exportCaption: ,
                  },
                  fileName: `病案明细`,
                }}
                btnDisabled={detailTableDataSource?.length < 1}
              />
              <Divider type="vertical" />
              <CloseOutlined
                className="detail-modal-close-x"
                onClick={(e) => {
                  e.stopPropagation();
                  customCancelRequest();
                  setDetailColumns([]);
                  setDetailTableDataSource([]);
                  setColumnState({});
                  onClose && onClose();
                }}
              />
            </Space.Compact>
          </div>
        } // 编码员A 绩效DRG-低风险死亡-编码后优化例数 的
        open={visible}
        closable={false}
        footer={null}
        destroyOnClose={true}
        zIndex={99}
        onCancel={() => {
          customCancelRequest();
          setDetailColumns([]);
          setDetailTableDataSource([]);
          setColumnState({});
          onClose && onClose();
        }}
      >
        <UniTable {...tableProps} />
      </Modal>

      {/* columns 编辑 modal */}
      <DetailColumnsSettingModal
        type={record?.MetricName}
        detailColumns={tableProps.columns}
        columnState={columnsState}
        onOk={onSaveCols}
      />
    </>
  );
};

export default SurgicalDetailTableModal;
