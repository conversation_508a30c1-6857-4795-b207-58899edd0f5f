.emr-table-extra-container {
  background: #ffffff;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;

  .emr-extra-table-opacity-1 {
    opacity: 1 !important;
  }

  .emr-extra-table {
    display: flex;
    flex-direction: column;
    width: 100% !important;

    transition: width 0.5s ease-in-out, opacity 0.5s ease-in-out,
      transform 0.5s ease-in-out;
    overflow: hidden;
    transform: translateX(0);

    opacity: 0;

    .uni-table {
      width: 100%;
    }

    .ant-table-thead > tr > th {
      padding: 4px 4px !important;
    }

    .ant-table-tbody > tr {
      height: 40px;
      min-height: 40px;
    }

    .ant-table-tbody > tr > td {
      padding: 0px 4px !important;
      min-height: 40px;
    }
  }

  .emr-extra-table-hidden {
    height: 0px !important;
    width: 0px !important;
    display: flex;
    flex-direction: row;

    transition: width 0.5s ease, opacity 0.5s ease, transform 0.5s ease;
    opacity: 0;
    transform: translateX(-100%);

    .uni-table {
      width: 100%;
    }
  }

  .prefix-show {
    display: flex;
    flex-direction: row;
    width: 100% !important;

    transition: width 0.5s ease-in-out, opacity 0.5s ease-in-out,
      transform 0.5s ease-in-out;
    overflow: hidden;
    opacity: 1;
    transform: translateX(0);
  }

  .prefix-hidden {
    height: 0px !important;
    width: 0px !important;
    display: flex;
    flex-direction: row;

    transition: width 0.5s ease, opacity 0.5s ease, transform 0.5s ease;
    opacity: 0;
    transform: translateX(-100%);
  }

  .emr-extra-table-switch {
    display: flex;
    flex-direction: column;
    width: 20px;
    height: 100%;
    align-items: center;
    border: 1px solid #a3cef1;
    cursor: pointer;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    background-color: #a3cef1;
  }

  .separator-transition {
    transition: width 0.5s ease, opacity 0.5s ease, transform 0.5s ease;
    opacity: 1;
    transform: translateX(0);
  }

  .separator-transition-hidden {
    transition: width 0.5s ease, opacity 0.5s ease, transform 0.5s ease;
    opacity: 0;
    transform: translateX(-100%);
  }

  .empty-container {
    width: 100% !important;
  }
}
