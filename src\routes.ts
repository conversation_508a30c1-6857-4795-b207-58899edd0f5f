// dmr configurations

export const configurationRoutes = [
  // 用户权限
  {
    path: '/systemConfiguration/permissions/menu',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/menu/index',
  },
  {
    path: '/systemConfiguration/permissions/users',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/users/index',
  },
  {
    path: '/systemConfiguration/permissions/roles',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/roles/index',
  },
  // 首页登记
  {
    path: '/systemConfiguration/layouts/index',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/layouts/index',
  },
  {
    path: '/systemConfiguration/layouts/enrollment',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/enrollment/index',
  },
  // 首页字典
  {
    path: '/systemConfiguration/base/dictionary',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/dictionary/index',
  },
  {
    path: '/systemConfiguration/base/dmrColumnDictMatch',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/dmrColumnDictMatch/index',
  },
  {
    path: '/systemConfiguration/base/icde',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/icde/index',
  },
  {
    path: '/systemConfiguration/base/operation',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/operation/index',
  },

  {
    path: '/systemConfiguration/base/category',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/category/index',
  },
  {
    path: '/systemConfiguration/base/missing',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/missing/index',
  },
  {
    path: '/systemConfiguration/base/department',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/department/index',
  },
  {
    path: '/systemConfiguration/base/operSet',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/operSet/index',
  },
  // 接口字典对照
  {
    path: '/systemConfiguration/path/icde',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/path/icde/index',
  },
  {
    path: '/systemConfiguration/path/operation',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/path/oper/index',
  },
  {
    path: '/systemConfiguration/path/dictionary',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/path/dictionary/index',
  },
  {
    path: '/systemConfiguration/path/department',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/path/cliDept/index',
  },
  {
    path: '/systemConfiguration/path/ward',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/path/ward/index',
  },
  {
    path: '/systemConfiguration/path/dynHierarchy',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/path/dynHierarchy/index',
  },
  // 结算字典
  {
    path: '/systemConfiguration/insur/dictionary',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/insur/dictionary/index',
  },
  {
    path: '/systemConfiguration/insur/insurColumnDictMatch',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/insur/insurColumnDictMatch/index',
  },
  {
    path: '/systemConfiguration/insur/icde',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/insur/icde/index',
  },
  {
    path: '/systemConfiguration/insur/operation',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/insur/operation/index',
  },
  {
    path: '/systemConfiguration/insur/department',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/insur/department/index',
  },
  {
    path: '/systemConfiguration/insur/missing',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/insur/missing/index',
  },
  // 国考字典
  {
    path: '/systemConfiguration/hqms/dictionary',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/hqms/dictionary/index',
  },
  {
    path: '/systemConfiguration/hqms/icde',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/hqms/icde/index',
  },
  {
    path: '/systemConfiguration/hqms/operation',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/hqms/operation/index',
  },
  {
    path: '/systemConfiguration/hqms/department',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/hqms/department/index',
  },
  {
    path: '/systemConfiguration/hqms/missing',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/hqms/missing/index',
  },
  // 卫统字典
  {
    path: '/systemConfiguration/wt/dictionary',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/wt/dictionary/index',
  },
  {
    path: '/systemConfiguration/wt/icde',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/wt/icde/index',
  },
  {
    path: '/systemConfiguration/wt/operation',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/wt/operation/index',
  },
  {
    path: '/systemConfiguration/wt/department',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/wt/department/index',
  },
];

export const escalateRoutes = [
  {
    path: '/systemConfiguration/escalate/dictionary',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/escalate/dictionary/index',
  },
  {
    path: '/systemConfiguration/escalate/icde',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/escalate/icde/index',
  },
  {
    path: '/systemConfiguration/escalate/operation',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/escalate/operation/index',
  },
  {
    path: '/systemConfiguration/escalate/department',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/escalate/cliDepts/index',
  },
  {
    path: '/systemConfiguration/reportCliDeptSettings',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/reportCliDeptSettings',
  },
  {
    path: '/systemConfiguration/calendar',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/calendar',
  },
];

export const institutionRoutes = [
  {
    path: '/systemConfiguration/institution/hospital',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/hospital/index',
  },
  {
    path: '/systemConfiguration/institution/department',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/cliDepts/index',
  },
  {
    path: '/systemConfiguration/institution/ward',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/ward/index',
  },
  {
    path: '/systemConfiguration/base/icdeCategory',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/icdeCategory/index',
  },
  {
    path: '/systemConfiguration/base/operCategory',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/operCategory/index',
  },
  // 学科
  {
    path: '/systemConfiguration/institution/majorPerfDept',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/perfDept/majorPerfDept/index',
  },
  // 绩效科室
  {
    path: '/systemConfiguration/institution/perfDept',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/perfDept/hierarchyPerfDept/index',
  },
  // 绩效科室 & 学科
  {
    path: '/systemConfiguration/institution/hierarchyPerfDept',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/perfDept/index',
  },
  {
    path: '/systemConfiguration/institution/employees',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/employees',
  },
  {
    path: '/systemConfiguration/institution/hospEmployee',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/hospEmployee',
  },
];

// insur configurations
export const insurConfigurationRoutes = [
  // 用户权限
  {
    path: '/insurConfiguration/permissions/menu',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/menu/index',
  },
  {
    path: '/insurConfiguration/permissions/users',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/users/index',
  },
  {
    path: '/insurConfiguration/permissions/roles',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/roles/index',
  },
  // 结算字典
  {
    path: '/insurConfiguration/insur/dictionary',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/insur/dictionary/index',
  },
  {
    path: '/insurConfiguration/insur/insurColumnDictMatch',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/insur/insurColumnDictMatch/index',
  },
  {
    path: '/insurConfiguration/insur/icde',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/insur/icde/index',
  },
  {
    path: '/insurConfiguration/insur/icdeReference',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/insurConfiguration/icdeReference/index',
  },
  {
    path: '/insurConfiguration/insur/operation',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/insur/operation/index',
  },
  {
    path: '/insurConfiguration/insur/operationReference',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/insurConfiguration/operReference/index',
  },
  {
    path: '/insurConfiguration/insur/department',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/insur/department/index',
  },
  {
    path: '/insurConfiguration/insur/missing',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/insur/missing/index',
  },
  // 医疗机构人员
  {
    path: '/insurConfiguration/institution/hospital',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/hospital/index',
  },
  {
    path: '/insurConfiguration/institution/department',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/cliDepts/index',
  },
  {
    path: '/insurConfiguration/institution/ward',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/ward/index',
  },
  {
    path: '/insurConfiguration/base/icdeCategory',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/icdeCategory/index',
  },
  {
    path: '/insurConfiguration/base/operCategory',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/base/operCategory/index',
  },
  // 学科
  {
    path: '/insurConfiguration/institution/majorPerfDept',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/perfDept/majorPerfDept/index',
  },
  // 绩效科室
  {
    path: '/insurConfiguration/institution/perfDept',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/perfDept/hierarchyPerfDept/index',
  },
  // 绩效科室 & 学科
  {
    path: '/insurConfiguration/institution/hierarchyPerfDept',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/perfDept/index',
  },
  {
    path: '/insurConfiguration/institution/employees',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/configuration/employees',
  },
  // 医嘱目录管理
  {
    path: '/insurConfiguration/advsManagement',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/insurConfiguration/advs/index',
  },
  // 收费目录管理
  {
    path: '/insurConfiguration/medChrgitmsManagement',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/insurConfiguration/medChrgitms/index',
  },
  // 在院病人信息字典
  {
    path: '/insurConfiguration/inHospPatientInfoDict',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/insurConfiguration/inHospPatient/index',
  },
  // 异常病例监控规则
  {
    path: '/insurConfiguration/abnormalCaseRules',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/insurConfiguration/abnormalCaseRule/index',
  },
  // drg支付标准管理
  {
    path: '/insurConfiguration/drgPayStandard',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/insurConfiguration/drgPayStandard/index',
  },
  // dip支付标准管理
  {
    path: '/insurConfiguration/dipPayStandard',
    exact: true,
    wrappers: ['@/layouts/base-layout'],
    component: '@/pages/insurConfiguration/dipPayStandard/index',
  },
];
