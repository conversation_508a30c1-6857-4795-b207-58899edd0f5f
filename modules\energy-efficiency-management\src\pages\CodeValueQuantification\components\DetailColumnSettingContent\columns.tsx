import {
  CloseOutlined,
  MenuOutlined,
  VerticalAlignBottomOutlined,
  VerticalAlignTopOutlined,
} from '@ant-design/icons';
import IconBtn from '@uni/components/src/iconBtn/index';
import { Emitter } from '@uni/utils/src/emitter';
import { Input, message, Popconfirm, Tooltip } from 'antd';
import { SortableHandle } from 'react-sortable-hoc';
import { DetailColumnSettingContentConstants } from './constants';

const DragHandler = (node) => {
  return SortableHandle(() => <div className={'grab-handle'}>{node}</div>);
};

export const columns = [
  {
    dataIndex: 'operation',
    visible: true,
    width: 40,
    align: 'center',
    title: '',
    fixed: 'left',
    render: (node, record, index) => {
      return (
        <IconBtn
          title="查看病案首页"
          type="checkInfo"
          className="operation-btn"
          onClick={(e) => {
            if (record?.HisId) {
              // window.open(
              //   `/dmr/index?hisId=${encodeURIComponent(record.HisId)}`,
              // );
              // global['tabAwareWorker'].port.postMessage({
              //   type: 'DMR_VIEW_CLICK',
              //   payload: {
              //     hisId: encodeURIComponent(record.HisId),
              //   },
              // });
              (global?.window as any)?.eventEmitter?.emit('DMR_AOD_STATUS', {
                status: true,
                hisId: record.HisId,
                forceDoubleDeckDoctorEmr: true,
              });
            } else {
              message.warn('HisId不存在');
            }
          }}
        />
      );
    },
  },
];

export const columnSettingColumns = [
  {
    dataIndex: '',
    title: '',
    visible: true,
    align: 'center',
    width: '10%',
    readonly: true,
    renderFormItem: ({ index, entity }) => {
      const SortDragHandler = DragHandler(<MenuOutlined />);
      return <SortDragHandler />;
    },
  },
  {
    dataIndex: 'originTitle',
    title: '项目名',
    visible: true,
    align: 'center',
    width: '35%',
    readonly: true,
    renderFormItem: ({ index, entity }) => {
      return <span>{entity['originTitle']}</span>;
    },
  },
  {
    dataIndex: 'customTitle',
    title: '重命名',
    visible: true,
    align: 'center',
    width: '35%',
    renderFormItem: ({ index, entity }) => {
      return (
        <Input.TextArea
          autoSize={{ minRows: 1 }}
          defaultValue={entity['title']}
          placeholder={entity['title']}
        />
      );
    },
  },
  {
    dataIndex: 'operation',
    title: '',
    visible: true,
    align: 'center',
    width: '20%',
    readonly: true,
    renderFormItem: ({ index, entity }) => {
      return (
        <div className={'selected-table-operation-container'}>
          <Tooltip title={'置顶'}>
            <VerticalAlignTopOutlined
              className={'item'}
              onClick={() => {
                Emitter.emit(
                  DetailColumnSettingContentConstants.COLUMN_SELECT_TOP,
                  {
                    index: index,
                    entity: entity,
                  },
                );
              }}
            />
          </Tooltip>
          <Tooltip title={'置底'}>
            <VerticalAlignBottomOutlined
              className={'item'}
              onClick={() => {
                Emitter.emit(
                  DetailColumnSettingContentConstants.COLUMN_SELECT_BOTTOM,
                  {
                    index: index,
                    entity: entity,
                  },
                );
              }}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除这列？"
            okText={'确定'}
            cancelText={'取消'}
            onConfirm={() => {
              Emitter.emit(
                DetailColumnSettingContentConstants.COLUMN_SELECT_DELETE,
                {
                  index: index,
                  entity: entity,
                },
              );
            }}
          >
            <Tooltip title={'删除'}>
              <CloseOutlined className={'item'} />
            </Tooltip>
          </Popconfirm>
        </div>
      );
    },
  },
];
