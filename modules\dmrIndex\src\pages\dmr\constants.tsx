import { ShortcutItem } from '@uni/commons/src/interfaces';
import {
  admsIcdeColumns,
  icdeColumns,
  icuColumns,
  operationColumns,
  otpsIcdeColumns,
  pathologyIcdeColumns,
} from '@/pages/dmr/columns';

export const tumorKeys = [
  'BkupDegCode',
  'TumorIcdeCode',
  'TumorIcdeName',
  'TumorDiagEvid',
  'TumorStaging_T',
  'TumorStaging_N',
  'TumorStaging_M',
  'TumorStagingType',
  'TumorTrtCond',
  'TumorTrtItem',
  'TumorTrtDesc',
  'TumorMultPrimary',
  'TumorLateralPosition',
  'TumorInitialTrtHosp',
  'TumorInitialTrtItem',
];

export const requiredKeys = [
  'HospCode',
  'PatNo',
  'InTimes',
  'InDate',
  'OutDate',
  'YLFKFS',
  'PatName',
  'PatSex',
  'PatBirth',
  'PatAge',
  'Country',
  'PatNation',
  'PatMarrage',
  'PatIdKind',
  // 'IdCard',
  // 'NativePlace',
  'NativePlaceProv',
  'NativePlaceCity',
  'AdmissionPath',
  'InDept',
  'OutDept',
  'InPeriod',
  'IcdeOtpsItem',
  'IcdeOtpsIcdeCode',
  'IcdeOtpsIcdeName',
  'Allergy',
  'XX',
  'Rh',
  'OutType',
  'ReturnPlan',
  // 'Unconscious',
  // 'RYQ_T',
  // 'RYQ_XS',
  // 'RYQ_FZ',
  // 'RYH_T',
  // 'RYH_XS',
  // 'RYH_FZ',
  'MedfeeSumamt',
  'SelfpayAmt',
];

/**
 * 表示必填 但是不在页面上展示 红色 * 号
 */
export const semiRequiredKeys = [
  'CurAddress',
  'CurPost',
  'RegistPost',
  'WorkPost',
  'Director',
  'Chief',
  'Chfpdr',
  'Resident',
  'RespNurse',
  'Coder',
  'Director',
];

export const formKeyToNavigationId = {
  RH: 'Rh',
  // 出生地
  BirthPlaceProv: 'BirthPlaceSeparateSelector',
  BirthPlaceCity: 'BirthPlaceSeparateSelector',
  BirthPlaceCoty: 'BirthPlaceSeparateSelector',
  // 籍贯
  NativePlaceProv: 'NativePlaceSeparateSelector',
  NativePlaceCity: 'NativePlaceSeparateSelector',

  // 现在住址
  CurAddressProv: 'CurAddressSeparateSelector',
  CurAddressCity: 'CurAddressSeparateSelector',
  CurAddressCoty: 'CurAddressSeparateSelector',
  CurAddressHousNum: 'CurAddressSeparateSelector',

  // 户口地址
  RegistAddrProv: 'RegistAddressSeparateSelector',
  RegistAddrCity: 'RegistAddressSeparateSelector',
  RegistAddrCoty: 'RegistAddressSeparateSelector',
  RegistAddrSubd: 'RegistAddressSeparateSelector',
  RegistAddrHousNum: 'RegistAddressSeparateSelector',

  // 诊断 TODO 诊断跳转

  // TODO 表格
  'Icde*': 'diagnosisTable',
  'Oper*': 'operationTable',
  'Icu*': 'icuTable',

  // 过敏
  Allergy: 'MedicineAllergy',
  AllergyDrugs: 'MedicineAllergy',

  // 离院方式
  OutType: 'OutHospital',

  // 再住院
  ReturnPlan: 'PatientReturnPlan',
  ReturnPurpose: 'PatientReturnPlan',

  // 费用
  MedfeeSumamt: 'MedicalFee',
  SelfpayAmt: 'MedicalFee',
};

export const noClickElementsClassName: string[] = [
  'ant-checkbox-input',
  'ant-switch',
  'operation',
];

export const deleteSpecialKeysProcessorByPrefix = (
  formKey: string,
  activeId: string,
) => {
  // 单独适配IcdeNote 因为存在诊断表 和 病理表
  if (formKey === 'IcdeNote') {
    if (activeId?.includes('PathologyTable')) {
      return 'pathologicalDiagnosisTable';
    }
  }

  if (
    formKey?.startsWith('Icde') ||
    icdeColumns?.find((item) => item.dataIndex === formKey) !== undefined
  ) {
    return 'diagnosisTable';
  }

  if (
    formKey?.startsWith('Oper') ||
    operationColumns?.find((item) => item.dataIndex === formKey) !== undefined
  ) {
    return 'operationTable';
  }

  if (
    formKey?.startsWith('PathologyIcde') ||
    pathologyIcdeColumns?.find((item) => item.dataIndex === formKey) !==
      undefined
  ) {
    return 'pathologicalDiagnosisTable';
  }

  // 入院门急诊表格
  if (
    formKey?.startsWith('AdmsIcde') ||
    admsIcdeColumns()?.find((item) => item.dataIndex === formKey) !== undefined
  ) {
    return 'admsDiagnosisTable';
  }

  if (
    formKey?.startsWith('OtpsIcde') ||
    otpsIcdeColumns()?.find((item) => item.dataIndex === formKey) !== undefined
  ) {
    return 'otpsDiagnosisTable';
  }

  if (
    formKey?.startsWith('Icu') ||
    icuColumns?.find((item) => item.dataIndex === formKey) !== undefined
  ) {
    return 'icuTable';
  }

  return undefined;
};

export const deleteSpecialKeysProcessor = {
  MedicineAllergy: (formKey: string) => 'Allergy',
  // Icde表格
  // 'IcdeCode': (formKey: string) => formKey?.split("#")?.slice(1,3)?.join("#"),
  IcdeCode: (formKey: string) => 'diagnosisTable',
  IcdeNote: (formKey: string) => 'diagnosisTable',
  IcdeCond: (formKey: string) => 'diagnosisTable',
  IcdeOutcome: (formKey: string) => 'diagnosisTable',
  // 'IsMain': (formKey: string) => 'diagnosisTable',
  // 手术表格
  OperCode: (formKey: string) => 'operationTable',
  OprnOprtBegntime: (formKey: string) => 'operationTable',
  Operator: (formKey: string) => 'operationTable',
  Firstasst: (formKey: string) => 'operationTable',
  Secondasst: (formKey: string) => 'operationTable',
  WoundHealingRateClass: (formKey: string) => 'operationTable',
  WoundRateClass: (formKey: string) => 'operationTable',
  HealingRateClass: (formKey: string) => 'operationTable',
  AnaType: (formKey: string) => 'operationTable',
  AnaDoc: (formKey: string) => 'operationTable',
  // 'IsMain': (formKey: string) => 'operationTable',
  // 转科科别table
  OutCliDept: (formKey: string) => 'transferTable',
  InCliDept: (formKey: string) => 'transferTable',
  TransferInDate: (formKey: string) => 'transferTable',
  TransferOutDate: (formKey: string) => 'transferTable',
  InDeptHours: (formKey: string) => 'transferTable',

  // 病理表
  PalgNo: (formKey: string) => 'pathologicalDiagnosisTable',
};
