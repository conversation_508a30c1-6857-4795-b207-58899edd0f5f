import dayjs from 'dayjs';

export const historyChangesPortalBaseColumns = [
  {
    dataIndex: 'Sort',
    title: '序号',
    visible: true,
    width: 60,
    align: 'center',
    render: (node, record, index) => {
      return <span>{index + 1}</span>;
    },
  },
];

export const historyChangesPortalColumns = [
  {
    dataIndex: 'CreatorId',
    title: '修改人',
    width: 120,
    visible: true,
  },
  {
    dataIndex: 'CreationTime',
    title: '修改时间',
    visible: true,
    width: 160,
    render: (node, record, index) => {
      return (
        <span>
          {dayjs(record?.CreationTime)?.format('YYYY-MM-DD HH:mm:ss')}
        </span>
      );
    },
  },
  {
    dataIndex: 'ChangeSummary',
    title: '修改内容',
    visible: true,
    width: 540,
    // TODO popup 用来展示以分号分隔的东西
    render: (node, record, index) => {
      let columnWidth = document.getElementById('');
      console.log('ChangeSummary', record, index, node);
      return (
        <div className={'flex-row-center'} style={{ maxWidth: 490 }}>
          <span className={'ellipsis'}>{record?.ChangeSummary}</span>
        </div>
      );
    },
  },
];

export const historyChangesDetailPortalColumns = [
  {
    dataIndex: 'Operation',
    title: '操作',
    visible: true,
    width: 60,
    align: 'center',
    render: (node, record, index) => {
      return (
        <span>
          {record?.Operation === 'Add'
            ? '新增'
            : record?.Operation === 'Modify'
            ? '修改'
            : record?.Operation === 'Remove'
            ? '删除'
            : record?.Operation}
        </span>
      );
    },
  },
  {
    dataIndex: 'DiffMsg',
    title: '内容',
    visible: true,
    width: 500,
    render: (node, record, index) => {
      return <span>{record?.DiffMsg}</span>;
    },
  },
];
