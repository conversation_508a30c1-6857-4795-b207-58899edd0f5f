import { Form, Input, InputNumber, Switch } from 'antd';
import React from 'react';
import { UniSelect } from '@uni/components/src';
import { icdeTypes } from '@/pages/configuration/base/icde/constants';
import { cliDeptTypes } from '@/pages/configuration/base/cliDepts/constants';
import './index.less';
import { useModel } from '@@/plugin-model/useModel';
import Datepicker from '@uni/components/src/picker/datepicker';
import dayjs from 'dayjs';

interface CliDeptAddItemProps {
  form: any;

  editIndex?: number;
}

const CliDeptItemAdd = (props: CliDeptAddItemProps) => {
  const { globalState } = useModel('@@qiankunStateForSlave');

  const dictData = globalState?.dictData;

  return (
    <Form
      className={'cli-depts-add-container'}
      form={props?.form}
      preserve={false}
    >
      <Form.Item
        label="院区"
        name="HospCode"
        rules={[
          {
            required: true,
            message: '请选择院区',
          },
        ]}
      >
        <UniSelect
          dataSource={dictData?.Hospital || []}
          placeholder={'请选择'}
          optionValueKey={'Code'}
          optionNameKey={'Name'}
        />
      </Form.Item>

      <Form.Item
        label="科室编码"
        name="Code"
        rules={[
          {
            required: true,
            message: '请填写编码',
          },
        ]}
      >
        <Input disabled={props?.editIndex !== undefined} />
      </Form.Item>

      <Form.Item
        label="科室名称"
        name="Name"
        rules={[
          {
            required: true,
            message: '请填写名称',
          },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item label="科室类型" name="CliDeptTypes">
        <UniSelect
          dataSource={cliDeptTypes}
          placeholder={'请选择'}
          optionNameKey={'label'}
          maxTagCount={'responsive'}
          mode={'multiple'}
        />
      </Form.Item>

      <Form.Item label="学科" name="MajorPerfDept">
        <UniSelect
          dataSource={dictData?.MajorPerfDepts || []}
          placeholder={'请选择'}
          optionValueKey={'Code'}
          optionNameKey={'Name'}
        />
      </Form.Item>

      <Form.Item label="排序-统计" name="Sort1">
        <InputNumber precision={0} />
      </Form.Item>

      <Form.Item label="排序-动态" name="Sort2">
        <InputNumber precision={0} />
      </Form.Item>

      <Form.Item label="排序-示踪" name="Sort3">
        <InputNumber precision={0} />
      </Form.Item>

      <Form.Item label="排序-预留" name="Sort4">
        <InputNumber precision={0} />
      </Form.Item>

      <Form.Item label="是否有效" name="IsValid">
        <Switch defaultChecked={props?.form?.getFieldValue('IsValid')} />
      </Form.Item>

      <Form.Item label="有效日期" name="dates">
        <Datepicker.RangePicker picker="date" />
      </Form.Item>
    </Form>
  );
};

export default CliDeptItemAdd;
