import dayjs from 'dayjs';
import { BaseLayoutHeaderItem } from '@/layouts/base-layout';
import { getDayjsEndOfByType, getDayjsStartOfByType } from '@/utils/utils';
/**
 * 这里的customHeader 也是header的一部分 FIXME 后期可能需要整合进来
 */
import { customHeaders } from './utils/customHeader';
import { EventConstant } from '@uni/utils/src/emitter';

/**
 * 此页面定义了 某一页面的的headerKey对应其header的 组件
 * 组件定义在 src/components/index.ts
 * valueKey的定义需要与 src/interfaces.tsx 以及 src/utils/commonRequestParamProcessor.ts 一致
 * 此处dataKey 未做任何限制 建议与后端给到的request parameter 一致
 * postProcess: 数据选择之后处理
 * preProcess: 数据选择之前处理
 * propsPreProcess: props参数处理
 * 可对上述方法再做扩展
 * needFetch: 是否需要外部数据支持
 * dataKey: 外部数据返回key
 * valueKey: 输出数据的key
 * componentName: 组件名
 */
const externalSearchConfig = (window as any).externalConfig?.['searchConfig'];
const autoClearSearchValue = externalSearchConfig?.['autoClearSearchValue'];

const fastDateSelectGroup = {
  componentName: 'FastDateSelect',
  needFetch: false,
  props: {
    className: '',
    dataKey: 'fast-date-select',
    valueKey: 'fastDateSelect',
    postProcess: (currentValue, selectedValue, extra = undefined) => {
      return selectedValue;
    },
  },
};

const hospitalNameSingle = {
  label: '医院',
  componentName: 'Select',
  needFetch: true,
  props: {
    dataKey: 'HospitalSingle',
    fetchKey: 'Hospital',
    valueKey: 'hospCodeSingle',
    placeholder: '请选择医院',
    optionValueKey: 'Code',
    optionNameKey: 'Name',
    defaultValueInitialize: (dataKey, dataSource) => {
      let data = {};
      data[dataKey] = dataSource?.at(0)?.Code;
      return data;
    },
  },
};

const cliDeptsWithHospitalCode = {
  // label: '科室（基于院区）',
  label: '科室',
  componentName: 'Select',
  needFetch: false,
  props: {
    dataKey: 'Hierarchies',
    valueKey: 'CliDepts',
    mode: 'multiple',
    virtual: false,
    placeholder: '请选择科室',
    optionValueKey: 'Code',
    optionNameKey: 'Name',
    maxTagCount: 'responsive',
    enableSelectAll: true,
    autoClearSearchValue: autoClearSearchValue,
    dataSourcePreProcess: (searchValue, dataSource) => {
      console.log('cliDeptsDataSourcePreProcess', searchValue, dataSource);
      // if (searchValue['hospCodes'] && searchValue['hospCodes']?.length > 0) {
      //   return dataSource?.filter(
      //     (item) =>
      //       searchValue['hospCodes'].includes(item.HospCode) &&
      //       item.HierarchyType === '1',
      //   );
      // } else {
      return dataSource?.filter((item) => item.HierarchyType === '1');
      // }
    },
  },
};

const majorPerfDeptsWithNoSingleChoose = {
  label: '绩效科室',
  componentName: 'Select',
  needFetch: false,
  props: {
    dataKey: 'MajorPerfDepts',
    valueKey: 'MajorPerfDepts',
    mode: 'multiple',
    virtual: false,
    maxTagCount: 'responsive',
    placeholder: '请选择绩效科室',
    optionValueKey: 'Code',
    optionNameKey: 'Name',
    enableSelectAll: true,
    enableSelectInternal: false,
    enableSelectSurgery: false,
  },
};

const majorPerfDeptsWithCliDeptsCode = {
  label: '学科',
  componentName: 'Select',
  needFetch: false,
  props: {
    dataKey: 'MajorPerfDepts',
    valueKey: 'MajorPerfDepts',
    mode: 'multiple',
    virtual: false,
    maxTagCount: 'responsive',
    placeholder: '请选择学科',
    optionValueKey: 'Code',
    optionNameKey: 'Name',
    enableSelectAll: true,
    enableSelectInternal: true,
    enableSelectSurgery: true,
  },
};

const HierarchiesRelationHospCode = {
  label: '科室（基于院区）',
  componentName: 'DependencySelect',
  needFetch: false,
  props: {
    dataKey: 'Hierarchies',
    valueKey: 'CliDepts',
    mode: 'multiple',
    virtual: false,
    placeholder: '请选择科室',
    optionValueKey: 'Code',
    optionNameKey: 'Name',
    dependencyFormKeys: [
      {
        key: 'hospCodes',
        valueKey: 'HospCode',
      },
    ],
    dataSourcePreProcess: (searchValue, dataSource) => {
      return dataSource?.filter((item) => item.HierarchyType === '1');
    },
  },
};

// 学科 目前还没办法基于院区过滤
const majorPerfDepts = {
  label: '学科',
  componentName: 'Select',
  needFetch: false,
  props: {
    dataKey: 'MajorPerfDepts',
    valueKey: 'MajorPerfDepts',
    mode: 'multiple',
    maxTagCount: 10,
    virtual: false,
    placeholder: '请选择学科',
    optionValueKey: 'Code',
    optionNameKey: 'Name',
    enableSelectAll: true,
    // dataSourcePreProcess: (searchValue, dataSource) => {
    //   console.log('cliDepts dataSourcePreProcess');
    //   if (searchValue['hospCodes'] && searchValue['hospCodes']?.length > 0) {
    //     return dataSource?.filter(
    //       (item) =>
    //         searchValue['hospCodes'].includes(item.HospCode) &&
    //         item.HierarchyType === '1',
    //     );
    //   } else {
    //     return dataSource?.filter((item) => item.HierarchyType === '1');
    //   }
    // },
  },
};

// 受限学科
const constrainedMajorPerfDepts = {
  label: '学科',
  componentName: 'ConstrainedSelect',
  needFetch: false,
  props: {
    dataKey: 'MajorPerfDepts',
    valueKey: 'MajorPerfDepts',
    mode: 'multiple',
    maxTagCount: 10,
    virtual: false,
    placeholder: '请选择学科',
    optionValueKey: 'Code',
    optionNameKey: 'Name',
    enableSelectAll: true,
    dataSourcePreProcess: (searchValue, dataSource, userInfo) => {
      if (userInfo?.MajorPerfDepts?.length > 0) {
        return dataSource?.filter((data) =>
          (userInfo?.MajorPerfDepts || [])?.includes(data.Code),
        );
      }
      return dataSource;
    },
  },
};

// 受限绩效科室
const constrainedPerfDepts = {
  label: '绩效科室',
  componentName: 'ConstrainedSelect',
  needFetch: false,
  props: {
    dataKey: 'PerfDepts',
    valueKey: 'PerfDepts',
    mode: 'multiple',
    maxTagCount: 10,
    virtual: false,
    placeholder: '请选择绩效科室',
    optionValueKey: 'Code',
    optionNameKey: 'Name',
    enableSelectAll: true,
    dataSourcePreProcess: (searchValue, dataSource, userInfo) => {
      if (userInfo?.PerfDepts?.length > 0) {
        return dataSource?.filter((data) =>
          (userInfo?.PerfDepts || [])?.includes(data.Code),
        );
      }
      return dataSource;
    },
  },
};

const cliDepts = {
  label: '科室',
  componentName: 'Select',
  needFetch: false,
  props: {
    dataKey: 'Hierarchies',
    valueKey: 'CliDepts',
    mode: 'multiple',
    maxTagCount: 3,
    virtual: false,
    placeholder: '请选择科室',
    optionValueKey: 'Code',
    optionNameKey: 'Name',
    enableSelectAll: true,
    autoClearSearchValue: autoClearSearchValue,
    dataSourcePreProcess: (searchValue, dataSource) => {
      console.log('cliDepts dataSourcePreProcess');
      if (searchValue['hospCodes'] && searchValue['hospCodes']?.length > 0) {
        return dataSource?.filter(
          (item) =>
            searchValue['hospCodes'].includes(item.HospCode) &&
            item.HierarchyType === '1',
        );
      } else {
        return dataSource?.filter((item) => item.HierarchyType === '1');
      }
    },
  },
};

// 受限科室
const constrainedCliDept = {
  label: '科室',
  componentName: 'ConstrainedSelect',
  needFetch: false,
  props: {
    dataKey: 'CliDepts',
    valueKey: 'CliDepts',
    mode: 'multiple',
    maxTagCount: 10,
    virtual: false,
    placeholder: '请选择科室',
    optionValueKey: 'Code',
    optionNameKey: 'Name',
    enableSelectAll: true,
    autoClearSearchValue: autoClearSearchValue,
    dataSourcePreProcess: (searchValue, dataSource, userInfo) => {
      if (userInfo?.CliDepts?.length > 0) {
        return dataSource?.filter((data) =>
          (userInfo?.CliDepts || [])?.includes(data.Code),
        );
      }
      return dataSource;
    },
  },
};

// 受限病区
const constrainedWard = {
  label: '病区',
  componentName: 'ConstrainedSelect',
  needFetch: false,
  props: {
    dataKey: 'Wards',
    valueKey: 'wards',
    mode: 'multiple',
    placeholder: '请选择病区',
    allowClear: true,
    optionValueKey: 'Code',
    optionNameKey: 'Name',
    enableSelectAll: true,
    dataSourcePreProcess: (searchValue, dataSource, userInfo) => {
      if (userInfo?.Wards?.length > 0) {
        return dataSource?.filter((data) =>
          (userInfo?.Wards || [])?.includes(data.Code),
        );
      }
      return dataSource;
    },
  },
};

// 医疗组
const medTeams = {
  label: '医疗组',
  componentName: 'Select',
  needFetch: false,
  props: {
    dataKey: 'Hierarchies',
    valueKey: 'MedTeams',
    mode: 'multiple',
    maxTagCount: 10,
    virtual: false,
    placeholder: '请选择医疗组',
    optionValueKey: 'Code',
    optionNameKey: 'Name',
    enableSelectAll: true,
    dataSourcePreProcess: (searchValue, dataSource) => {
      if (
        searchValue?.['hospCodes'] &&
        searchValue?.['hospCodes']?.length > 0
      ) {
        return dataSource?.filter(
          (item) =>
            searchValue?.['hospCodes']?.includes(item.HospCode) &&
            item.HierarchyType === '3',
        );
      } else {
        return [];
      }
    },
  },
};

// 受限医疗组
const constrainedMedTeams = {
  label: '医疗组',
  componentName: 'ConstrainedSelect',
  needFetch: false,
  props: {
    dataKey: 'MedTeams',
    valueKey: 'MedTeams',
    mode: 'multiple',
    maxTagCount: 10,
    virtual: false,
    placeholder: '请选择医疗组',
    optionValueKey: 'Code',
    optionNameKey: 'Name',
    enableSelectAll: true,
    dataSourcePreProcess: (searchValue, dataSource, userInfo) => {
      if (userInfo?.MedTeams?.length > 0) {
        return dataSource?.filter((data) =>
          (userInfo?.MedTeams || [])?.includes(data.Code),
        );
      }
      return dataSource;
    },
  },
};

const baseHeaderComponents: { [key: string]: BaseLayoutHeaderItem } = {
  datePicker: {
    label: '时间',
    required: true,
    componentName: 'RangePicker',
    needFetch: false,
    props: {
      className: '',
      dataKey: 'date-range',
      valueKey: 'dateRange',
      dataType: 'date',
      picker: 'month',
      allowClear: 'true',
      valuePreProcess: (value) => {
        return value?.map((item) => {
          if (item) {
            return dayjs(item);
          }

          return item;
        });
      },
      postProcess: (currentValue, selectedValue, extra = undefined) => {
        return selectedValue?.map((item, index) => {
          if (item) {
            if (index === 0) {
              return getDayjsStartOfByType(extra?.picker, dayjs(item)).format(
                'YYYY-MM-DD',
              );
            } else {
              return getDayjsEndOfByType(extra?.picker, dayjs(item)).format(
                'YYYY-MM-DD',
              );
            }
          }

          return item;
        });
      },
    },
  },
  singleDatePicker: {
    label: '时间',
    required: true,
    componentName: 'DatePicker',
    needFetch: false,
    props: {
      className: '',
      dataKey: 'single-date',
      valueKey: 'singleDate',
      dataType: 'date',
      picker: 'date',
      allowClear: 'true',
      valuePreProcess: (value) => {
        return value ? dayjs(value) : value;
      },
      postProcess: (currentValue, selectedValue, extra = undefined) => {
        return selectedValue
          ? dayjs(selectedValue).format('YYYY-MM-DD')
          : selectedValue;
      },
    },
  },
  radioDatePicker: {
    label: '时间',
    outerLabelHidden: true,
    componentName: 'DateRadioPicker',
    needFetch: false,
    props: {
      className: '',
      dataKey: 'radio-date-range',
      valueKeys: ['dateType', 'dateRange'],
      formKey: 'dateRange',
      dataType: 'date',
      allowClear: true,
      valuePreProcess: (values) => {
        if (values?.length === 2) {
          values[1] = values?.at(1)?.map((item) => {
            if (item) {
              return dayjs(item);
            }
            return item;
          });
        }
        return values;
      },
      postProcess: (currentValues, changedValues, extra = undefined) => {
        return extra?.valueKeys?.map((d, i) => {
          if (d === 'dateRange') {
            return {
              [d]: changedValues?.[d]?.map((item, index) => {
                if (item) {
                  if (index === 0) {
                    return getDayjsStartOfByType(
                      extra?.picker === 'byValue'
                        ? changedValues?.['dateType']
                        : extra?.picker,
                      dayjs(item),
                    ).format('YYYY-MM-DD');
                  } else {
                    return getDayjsEndOfByType(
                      extra?.picker === 'byValue'
                        ? changedValues?.['dateType']
                        : extra?.picker,
                      dayjs(item),
                    ).format('YYYY-MM-DD');
                  }
                }

                return item;
              }),
            };
          }
          // 其他的直接返回
          return { [d]: changedValues?.[d] };
        });
      },
    },
  },

  regionSelect: {
    label: '区域',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'Region',
      valueKey: 'regionCodes',
      mode: 'multiple',
      placeholder: '请选择区域',
      optionValueKey: 'code',
    },
  },

  hospitalType: {
    label: '医院类型',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'HospType',
      valueKey: 'hospTypes',
      mode: 'multiple',
      placeholder: '请选择医院类型',
      optionValueKey: 'code',
    },
  },

  hospitalLevel: {
    label: '医院级别',
    componentName: 'Select',
    // needFetch: true,
    props: {
      dataKey: 'HospClass',
      valueKey: 'hospClasses',
      mode: 'multiple',
      placeholder: '请选择医院级别',
      optionValueKey: 'code',
    },
  },

  hospitalName: {
    label: '院区',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'Hospital',
      valueKey: 'hospCodes',
      mode: 'multiple',
      placeholder: '请选择院区',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
      enableOptionAllInData: false,
      dataSourcePreProcess: (searchValue, dataSource) => {
        if (
          searchValue['regionCodes'] &&
          searchValue['regionCodes']?.length > 0
        ) {
          dataSource = dataSource?.filter((item) =>
            searchValue['regionCodes'].includes(item?.infos?.RegionCode),
          );
        }

        if (searchValue['hospTypes'] && searchValue['hospTypes']?.length > 0) {
          dataSource = dataSource?.filter((item) =>
            searchValue['hospTypes'].includes(item?.infos?.HospType),
          );
        }

        if (
          searchValue['hospClasses'] &&
          searchValue['hospClasses']?.length > 0
        ) {
          dataSource = dataSource?.filter((item) =>
            searchValue['hospClasses'].includes(item?.infos?.HospClass),
          );
        }

        return dataSource;
      },
    },
  },

  hospitalNameCoder: {
    label: '院区',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'Hospital',
      valueKey: 'hospCodes',
      mode: 'multiple',
      placeholder: '请选择院区',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
      maxTagCount: 'responsive',
      enableOptionAllInData: false,
      dataSourcePreProcess: (searchValue, dataSource) => {
        if (
          searchValue['regionCodes'] &&
          searchValue['regionCodes']?.length > 0
        ) {
          dataSource = dataSource?.filter((item) =>
            searchValue['regionCodes'].includes(item?.infos?.RegionCode),
          );
        }

        if (searchValue['hospTypes'] && searchValue['hospTypes']?.length > 0) {
          dataSource = dataSource?.filter((item) =>
            searchValue['hospTypes'].includes(item?.infos?.HospType),
          );
        }

        if (
          searchValue['hospClasses'] &&
          searchValue['hospClasses']?.length > 0
        ) {
          dataSource = dataSource?.filter((item) =>
            searchValue['hospClasses'].includes(item?.infos?.HospClass),
          );
        }

        return dataSource;
      },
    },
  },

  // 受限院区
  constrainedHospital: {
    label: '院区',
    componentName: 'ConstrainedSelect',
    needFetch: false,
    props: {
      dataKey: 'Hospital',
      valueKey: 'hospCodes',
      mode: 'multiple',
      maxTagCount: 10,
      virtual: false,
      placeholder: '请选择院区',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
      enableSelectAll: true,
      dataSourcePreProcess: (searchValue, dataSource, userInfo) => {
        if (userInfo?.HospCode?.length > 0) {
          return dataSource?.filter((data) =>
            userInfo?.HospCode?.includes(data.Code),
          );
        }
        return dataSource;
      },
    },
  },

  //drg
  cardType: {
    label: '病案类型',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'CardType',
      valueKey: 'cardTypes',
      mode: 'multiple',
      placeholder: '请选择病案类型',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },
  operType: {
    label: '手术级别',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'OperRate',
      valueKey: 'operRate',
      mode: 'multiple',
      placeholder: '请选择手术级别',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },
  Sd: {
    label: '重点监控病种',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'SdCode',
      valueKey: 'sdCode',
      mode: 'multiple',
      placeholder: '请选择重点监控病种',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },
  VersionedSdCode: {
    label: '重点监控病种',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'VersionedSdCode',
      valueKey: 'versionedSdCode',
      mode: 'multiple',
      placeholder: '请选择重点监控病种',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },
  SdCode: {
    label: '重点监控病种',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'SdCode',
      valueKey: 'SdCode',
      mode: 'multiple',
      placeholder: '请选择重点监控病种',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },
  ChsVersion: {
    label: 'DRG版本',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'ChsVersion',
      valueKey: 'chsVersion',
      placeholder: '请选择DRG版本',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },

  insurType: {
    label: '医保类型',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'InsurType',
      // dataKeyGroup: 'Insur',
      valueKey: 'insurType',
      mode: 'multiple',
      placeholder: '请选择医保类型',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },

  dmrCardMark: {
    label: '病案标识',
    componentName: 'Input',
    needFetch: false,
    props: {
      style: {
        height: 28,
      },
      dataKey: 'SearchKeyword',
      valueKey: 'SearchKeyword',
      placeholder: '病案号/姓名/住院号/条形码',
      allowClear: true,
    },
  },

  uniqueId: {
    label: '病案标识',
    componentName: 'Input',
    needFetch: false,
    props: {
      style: {
        height: 28,
      },
      dataKey: 'UniqueId',
      valueKey: 'uniqueId',
      placeholder: '病案号/姓名',
      allowClear: true,
    },
  },

  coder: {
    label: '编码员',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'Coder',
      valueKey: 'coders',
      mode: 'multiple',
      placeholder: '请选择编码员',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },

  ward: {
    label: '病区',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'Wards',
      valueKey: 'wards',
      mode: 'multiple',
      placeholder: '请选择病区',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },

  versionedDrgCodes: {
    label: 'DRG分组',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'VersionedDrgCode',
      valueKey: 'versionedDrgCodes',
      mode: 'multiple',
      placeholder: '请选择DRG分组',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
      dataSourcePreProcess: (searchValue, dataSource) => {
        return dataSource?.map((d) => ({
          ...d,
          Name: `${d?.Code?.split('-')?.[1] || ''} ${d?.Name}`,
        }));
      },
    },
  },

  errorLevels: {
    label: '错误等级',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'CheckErrorLevel',
      valueKey: 'ErrorLevel',
      mode: 'single',
      placeholder: '请选择状态',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },

  monitorLevel: {
    label: '监控等级',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'QualityMonitorLevel',
      valueKey: 'MonitorLevel',
      mode: 'single',
      placeholder: '请选择监控等级',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },

  dateWithType: {
    label: '',
    componentName: 'DateRangeWithType',
    needFetch: false,
    props: {
      enableDateFormatTypeSelector: false,
      needFormWrapper: false,
      valuePreProcess: (value) => {
        return value?.map((item) => {
          if (item) {
            return dayjs(item);
          }

          return item;
        });
      },
    },
  },

  MainIcdeCode: {
    label: '主要诊断',
    componentName: 'Input',
    needFetch: false,
    props: {
      style: {
        height: 28,
      },
      dataKey: 'MainIcdeCode',
      valueKey: 'MainIcdeCode',
      placeholder: '主要诊断',
      allowClear: true,
    },
  },

  FirstOperCode: {
    label: '第一手术',
    componentName: 'Input',
    needFetch: false,
    props: {
      style: {
        height: 28,
      },
      dataKey: 'FirstOperCode',
      valueKey: 'FirstOperCode',
      placeholder: '第一手术',
      allowClear: true,
    },
  },

  errorLevel: {
    label: '规则等级',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'ErrorLevel',
      valueKey: 'errorLevel',
      placeholder: '请选择规则等级',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
      dataSourcePreProcess: (searchValue, dataSource, userInfo) => {
        return [
          { Name: '提示性规则错误', Code: '2', IsValid: true },
          { Name: '审核强制规则错误', Code: '5', IsValid: true },
        ];
      },
    },
  },

  qualityMonitorLevel: {
    label: '监控等级',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'QualityMonitorLevel',
      valueKey: 'qualityMonitorLevel',
      placeholder: '请选择监控等级',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },
};

/**
 * 可定制  定制样例:
 * {
 *       ...baseHeaderComponents['datePicker'],
 *       props: {
 *         ...baseHeaderComponents['datePicker'].props,
 *         picker: 'month',
 *       },
 *     },
 */

export const headers: {
  [key: string]: BaseLayoutHeaderItem[];
} = {
  default: [],

  settlementAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['hospitalName'],
  ],

  settlementCoderWorker: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month', 'date'],
      },
      required: true,
    },
    {
      ...baseHeaderComponents['hospitalNameCoder'],
      required: true,
    },
  ],

  settlementAnalysisManagement: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['hospitalName'],
    baseHeaderComponents['dmrCardMark'],
  ],

  deptSettlementAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['hospitalName'],
    cliDeptsWithHospitalCode,
  ],
  // 院区、科室、绩效科室
  deptSettlementAnalysisWithPerfDepts: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['hospitalName'],
    cliDeptsWithHospitalCode,
    majorPerfDeptsWithNoSingleChoose,
  ],
  // 绩效科室
  majorPerfDeptSettlementAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['hospitalName'],
    majorPerfDeptsWithCliDeptsCode,
  ],

  medTeamSettlementAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['hospitalName'],
    medTeams,
  ],

  ADrgComposition: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['hospitalName'],
    // baseHeaderComponents['cardType'],
  ],

  hqmsDataManagement: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
        required: true,
      },
    },
    baseHeaderComponents['hospitalName'],
    // baseHeaderComponents['errorLevel'],
  ],

  drgHospDrg: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        mode: undefined,
      },
    },
    cliDeptsWithHospitalCode,
    baseHeaderComponents['uniqueId'],
    baseHeaderComponents['versionedDrgCodes'],
    baseHeaderComponents['MainIcdeCode'],
    baseHeaderComponents['FirstOperCode'],
  ],

  drgHospOper: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        mode: undefined,
      },
    },
    baseHeaderComponents['operType'],
  ],

  drgHospSd: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        mode: undefined,
      },
    },
    baseHeaderComponents['SdCode'],
  ],

  calendar: [
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        mode: undefined,
        allowClear: false,
        required: true,
      },
    },
    {
      ...baseHeaderComponents['singleDatePicker'],
      label: '年份',
      props: {
        ...baseHeaderComponents['singleDatePicker'].props,
        picker: 'year',
        allowClear: false,
        required: true,
      },
    },
    baseHeaderComponents['ward'],
    baseHeaderComponents['coder'],
  ],

  ...customHeaders,

  datePickerAnalysis: [baseHeaderComponents['datePicker']],

  diseaseSequence: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['hospitalName'],
    cliDeptsWithHospitalCode,
    {
      label: '是否主要',
      componentName: 'Switch',
      needFetch: false,
      props: {
        dataKey: 'IsMain',
        valueKey: 'IsMain',
        className: 'header_switch',
      },
    },
    {
      label: '出院人数前',
      componentName: 'InputNumber',

      props: {
        style: { maxWidth: '200px' },
        colon: false,
        dataKey: 'FilterNumberSequence',
        valueKey: 'FilterNumber',
        placeholder: '请输入数量，默认为10',
        allowClear: true,
        addonAfter: '个',
        min: 0,
        precision: 0,
      },
    },
  ],

  surgerySequence: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['hospitalName'],
    cliDeptsWithHospitalCode,
    {
      label: '是否主要',
      componentName: 'Switch',
      needFetch: false,
      props: {
        dataKey: 'IsMain',
        valueKey: 'IsMain',
        className: 'header_switch',
      },
    },
    {
      label: '手术类型',
      componentName: 'Select',
      // needFetch: true,
      props: {
        dataKey: 'OperType',
        dataKeyGroup: 'Dmr',
        valueKey: 'OperTypes',
        mode: 'multiple',
        placeholder: '请选择手术类型',
        optionValueKey: 'Code',
        optionNameKey: 'Name',
      },
    },
    {
      label: '出院人数前',
      componentName: 'InputNumber',

      props: {
        style: { maxWidth: '200px' },
        colon: false,
        dataKey: 'FilterNumberSequence',
        valueKey: 'FilterNumber',
        placeholder: '请输入数量，默认为10',
        allowClear: true,
        addonAfter: '个',
        min: 0,
        precision: 0,
      },
    },
  ],

  wikiSettlement: [baseHeaderComponents['ChsVersion']],

  // drg / dip 使用
  settlementRequiredAnalysis: [
    // baseHeaderComponents['datePicker'],
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        // mode: undefined,
        // valueKey: 'hospCode',
        allowClear: true,
      },
    },
  ],

  settlementDeptRequiredAnalysis: [
    // baseHeaderComponents['datePicker'],
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        allowClear: true,
      },
    },
    {
      ...cliDepts,
      required: true,
      props: {
        ...cliDepts.props,
        allowClear: true,
        enableSelectAll: true,
      },
    },
  ],

  settlementRequiredWithWardCoderAnalysis: [
    // baseHeaderComponents['datePicker'],
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        // mode: undefined,
        // valueKey: 'hospCode',
        allowClear: true,
      },
    },
    baseHeaderComponents['insurType'],
    baseHeaderComponents['ward'],
    baseHeaderComponents['coder'],
  ],
  // 学科
  settlementMarjorPerfDeptRequiredWithWardCoderAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        allowClear: true,
      },
    },
    baseHeaderComponents['insurType'],
    {
      ...majorPerfDepts,
      required: true,
      props: {
        ...majorPerfDepts.props,
        allowClear: true,
        enableSelectAll: true,
      },
    },
    {
      ...cliDepts,
      // required: true,
      props: {
        ...cliDepts.props,
        allowClear: true,
        enableSelectAll: true,
      },
    },
    // baseHeaderComponents['ward'],
    baseHeaderComponents['coder'],
  ],

  settlementDeptRequiredWithWardCoderAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    {
      ...baseHeaderComponents['constrainedHospital'],
      required: true,
      props: {
        ...baseHeaderComponents['constrainedHospital'].props,
        errorTooltip: true,
      },
    },
    // {
    //   ...baseHeaderComponents['hospitalName'],
    //   required: true,
    //   props: {
    //     ...baseHeaderComponents['hospitalName'].props,
    //     allowClear: true,
    //   },
    // },
    baseHeaderComponents['insurType'],
    // {
    //   ...cliDepts,
    //   required: true,
    //   props: {
    //     ...cliDepts.props,
    //     allowClear: true,
    //     enableSelectAll: true,
    //   },
    // },
    {
      ...constrainedCliDept,
      required: true,
      props: {
        ...constrainedCliDept.props,
        errorTooltip: true,
        allowClear: true,
      },
    },
    // baseHeaderComponents['ward'],
    constrainedWard,
    baseHeaderComponents['coder'],
  ],

  settleMedTeamRequiredWithWardCoderAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        allowClear: true,
      },
    },
    baseHeaderComponents['insurType'],
    {
      ...medTeams,
      required: true,
      props: {
        ...medTeams.props,
        allowClear: true,
        enableSelectAll: true,
      },
    },
    baseHeaderComponents['ward'],
    baseHeaderComponents['coder'],
  ],

  // drg 全院在院监控 用
  drgHospOnly: [
    {
      ...baseHeaderComponents['constrainedHospital'],
      required: true,
      // props: {
      //   ...baseHeaderComponents['hospitalName'].props,
      //   allowClear: true,
      // },
    },
    // {
    //   ...constrainedCliDept,
    //   required: false,
    //   props: {
    //     ...constrainedCliDept.props,
    //     allowClear: true,
    //   },
    // },
    constrainedWard,
    // baseHeaderComponents['ward'],
  ],
  // dip 全院在院监控 用
  hospOnly: [
    {
      ...baseHeaderComponents['constrainedHospital'],
      required: true,
      props: {
        ...baseHeaderComponents['constrainedHospital'].props,
        errorTooltip: true,
      },
      // props: {
      //   ...baseHeaderComponents['hospitalName'].props,
      //   allowClear: true,
      // },
    },
    {
      ...constrainedCliDept,
      required: false,
      props: {
        ...constrainedCliDept.props,
        allowClear: true,
        errorTooltip: true,
      },
    },
    constrainedWard,
    // baseHeaderComponents['ward'],
  ],

  deptOnly: [
    {
      ...baseHeaderComponents['constrainedHospital'],
      required: true,
      props: {
        ...baseHeaderComponents['constrainedHospital'].props,
        errorTooltip: true,
      },
      // props: {
      //   ...baseHeaderComponents['hospitalName'].props,
      //   allowClear: true,
      // },
    },
    {
      ...constrainedCliDept,
      required: true,
      props: {
        ...constrainedCliDept.props,
        allowClear: true,
        errorTooltip: true,
      },
    },
    constrainedWard,
    // baseHeaderComponents['ward'],
  ],

  settlementRequiredWithInsurTypeAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['insurType'],
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
    },
  ],
  // 需要科室但科室非必填
  // **目前只有入组覆盖率分析使用 肿瘤的需要单独处理成学科
  settlementRequiredWithDeptAndInsurTypeAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['insurType'],
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
    },
    // {
    //   ...cliDepts,
    //   props: {
    //     ...cliDepts.props,
    //     allowClear: true,
    //     enableSelectAll: true,
    //   },
    // },
    {
      ...constrainedMajorPerfDepts,
      label: '科室',
      // required: true,
      props: {
        ...constrainedMajorPerfDepts.props,
        allowClear: true,
        enableSelectAll: true,
      },
    },
  ],

  // 学科
  majorPerfDeptSettlementRequiredWithInsurTypeAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['insurType'],
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
    },
    {
      ...majorPerfDepts,
      required: true,
      props: {
        ...majorPerfDepts.props,
        allowClear: true,
        enableSelectAll: true,
      },
    },
  ],
  // 科室
  deptSettlementRequiredWithInsurTypeAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['insurType'],
    // {
    //   ...baseHeaderComponents['hospitalName'],
    //   required: true,
    // },
    { ...baseHeaderComponents['constrainedHospital'], required: true },
    // {
    //   ...cliDepts,
    //   required: true,
    //   props: {
    //     ...cliDepts.props,
    //     allowClear: true,
    //     enableSelectAll: true,
    //   },
    // },
    {
      ...constrainedCliDept,
      required: true,
    },
    constrainedWard,
    // baseHeaderComponents['constrainedCliDept'],
  ],
  // 医疗组必填
  medTeamSettlementRequiredWithInsurTypeAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['insurType'],
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
    },
    {
      ...medTeams,
      required: true,
      props: {
        ...medTeams.props,
        allowClear: true,
        enableSelectAll: true,
      },
    },
  ],

  deptChsSettlementAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['insurType'],
    {
      ...baseHeaderComponents['hospitalName'],
      // required: true,
    },
    {
      ...cliDepts,
      // required: true,
      props: {
        ...cliDepts.props,
        allowClear: true,
        enableSelectAll: true,
      },
    },
  ],

  diffDetailsSettlementAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['hospitalName'],
    {
      ...cliDepts,
      // required: true,
      props: {
        ...cliDepts.props,
        allowClear: true,
        enableSelectAll: true,
      },
    },

    baseHeaderComponents['coder'],
    baseHeaderComponents['uniqueId'],
    baseHeaderComponents['versionedDrgCodes'],
  ],

  qcStandaloneReviewDetail: [
    {
      ...baseHeaderComponents['datePicker'],
      props: {
        ...baseHeaderComponents['datePicker'].props,
        allowClear: false,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        valueKey: 'hospCode',
        allowClear: true,
        mode: 'single',
      },
    },
    cliDepts,
    baseHeaderComponents['errorLevels'],
    baseHeaderComponents['coder'],
  ],
  qcStandaloneReviewProgress: [
    {
      ...baseHeaderComponents['datePicker'],
      props: {
        ...baseHeaderComponents['datePicker'].props,
        allowClear: false,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        valueKey: 'hospCode',
        allowClear: true,
        mode: 'single',
      },
    },
    cliDepts,
    baseHeaderComponents['coder'],
    baseHeaderComponents['errorLevels'],
  ],
  qcStandaloneReviewRules: [
    {
      ...baseHeaderComponents['datePicker'],
      props: {
        ...baseHeaderComponents['datePicker'].props,
        allowClear: false,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        valueKey: 'hospCode',
        allowClear: true,
        mode: 'single',
      },
    },
    cliDepts,
    baseHeaderComponents['coder'],
    // baseHeaderComponents['dateWithType'],
  ],

  qcStandaloneReviewClosedInvalid: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['hospitalName'],
    baseHeaderComponents['errorLevel'],
    // baseHeaderComponents['qualityMonitorLevel'],
    baseHeaderComponents['coder'],
  ],

  settlementAnalysisWithErrorLevel: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['hospitalName'],
    baseHeaderComponents['errorLevel'],
    // baseHeaderComponents['qualityMonitorLevel'],
    baseHeaderComponents['coder'],
  ],
  // 特病单议用
  specialDiseaseSettlementAnalysis: [
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院时间',
    },
    baseHeaderComponents['hospitalName'],
    baseHeaderComponents['insurType'],
  ],
  // 特病单议导入记录用
  specialDiseaseImportRecordAnalysis: [
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院时间',
    },
    {
      label: '申诉类型',
      componentName: 'Select',
      needFetch: false,
      props: {
        dataKey: 'CenterSettleAppealTaskType',
        valueKey: 'TaskType',
        placeholder: '请选择申诉类型',
        optionValueKey: 'Code',
        optionNameKey: 'Name',
        dataSourcePreProcess: (searchValue, dataSource) => {
          return dataSource;
        },
      },
    },
    {
      label: '申诉渠道',
      componentName: 'Select',
      needFetch: false,
      props: {
        dataKey: 'CenterSettleAppealChannel',
        valueKey: 'AppealChannel',
        placeholder: '请选择申诉渠道',
        optionValueKey: 'Code',
        optionNameKey: 'Name',
        dataSourcePreProcess: (searchValue, dataSource) => {
          return dataSource;
        },
      },
    },
    {
      label: '导入状态',
      componentName: 'Select',
      needFetch: false,
      props: {
        dataKey: 'JobResultStatus',
        valueKey: 'Status',
        placeholder: '请选择导入状态',
        optionValueKey: 'Code',
        optionNameKey: 'Name',
        dataSourcePreProcess: (searchValue, dataSource) => {
          return dataSource;
        },
      },
    },
    {
      label: '文件名称',
      componentName: 'Input',
      needFetch: false,
      props: {
        valueKey: 'FileName',
        placeholder: '请填写文件名称',
      },
    },
  ],
  // DIP 分组明细
  dipGroupingDetails: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      label: '结算时间',
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['insurType'],
    baseHeaderComponents['hospitalName'],
    {
      ...cliDepts,
      // required: true,
      props: {
        ...cliDepts.props,
        allowClear: true,
        enableSelectAll: true,
      },
    },
  ],
  reviewDataAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: false,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    baseHeaderComponents['constrainedHospital'],
    constrainedCliDept,
    baseHeaderComponents['coder'],
  ],

  doctorQualityControlHospSettlementAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
    },
    baseHeaderComponents['errorLevel'],
  ],

  doctorQualityControlDeptSettlementAnalysis: [
    {
      ...baseHeaderComponents['radioDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['radioDatePicker']?.props,
        enabledRadioKeys: ['year', 'quarter', 'month'],
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
    },
    {
      ...cliDeptsWithHospitalCode,
      required: true,
    },
    baseHeaderComponents['errorLevel'],
  ],
};
