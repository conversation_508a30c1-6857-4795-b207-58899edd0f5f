import { UniSelect } from '@uni/components/src';
import React, { useEffect, useState } from 'react';
import { useRequest, useModel } from 'umi';
import { uniq, uniqBy } from 'lodash';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { BatchItem, BatchMasterItem } from '@/pages/review/interface';
import { isEmptyValues } from '@uni/utils/src/utils';
import './index.less';
import type { MenuProps } from 'antd';
import { Button, Input, message, Modal, Tooltip, Form, Dropdown } from 'antd';
import { DeleteOutlined, ExportOutlined } from '@ant-design/icons';
import {
  getQualityExamineReviewers,
  ReviewerItem,
} from '@/pages/review/components/batch-create/services';
import { useUpdateEffect } from 'ahooks';
import { commonBusinessDomain } from '@uni/services/src/commonService';
import dayjs from 'dayjs';
import { useRouteProps } from '@uni/commons/src/route-context';
import { RevieweeTypeToLabel } from '@/pages/review/constants';

interface ReviewPeriodSelectProps {
  containerRef?: any;
  batchCreateUpdate?: boolean;
  batchDelete?: boolean;
  batchCoderSelect?: boolean;
  onBatchUpdateClick?: (type?: string) => void;
  onBatchDeleteSuccess?: () => void;
  onBatchCreateDateClick?: () => void;
  onBatchCoderSelect?: (value: string, option: any) => void;
  onBatchKeywordInput?: (value: string) => void;
  currentUserEmployeeCode?: string;
  batchExport?: boolean;
  form?: any;
  isManagement?: boolean;
  searchParams?: any;
  setSearchParams?: (params: any) => void;
  assignments?: any[];
  currentBatchMaster?: any;
}
const externalSearchConfig = (window as any).externalConfig?.['searchConfig'];
const autoClearSearchValue = externalSearchConfig?.['autoClearSearchValue'];

const ReviewPeriodSelect = (props: ReviewPeriodSelectProps) => {
  const {
    searchParams,
    assignments,
    form,
    isManagement,
    setSearchParams,
    currentBatchMaster,
  } = props;
  const [batches, setBatches] = useState<BatchItem[]>([]);

  const [reviewers, setReviewers] = useState<ReviewerItem[]>([]);

  const { examineMasterId } = useRouteProps();

  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateFromMaster',
  );

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      refreshBatches: () => {
        batchesReq();
      },
    };
  });

  useEffect(() => {
    batchesReq();
  }, []);

  useUpdateEffect(() => {
    if (!isEmptyValues(searchParams?.batchInfo?.MasterId)) {
      getDefaultReviewersReq();
    }
  }, [searchParams?.batchInfo?.MasterId]);

  const { loading: getDefaultReviewersLoading, run: getDefaultReviewersReq } =
    useRequest(
      () => {
        return getQualityExamineReviewers(searchParams?.batchInfo?.MasterId);
      },
      {
        manual: true,
        formatResult: (response: RespVO<any>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            setReviewers(response?.data);
            if (!isEmptyValues(props?.currentUserEmployeeCode)) {
              let employItemInData = response?.data?.find(
                (item) => item.EmployCode === props?.currentUserEmployeeCode,
              );
              if (!isEmptyValues(employItemInData)) {
                setSearchParams({
                  ...searchParams,
                  reviewerCodes: [employItemInData?.EmployCode],
                  adminReviewerCode: [employItemInData.EmployCode],
                  // adminReviewerItem: employItemInData,
                });
              } else {
                setSearchParams({
                  ...searchParams,
                  reviewerCodes: [response?.data?.at(0)?.EmployCode],
                  adminReviewerCode: [response?.data?.at(0)?.EmployCode],
                  // adminReviewerItem: response?.data?.at(0),
                });
              }
            }
          } else {
            setReviewers([]);
          }
        },
      },
    );

  const { loading: batchedLoading, run: batchesReq } = useRequest(
    () => {
      let data = {};
      if (!isEmptyValues(examineMasterId)) {
        data['MasterId'] = examineMasterId;
      }
      return uniCommonService('Api/Dmr/DmrCardQualityExamine/GetBatches', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<BatchItem[]>) => {
        let batches = response?.data;
        batches?.forEach((batchInfo) => {
          if (!isEmptyValues(batchInfo?.BatchArgs)) {
            try {
              batchInfo['BatchArgsItem'] = JSON.parse(batchInfo?.BatchArgs);
            } catch (e) {
              batchInfo['BatchArgsItem'] = undefined;
            }
          }
        });
        setBatches(batches);
      },
    },
  );

  const batchesWithDeleteAndExport = (batches: any[]) => {
    return batches?.map((item) => {
      return {
        ...item,
        BatchLabel: (
          <div className={'select-label-container'}>
            <span>{item?.BatchName}</span>
            <div className={'batch-item-operation-container'}>
              <Tooltip className={'item'} title={'删除'}>
                <DeleteOutlined />
              </Tooltip>

              <Tooltip className={'item'} title={'导出'}>
                <ExportOutlined />
              </Tooltip>
            </div>
          </div>
        ),
      };
    });
  };

  const { loading: batchDeleteLoading, run: batchDeleteReq } = useRequest(
    (batchId: string, forceDelete = false) => {
      return uniCommonService('Api/Dmr/DmrCardQualityExamine/DeleteBatch', {
        method: 'POST',
        data: {
          batchId: batchId,
          forceDelete: forceDelete,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          if (response?.data === false) {
            message.error('存在已审核病案，删除失败');
          } else {
            batchesReq();
            props?.onBatchDeleteSuccess && props?.onBatchDeleteSuccess();
          }
        }
      },
    },
  );

  // TODO
  const { loading: batchExportLoading, run: batchExportReq } = useRequest(
    (batchId: string) => {
      return uniCommonService('Api/Dmr/DmrCardQualityExamine/ExportBatch', {
        method: 'POST',
        data: {
          batchId: batchId,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<string>) => {
        return response;
      },
    },
  );

  const onBatchDeleteClick = (batchId: string, batchName: string) => {
    Modal.confirm({
      title: '删除批次',
      content: (
        <>
          <span>确定删除“{batchName}”？</span>
          {/*TODO 强制删除*/}
        </>
      ),
      onOk: () => {
        batchDeleteReq(batchId);
      },
    });
  };

  const batchExport = async () => {
    let batchExportResponse: RespVO<string> = await batchExportReq(
      searchParams?.BatchId,
    );
    if (
      batchExportResponse?.statusCode === 200 &&
      batchExportResponse?.code === 0
    ) {
      message.loading('导出中');
      message.success('导出成功');
      // console.log(backendConfig, res.data?.FileName);
      let a = document.createElement('a');
      a.target = '_blank';
      a.href = `${commonBusinessDomain}/Api/Common/Blob/Download?Id=${
        batchExportResponse?.data
      }&fileName=${searchParams?.batchInfo?.BatchName}-${dayjs().format(
        'YYYYMMDD_HHmmss',
      )}`;
      a.click();
      a.remove();
    } else {
      message.error('导出出现错误，请检查');
    }
  };

  let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');

  const onFinish = (values: any) => {
    console.log(values, 'values');
    const batchOption = batches?.find(
      (item) => item?.BatchId === values?.BatchId,
    );
    // const reviewersOption = reviewers?.filter((item) =>
    //   values?.reviewerCodes?.includes(item?.EmployCode),
    // );
    const params = {
      ...values,
      MasterId: batchOption?.MasterId,
      // 后面参数为了不影响其他代码变量加的，传参非必要
      adminReviewerCode: values.reviewerCodes,
      searchKeyword: values?.Keyword,
      BatchItem: batchOption,
      batchInfo: batchOption,
      // 先用不到
      // adminReviewerItem: reviewersOption,
    };
    setSearchParams(params);
    setQiankunGlobalState({
      ...globalState,
      searchParams: {
        ...(globalState?.searchParams ?? {}),
        ...params,
      },
    });
  };

  useEffect(() => {
    if (form) {
      form.setFieldsValue({
        BatchId: globalState?.searchParams?.BatchItem?.batchId,
        ...searchParams,
      });
    }
  }, [searchParams, form]);

  const items: MenuProps['items'] = [
    props?.batchDelete === true && {
      key: '1',
      label: (
        <a
          type={'primary'}
          onClick={() => {
            onBatchDeleteClick(
              searchParams?.BatchId,
              searchParams?.batchInfo?.BatchName,
            );
          }}
        >
          删除
        </a>
      ),
    },
    props?.batchExport === true &&
      !isEmptyValues(currentBatchMaster?.ExportSettingId) && {
        key: '2',
        label: (
          <a
            type={'primary'}
            loading={batchExportLoading}
            onClick={() => {
              batchExport();
            }}
          >
            导出简报
          </a>
        ),
      },
    props?.batchCreateUpdate === true && {
      key: '3',
      label: (
        <a
          className={'btn'}
          type={'primary'}
          onClick={() => {
            props?.onBatchUpdateClick && props?.onBatchUpdateClick('NEW');
          }}
        >
          新建评审计划
        </a>
      ),
    },
    props?.batchCreateUpdate === true && {
      key: '4',
      label: (
        <a
          className={'btn'}
          type={'primary'}
          onClick={() => {
            props?.onBatchUpdateClick && props?.onBatchUpdateClick('ADJUST');
          }}
        >
          调整当前评审计划
        </a>
      ),
    },
  ].filter(Boolean);

  return (
    <div id={'batch-selector-container'} className={'batch-selector-container'}>
      <Form
        style={{ width: '100%' }}
        form={form}
        layout="inline"
        className={'form'}
        onFinish={onFinish}
      >
        <Form.Item label="评审周期" name="BatchId">
          <UniSelect
            allowClear={false}
            dataSource={batches ?? []}
            placeholder={'请选择评审周期'}
            optionNameKey={'BatchName'}
            optionValueKey={'BatchId'}
            enablePinyinSearch={true}
            getPopupContainer={(triggerNode: any) => {
              return document.getElementById('batch-selector-container');
            }}
          />
        </Form.Item>
        <Form.Item label="病案标识" name="Keyword">
          <Input placeholder={'请输入病案标识'} />
        </Form.Item>
        {(userInfo?.Roles ?? [])?.includes('Admin') && !isManagement && (
          <Form.Item label="评审人" name="reviewerCodes">
            <UniSelect
              placeholder={'请选择评审人'}
              dataSource={reviewers}
              optionValueKey={'EmployCode'}
              optionNameKey={'Name'}
              mode="multiple"
            />
          </Form.Item>
        )}
        <Form.Item label="院区" name="hospCode">
          <UniSelect
            placeholder={'请选择院区'}
            dataSource={globalState?.dictData?.Hospital || []}
            optionValueKey={'Code'}
            optionNameKey={'Name'}
            mode="multiple"
            maxTagCount={'responsive'}
          />
        </Form.Item>
        <Form.Item label="所属学科" name="MajorPerfDepts">
          <UniSelect
            mode={'multiple'}
            maxTagCount={'responsive'}
            placeholder={'请选择学科'}
            dataSource={globalState?.dictData?.MajorPerfDepts || []}
            optionValueKey={'Code'}
            optionNameKey={'Name'}
          />
        </Form.Item>
        <Form.Item label="科室" name="CliDepts">
          <UniSelect
            placeholder={'请选择科室'}
            dataSource={globalState?.dictData?.CliDepts || []}
            optionValueKey={'Code'}
            optionNameKey={'Name'}
            mode="multiple"
            maxTagCount={'responsive'}
            autoClearSearchValue={autoClearSearchValue}
          />
        </Form.Item>
        <Form.Item label="责任医生" name="Doctors">
          <UniSelect
            placeholder={'请选择责任医生'}
            dataSource={
              globalState?.dictData?.Employee?.map((e) => {
                return {
                  ...e,
                  Name: `${e.Name}(${e.ExtraProperties?.Remark ?? '无'})`,
                };
              }) || []
            }
            optionValueKey={'Code'}
            optionNameKey={'Name'}
            mode="multiple"
            maxTagCount={'responsive'}
          />
        </Form.Item>
        <Form.Item label="编码员" name="Coder">
          <UniSelect
            placeholder={'请选择编码员'}
            dataSource={globalState?.dictData?.Coder || []}
            optionValueKey={'Code'}
            optionNameKey={'Name'}
          />
        </Form.Item>
        {isManagement && (
          <>
            <Form.Item label="审核人" name="ReviewerCodes">
              <UniSelect
                placeholder={'请选择审核人'}
                dataSource={uniqBy(assignments, 'ReviewerCode')}
                optionValueKey={'ReviewerCode'}
                optionNameKey={'ReviewerName'}
                mode="multiple"
                maxTagCount={'responsive'}
              />
            </Form.Item>
            {(() => {
              const revieweeType = currentBatchMaster?.RevieweeType;
              const revieweeTypeLabel =
                RevieweeTypeToLabel?.[currentBatchMaster?.RevieweeType];

              return (
                revieweeTypeLabel !== undefined && (
                  <Form.Item
                    label={revieweeTypeLabel}
                    name={`${revieweeType}s`}
                  >
                    <UniSelect
                      placeholder={`请选择${revieweeTypeLabel}`}
                      dataSource={uniq(
                        assignments?.filter(
                          (item) => item?.RevieweeType === revieweeType,
                        ) ?? [],
                        'RevieweeCode',
                      )}
                      optionValueKey="RevieweeCode"
                      optionNameKey="RevieweeName"
                      mode="multiple"
                      maxTagCount="responsive"
                    />
                  </Form.Item>
                )
              );
            })()}
          </>
        )}
      </Form>

      {items.length > 0 ? (
        <Dropdown.Button
          className={'search-btn'}
          style={{ margin: '0px 5px' }}
          type={'primary'}
          htmlType="submit"
          menu={{
            items,
          }}
          onClick={() => {
            form?.submit();
          }}
        >
          查询
        </Dropdown.Button>
      ) : (
        <Button
          className={'search-btn'}
          style={{ margin: '0px 5px' }}
          type={'primary'}
          htmlType="submit"
          onClick={() => {
            form?.submit();
          }}
        >
          查询
        </Button>
      )}
    </div>
  );
};

export default ReviewPeriodSelect;
