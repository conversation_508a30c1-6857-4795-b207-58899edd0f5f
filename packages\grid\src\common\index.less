@import '~@uni/commons/src/style/variables.less';

.grid-stack-item-container {
  height: 100%;
  padding: 5px 10px;
}

.form-item-tooltip-popup-container {
  .ant-tooltip-inner {
    width: 1000px;
  }
}

.form-item-tooltip-container {
  input[disabled] {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
}

.grid-item-container {
  display: flex;
  width: 100% !important;
  //height: 100%;
  flex-direction: row;
  align-items: center;

  div,
  span,
  input,
  label {
    font-size: 16px !important;
  }

  .ant-form-item {
    margin-bottom: 0px !important;
  }

  .prefix,
  .label,
  .suffix {
    white-space: nowrap;
    // font-weight: bold;
    margin-right: 5px;
    color: @label-color;
  }

  .input {
    flex: auto;
  }

  .suffix {
    display: block;
    white-space: nowrap;
    margin-left: 5px;
  }
}

.border-bottom-light {
  // border-bottom: 0px solid @border-color;
  border-bottom: 1px solid transparent;

  // 先行去掉hover
  &:focus,
  &:active {
    //border-bottom: 1px solid @border-color;
    border-bottom: 1px solid transparent;
  }
}

.table-prefix-flex-column {
  display: flex;
  flex-direction: column;
}

.table-prefix-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 10px 8px 10px;
  border-bottom: 1px solid #babfc7;
  width: 100%;
  height: 40px;
  max-height: 40px;

  span {
    font-weight: bold;
    font-size: 14px;
  }
}
