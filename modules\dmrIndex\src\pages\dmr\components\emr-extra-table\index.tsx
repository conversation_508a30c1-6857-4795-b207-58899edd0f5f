import { icdePortalColumns } from '@/pages/dmr/components/icde-oper-portal/columns';
import { UniTable } from '@uni/components/src';
import React, { useContext, useEffect, useLayoutEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import './index.less';
import { resizeContentById } from '@uni/grid/src/core/custom-utils';
import { mergeColumnsInDmrTable } from '@/pages/dmr/utils';
import {
  LeftOutlined,
  RightOutlined,
  CaretRightFilled,
  CaretLeftFilled,
} from '@ant-design/icons';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { Form, Tooltip } from 'antd';
import ReactDOM from 'react-dom';
import { isEmptyValues } from '@uni/utils/src/utils';
import isNumber from 'lodash/isNumber';
import UniTableNG from '@uni/components/src/tanstack-table';
import GridItemContext from '@uni/commons/src/grid-context';
import { useLatest, useUpdateEffect } from 'ahooks';

interface EmrExtraTableProps {
  prefix?: string;
  containerRef: any;
  width?: string | number;
  baseColumns: any[];

  emrColumns: any[];
  dmrTableId: string;
  containerForm: any;
  className?: string;
  tableId?: string;

  emrItemKey: string;

  componentId: string;

  onVisibleChange: (status: boolean) => void;
}

const isFullScreenOpenEmrExtraTable =
  (window as any).externalConfig?.['dmr']?.isFullScreenOpenEmrExtraTable ??
  false;

export const icdeKeyToLabel = {
  IcdeAdms: '入院诊断',
  IcdeDamgs: '损伤中毒',
  IcdeDscgs: '出院诊断',
  IcdeOtps: '门急诊诊断',
  IcdePathos: '病理诊断',
};

const EmrExtraTable = (props: EmrExtraTableProps) => {
  const context = useContext(GridItemContext);

  const [emrExtraStatus, setEmrExtraStatus] = useState(false);

  const [emrSeparatorStatus, setEmrSeparatorStatus] = useState(false);

  const [tableColumns, setTableColumns] = useState<any[]>([]);

  const emrData = Form.useWatch('emrIcdeOperData', props?.containerForm);

  const [form] = Form.useForm();

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      getWidthStyle: () => {
        return emrExtraStatus === true ? widthStyle : '20px';
      },
      getEmrExtraStatus: () => emrExtraStatus,
    };
  });

  useUpdateEffect(() => {
    let status = props?.containerRef?.current?.getEmrExtraStatus();
    if (status === true) {
      resizeContentById(props?.dmrTableId);
    }
  }, [emrData]);

  useLayoutEffect(() => {
    setTimeout(() => {
      setEmrSeparatorStatus(emrExtraStatus);

      let fieldSet = document.getElementById(
        `${props?.dmrTableId}-${props?.tableId ?? 'EMR-FIELDSET'}`,
      );
      if (!isEmptyValues(fieldSet)) {
        fieldSet.style.opacity = emrExtraStatus === true ? '1' : '0';
        fieldSet.style.height = emrExtraStatus === true ? '100%' : '0';
        resizeContentById(props?.dmrTableId);
      }
    }, 300);
  }, [emrExtraStatus]);

  const columnsRenderFromrenderColumnFormItem = (columns: any[]) => {
    return columns?.map((item) => {
      if (item?.renderColumnFormItem !== undefined) {
        item['render'] = (text, record, rowIndex) => {
          let fieldDom = <></>;

          if (record['id'] !== 'ADD') {
            fieldDom = (
              <fieldset disabled={true}>
                <Form.Item
                  name={[record['id'], item?.dataIndex]}
                  {...(item?.formFieldProps ?? {})}
                >
                  {item?.renderColumnFormItem(
                    text,
                    record,
                    rowIndex,
                    item?.dataIndex,
                    form,
                    item,
                  )}
                </Form.Item>
              </fieldset>
            );
          }

          return <>{fieldDom}</>;
        };
      }
      return item;
    });
  };

  const clearIdInInputWhenRender = () => {
    const inputs = document.querySelectorAll(
      `#${props?.dmrTableId}-${props?.tableId ?? 'EMR-DATA'} input`,
    );
    inputs.forEach((input) => {
      input.removeAttribute('id');
    });
  };

  useEffect(() => {
    let columns = mergeColumnsInDmrTable(
      props?.emrColumns,
      props?.baseColumns,
      props?.componentId,
    );

    setTableColumns(columnsRenderFromrenderColumnFormItem(columns));
  }, [props?.emrColumns]);

  // 全屏自动展开医生端表格
  useEffect(() => {
    if (context?.extra?.isFullScreen && isFullScreenOpenEmrExtraTable) {
      setTimeout(() => {
        setEmrExtraStatus(true);
        props?.onVisibleChange && props?.onVisibleChange(true);
      }, 500);
    } else if (
      !context?.extra?.isFullScreen &&
      isFullScreenOpenEmrExtraTable &&
      emrExtraStatus
    ) {
      setTimeout(() => {
        setEmrExtraStatus(false);
        props?.onVisibleChange && props?.onVisibleChange(false);
      }, 500);
    }
  }, [context, props?.onVisibleChange]);

  const tableData = emrData?.[props?.emrItemKey]?.map((item) => {
    if (isEmptyValues(item?.['UniqueId'])) {
      item['UniqueId'] = uuidv4();
    }

    item['id'] = item?.['UniqueId'];
    return item;
  });

  let formValues = {};
  tableData?.forEach((item, index) => {
    formValues[item['id']] = item;
  });
  form.setFieldsValue(formValues);

  console.log('tableData', emrData, props?.emrItemKey);

  clearIdInInputWhenRender();

  const widthStyle = !isEmptyValues(props?.width)
    ? isNumber(props?.width)
      ? `${props?.width}px`
      : props?.width
    : '50%';

  return (
    <div
      className={'emr-table-extra-container dmr-emr-transition-container'}
      style={
        context?.underConfiguration === true
          ? { height: 80, width: `20px`, marginRight: 5 }
          : emrExtraStatus === true
          ? { width: `calc(${widthStyle} - 5px)`, marginRight: 5 }
          : { width: `20px`, marginRight: 5 }
      }
    >
      {emrSeparatorStatus === true &&
        ReactDOM.createPortal(
          <div
            className={'emr-dmr-separator'}
            style={{
              position: 'absolute',
              left: widthStyle,
              zIndex: 98,
            }}
          />,
          document?.getElementById(props?.dmrTableId) ?? document?.body,
        )}
      <fieldset
        id={`${props?.dmrTableId}-${props?.tableId ?? 'EMR-FIELDSET'}`}
        className={
          emrExtraStatus === true ? 'emr-extra-table' : 'emr-extra-table-hidden'
        }
        disabled={true}
        style={{ height: '100%' }}
      >
        {props?.prefix && (
          <div
            className={`table-prefix-container ${
              emrExtraStatus === true ? 'prefix-show' : 'prefix-hidden'
            }`}
          >
            <span>医生端{props?.prefix}</span>
          </div>
        )}

        <Form form={form} style={{ height: '100%', width: '100%' }}>
          <UniTableNG
            id={`${props?.dmrTableId}-${props?.tableId ?? 'EMR-DATA'}`}
            rowKey={'id'}
            formKey={`${props?.dmrTableId}-${props?.tableId ?? 'EMR-DATA'}`}
            loading={false}
            columns={tableColumns}
            dataSource={tableData}
            pagination={false}
            scroll={{
              x: `calc(${widthStyle} - 5px)`,
            }}
          />
        </Form>
      </fieldset>
      <div
        className={'emr-extra-table-switch'}
        onClick={() => {
          if (context?.underConfiguration === true) {
            return;
          }

          let newStatus = !emrExtraStatus;
          setEmrExtraStatus(newStatus);

          props?.onVisibleChange && props?.onVisibleChange(newStatus);
        }}
      >
        <Tooltip
          title={emrExtraStatus === true ? '隐藏医生端数据' : '展开医生端数据'}
          getPopupContainer={() => {
            return document.getElementById('dmr-content-grid-layout');
          }}
        >
          {emrExtraStatus === true ? (
            <CaretLeftFilled style={{ color: '#F6511D' }} />
          ) : (
            <CaretRightFilled style={{ color: '#F6511D' }} />
          )}
        </Tooltip>
      </div>
    </div>
  );
};

export default EmrExtraTable;
