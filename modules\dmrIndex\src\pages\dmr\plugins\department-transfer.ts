import { isEmptyValues } from '@uni/utils/src/utils';
import cloneDeep from 'lodash/cloneDeep';

const departmentTransferWard =
  (window as any).externalConfig?.['dmr']?.departmentTransferWard ?? false;

export const departmentTransferTableResponseProcessorWithWardSplitEnabled = (
  departmentTransferData: any[],
  formFieldValue: any,
) => {
  if (departmentTransferWard === true) {
    let wardColumnsDataSource = departmentTransferData?.filter((item) => {
      return isEmptyValues(item?.InCliDept) && isEmptyValues(item?.OutCliDept);
    });

    let cliDeptColumnsDataSource = departmentTransferData?.filter((item) => {
      return isEmptyValues(item?.InWard) && isEmptyValues(item?.OutWard);
    });

    // TODO 切分 set
    // 科室 转科
    formFieldValue['department-transfer-table'] =
      cliDeptColumnsDataSource?.sort(
        (a, b) => (a?.TransferSort ?? 0) - (b?.TransferSort ?? 0),
      );
    formFieldValue['departmentTransferTable'] = cloneDeep(
      formFieldValue['department-transfer-table']?.slice(),
    );

    // 病区转科
    formFieldValue['department-transfer-table-ward'] =
      wardColumnsDataSource?.sort(
        (a, b) => (a?.TransferSort ?? 0) - (b?.TransferSort ?? 0),
      );
    formFieldValue['departmentTransferWardTable'] = cloneDeep(
      formFieldValue['department-transfer-table-ward']?.slice(),
    );
  }
};

export const departmentTransferTableRequestParamProcessorWithWardSpiltEnabled =
  (formFieldValues: any, data: any, ignoreNull: boolean) => {
    // TODO 合并  set
    if (departmentTransferWard === true) {
      // 科室 转科
      let cliDeptTransferTable = cloneDeep(
        formFieldValues?.['department-transfer-table'] ?? [],
      );
      cliDeptTransferTable
        ?.filter((item) => {
          return (
            !isEmptyValues(item?.InCliDept) &&
            !isEmptyValues(item?.OutCliDept) &&
            !isEmptyValues(item?.TransferInDate) &&
            !isEmptyValues(item?.InDeptHours)
          );
        })
        ?.slice()
        ?.forEach((item, index) => {
          item['TransferSort'] = index + 1;
          item['InDeptHours'] = parseInt(item['InDeptHours'] ?? 0) * 24;
        });

      // 病区转科
      let wardTransferTable = cloneDeep(
        formFieldValues?.['department-transfer-table-ward'] ?? [],
      );
      wardTransferTable
        ?.filter((item) => {
          return (
            !isEmptyValues(item?.InWard) &&
            !isEmptyValues(item?.OutWard) &&
            !isEmptyValues(item?.TransferInDate) &&
            !isEmptyValues(item?.InDeptHours)
          );
        })
        ?.slice()
        ?.forEach((item, index) => {
          item['TransferSort'] = index + 1;
          item['InDeptHours'] = parseInt(item['InDeptHours'] ?? 0) * 24;
        });

      data['CardTransfers'] =
        cliDeptTransferTable?.concat(wardTransferTable) ?? [];
    }
  };
