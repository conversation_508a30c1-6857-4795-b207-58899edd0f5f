import {
  AlertOutlined,
  CheckOutlined,
  CloseOutlined,
  InfoCircleFilled,
  LockFilled,
  LockOutlined,
  UnlockFilled,
  UnlockOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { UniAntdSelect } from '@uni/components/src/index';
import Datepicker from '@uni/components/src/picker/datepicker';
import { ProFormDatePicker } from '@uni/components/src/pro-form';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import { Select, Space, Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';

export const commonColumns = [
  // 第一个是用于审核失败的时候的提示
  {
    dataIndex: 'FrontStatus',
    data: 'FrontStatus',
    title: ' ',
    name: 'FrontStatus',
    visible: true,
    fixed: 'left',
    sorter: false,
    order: -1,
    align: 'center',
    width: 50,
    editable: false,
    render: (text, record) => {
      return (
        <Tooltip title={record?.Remark}>
          <InfoCircleFilled
            className="inpatients_red-color"
            style={{ visibility: record?.Remark ? 'visible' : 'hidden' }}
          />
        </Tooltip>
      );
    },
  },
  // 其他的是状态与锁定
  {
    data: 'StatusName',
    dataIndex: 'StatusName',
    align: 'center',
    orderable: false,
    sorter: false,
    width: 50,
    render: (text, record) => {
      return (
        <Tooltip title={record?.StatusName}>
          {record?.Status === '100' ? (
            <CheckOutlined />
          ) : record?.Status === '1-99' || record?.Status === '99' ? (
            <CloseOutlined />
          ) : (
            <AlertOutlined />
          )}
        </Tooltip>
      );
    },
  },
  {
    data: 'Status',
    dataIndex: 'Status',
    width: 40,
    align: 'center',
    render: (text, record) => {
      return (
        <Tooltip title={record?.StatusName}>
          {record?.Status === '100' ? (
            <CheckOutlined />
          ) : record?.Status === '1-99' || record?.Status === '99' ? (
            <CloseOutlined />
          ) : (
            <AlertOutlined />
          )}
        </Tooltip>
      );
    },
  },
  {
    data: 'IsLocked',
    dataIndex: 'IsLocked',
    align: 'center',
    width: 50,
    render: (text, record) => {
      return (
        <Tooltip title={record?.IsLocked ? '已锁定' : '未锁定'}>
          {record?.IsLocked ? <LockFilled /> : <UnlockFilled />}
        </Tooltip>
      );
    },
  },
];

// 住院动态 按日用
export const InFixedColumnsDaily = (hospList, deptList, dataSource) => [
  ...commonColumns,
  {
    data: 'HospName',
    dataIndex: 'HospName',
    editable: true,
    renderFormItem: (text, props, dom) => {
      console.log(text, props, dom);
      return !props?.record?.Id?.toString()?.includes('new') ? (
        props?.record?.HospName
      ) : (
        <UniAntdSelect
          {...props?.fieldProps}
          placeholder="请先选择院区"
          // disabled={!props?.record?.Id?.toString()?.includes('new')}
          options={hospList}
          fieldNames={{ label: 'Name', value: 'Code' }}
          size="small"
          bordered={false}
          showSearch={true}
          filterOption={(input, option) =>
            (option?.Name ?? '').toLowerCase().includes(input.toLowerCase()) ||
            (option?.Code ?? '').toLowerCase().includes(input.toLowerCase()) ||
            pinyinInitialSearch(
              option?.Name?.toString()?.toLowerCase(),
              input.toLowerCase(),
            )
          }
          enterSwitch={true}
          labelInValue
          onClick={(e) => e.stopPropagation()}
        />
      );
    },
  },
  {
    data: 'DeptName',
    dataIndex: 'DeptName',
    editable: true,
    filterType: 'search',
    width: 200,
    renderFormItem: (text, props, dom) => {
      let options = props?.record?.Id?.toString()?.includes('new')
        ? deptList
            ?.filter(
              (item) =>
                (props?.record?.HospName?.value ?? props?.record?.HospName) ==
                item?.ExtraProperties?.HospCode,
            )
            ?.map((d) => {
              if (dataSource?.findIndex((v) => v.DeptCode === d.Code) > -1) {
                return { ...d, Name: d.Name + '(已存在)', disabled: true };
              } else {
                return d;
              }
            })
        : deptList;
      return !props?.record?.Id?.toString()?.includes('new') ? (
        props?.record?.DeptName
      ) : (
        <UniAntdSelect
          {...props?.fieldProps}
          size="small"
          bordered={false}
          placeholder="请先选择科室"
          disabled={!props?.record?.Id?.toString()?.includes('new')}
          options={options}
          fieldNames={{ label: 'Name', value: 'Code' }}
          showSearch={true}
          filterOption={(input, option) =>
            (option?.Name ?? '').toLowerCase().includes(input.toLowerCase()) ||
            (option?.Code ?? '').toLowerCase().includes(input.toLowerCase()) ||
            pinyinInitialSearch(
              option?.Name?.toString()?.toLowerCase(),
              input.toLowerCase(),
            )
          }
          enterSwitch={true}
          labelInValue
        />
      );
    },
    formItemProps: (form, config) => {
      return {
        rules: [{ required: true }],
      };
    },
  },
];

// 住院动态 科室住院登记columns
export const InFixedColumnsDept = (hospList, disabledDate) => [
  ...commonColumns,
  // {
  //   data: 'HospName',
  //   dataIndex: 'HospName',
  //   renderFormItem: (text, props, dom) => {
  //     return (
  //       <Select
  //         {...props?.fieldProps}
  //         placeholder="请先选择院区"
  //         disabled={!props?.record?.Id?.toString()?.includes('new')}
  //         options={hospList}
  //         fieldNames={{ label: 'Name', value: 'Code' }}
  //         showSearch={true}
  //         filterOption={(input, option) =>
  //           (option?.Name ?? '').toLowerCase().includes(input.toLowerCase()) ||
  //           (option?.Code ?? '').toLowerCase().includes(input.toLowerCase()) ||
  //           pinyinInitialSearch(
  //             option?.Name?.toString()?.toLowerCase(),
  //             input.toLowerCase(),
  //           )
  //         }
  //         labelInValue
  //         onClick={e => e.stopPropagation()}
  //       />
  //     );
  //   },
  // },
  {
    data: 'ExactDate',
    dataIndex: 'ExactDate',
    editable: true,
    width: 150,
    renderFormItem: (text, props, dom) => {
      // console.log(props?.fieldProps, text)
      return !props?.record?.Id?.toString()?.includes('new') ? (
        valueNullOrUndefinedReturnDash(props?.record?.ExactDate, 'Date')
      ) : (
        <ProFormDatePicker
          fieldProps={{
            ...props?.fieldProps,
            disabledDate,
            // disabled: !props?.record?.Id?.toString()?.includes('new'),
            format: 'YYYY-MM-DD',
            size: 'small',
            bordered: false,
          }}
          placeholder="请选择日期"
          // disabledDate={disabledDate}
        />
      );
    },
    formItemProps: (form, config) => {
      return {
        rules: [{ required: true }],
      };
    },
  },
];

// 住院动态 病区按日用
export const InFixedColumnsWardDaily = (hospList, wardList, dataSource) => [
  ...commonColumns,
  {
    data: 'HospName',
    dataIndex: 'HospName',
    editable: true,
    renderFormItem: (text, props, dom) => {
      console.log(text, props, dom);
      return !props?.record?.Id?.toString()?.includes('new') ? (
        props?.record?.HospName
      ) : (
        <UniAntdSelect
          {...props?.fieldProps}
          placeholder="请先选择院区"
          // disabled={!props?.record?.Id?.toString()?.includes('new')}
          options={hospList}
          fieldNames={{ label: 'Name', value: 'Code' }}
          size="small"
          bordered={false}
          showSearch={true}
          filterOption={(input, option) =>
            (option?.Name ?? '').toLowerCase().includes(input.toLowerCase()) ||
            (option?.Code ?? '').toLowerCase().includes(input.toLowerCase()) ||
            pinyinInitialSearch(
              option?.Name?.toString()?.toLowerCase(),
              input.toLowerCase(),
            )
          }
          enterSwitch={true}
          labelInValue
          onClick={(e) => e.stopPropagation()}
        />
      );
    },
  },
  {
    data: 'DeptName',
    dataIndex: 'DeptName',
    editable: true,
    filterType: 'search',
    width: 200,
    renderFormItem: (text, props, dom) => {
      let options = props?.record?.Id?.toString()?.includes('new')
        ? wardList
            ?.filter(
              (item) =>
                (props?.record?.HospName?.value ?? props?.record?.HospName) ==
                item?.ExtraProperties?.HospCode,
            )
            ?.map((d) => {
              if (dataSource?.findIndex((v) => v.DeptCode === d.Code) > -1) {
                return { ...d, Name: d.Name + '(已存在)', disabled: true };
              } else {
                return d;
              }
            })
        : wardList;
      return !props?.record?.Id?.toString()?.includes('new') ? (
        props?.record?.DeptName
      ) : (
        <UniAntdSelect
          {...props?.fieldProps}
          size="small"
          style={{ width: '100%' }}
          bordered={false}
          placeholder="请先选择病区"
          disabled={!props?.record?.Id?.toString()?.includes('new')}
          options={options}
          fieldNames={{ label: 'Name', value: 'Code' }}
          showSearch={true}
          filterOption={(input, option) =>
            (option?.Name ?? '').toLowerCase().includes(input.toLowerCase()) ||
            (option?.Code ?? '').toLowerCase().includes(input.toLowerCase()) ||
            pinyinInitialSearch(
              option?.Name?.toString()?.toLowerCase(),
              input.toLowerCase(),
            )
          }
          enterSwitch={true}
          labelInValue
        />
      );
    },
    formItemProps: (form, config) => {
      return {
        rules: [{ required: true }],
      };
    },
  },
];

export const proofreadDeptCompareColumns = [
  {
    dataIndex: 'DeptCode',
    title: '科室编码',
    name: 'DeptCode',
    visible: false,
  },
  {
    dataIndex: 'DeptName',
    title: '科室',
    name: 'DeptName',
    visible: true,
  },
  {
    dataIndex: 'CardAmt',
    title: '首页数',
    name: 'CardAmt',
    visible: true,
  },
  {
    dataIndex: 'Amt',
    title: '动态数',
    name: 'Amt',
    visible: true,
  },
  {
    dataIndex: 'DiffNum',
    title: '差异数',
    name: 'DiffNum',
    visible: true,
    render: (text, record) => {
      return _.isNumber(text) && text !== 0 ? (
        <span style={{ color: 'red' }}>{text}</span>
      ) : (
        { text }
      );
    },
  },
];

export const proofreadDailyCompareColumns = [
  {
    dataIndex: 'ExactDate',
    title: '日期',
    name: 'ExactDate',
    visible: true,
  },
  {
    dataIndex: 'CardAmt',
    title: '首页数',
    name: 'CardAmt',
    visible: true,
  },
  {
    dataIndex: 'Amt',
    title: '动态数',
    name: 'Amt',
    visible: true,
  },
  {
    dataIndex: 'DiffNum',
    title: '差异数',
    name: 'DiffNum',
    visible: true,
    render: (text, record) => {
      return _.isNumber(text) && text !== 0 ? (
        <span style={{ color: 'red' }}>{text}</span>
      ) : (
        { text }
      );
    },
  },
];
