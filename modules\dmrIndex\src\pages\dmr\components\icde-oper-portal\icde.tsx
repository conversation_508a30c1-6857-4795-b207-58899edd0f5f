import './index.less';
import React, { useEffect, useState } from 'react';
import {
  DndContext,
  PointerSensor,
  useDraggable,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { Coordinates, CSS } from '@dnd-kit/utilities';
import { CloseOutlined, HolderOutlined } from '@ant-design/icons';
import { UniTable } from '@uni/components/src';
import { icdePortalColumns } from '@/pages/dmr/components/icde-oper-portal/columns';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import { isEmptyValues } from '@uni/utils/src/utils';

export const defaultCoordinates = {
  x: 240,
  y: 0,
};

export interface IcdeOperPortalProps {
  containerRef: any;
  hisId: string;
  dictData: any;

  form?: any;
}

const icdeKeyToLabel = {
  IcdeOtps: '门急诊诊断',
  IcdeAdms: '入院诊断',
  IcdeDscgs: '出院诊断',
  IcdeDamgs: '损伤中毒',
  IcdePathos: '病理诊断',
  // // IcdeAdms: '入院诊断',
  // // IcdeDamgs: '损伤中毒',
  // IcdeDscgs: '出院诊断',
  // // IcdeOtps: '门急诊诊断',
  // // IcdePathos: '病理诊断',
};

const dmrIcdeKeyToFormKeys = {
  IcdeOtps: ['IcdeOtpsItem', 'otps-diagnosis-table'],
  IcdeAdms: ['IcdeAdmsItem', 'adms-diagnosis-table'],
  IcdeDscgs: ['diagnosis-table'],
  IcdeDamgs: ['IcdeDamgsItem'],
  TcmIcdeOtpsMain: ['TcmIcdeOtpsMainItem'],
  IcdePathos: ['IcdePathosItem', 'pathological-diagnosis-table'],
};

const IcdeDataPortal = (props: IcdeOperPortalProps) => {
  const [coordinates, setCoordinates] =
    useState<Coordinates>(defaultCoordinates);

  const { attributes, isDragging, listeners, setNodeRef, transform } =
    useDraggable({
      id: 'emr-icde-container',
    });
  const style = {
    transform: CSS.Translate.toString(transform),
  };

  const [icdePortalShow, setIcdePortalShow] = useState<boolean>(false);
  const [dmrIcdeDataShow, setDmrIcdeDataShow] = useState<boolean>(false);

  const [emrIcdeData, setEmrIcdeData] = useState<any[]>([]);
  const [dmrIcdeData, setDmrIcdeData] = useState<any[]>([]);

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      showStatus: (data: any) => {
        setIcdePortalShow(data?.status);
        setDmrIcdeDataShow(data?.dmrDataShow);
        if (data?.status === true && data?.dmrDataShow === false) {
          emrIcdeReq();
        }

        if (data?.status === true && data?.dmrDataShow === true) {
          let formValues = props?.form?.getFieldsValue();
          dmrIcdeDataSetter(formValues);
        }
      },
      updateCoordinates: (data: any) => {
        let currentCoordinates: any = {};
        currentCoordinates['x'] =
          (coordinates['x'] ?? defaultCoordinates['x']) + data['x'];
        currentCoordinates['y'] =
          (coordinates['y'] ?? defaultCoordinates['y']) + data['y'];
        setCoordinates(currentCoordinates);
      },
      updateDmrDataValue: (formValues: any) => {
        if (dmrIcdeDataShow === true && icdePortalShow === true) {
          dmrIcdeDataSetter(formValues);
        }
      },
    };
  });

  const dmrIcdeDataSetter = (formValues: any) => {
    let dmrIcdeData = [];
    let index = 1;
    Object.keys(icdeKeyToLabel)?.forEach((key) => {
      dmrIcdeKeyToFormKeys?.[key]?.forEach((formKey) => {
        let formItemData = formValues?.[formKey];
        if (!isEmptyValues(formItemData)) {
          if (Array.isArray(formItemData)) {
            formItemData.forEach((item) => {
              if (!isEmptyValues(item?.IcdeCode)) {
                dmrIcdeData.push({
                  IcdeLabel: icdeKeyToLabel?.[key],
                  ...item,
                  Sort: index,
                });
              }
              index++;
            });
          } else {
            dmrIcdeData.push({
              IcdeLabel: icdeKeyToLabel?.[key],
              ...formItemData,
              Sort: index,
              IcdeCode:
                formItemData?.IcdeCode ?? formItemData?.[`${key}IcdeCode`],
              IcdeName:
                formItemData?.IcdeName ?? formItemData?.[`${key}IcdeName`],
            });
            index++;
          }
        }
      });
    });
    setDmrIcdeData(dmrIcdeData);
  };

  const { loading: emrIcdeLoading, run: emrIcdeReq } = useRequest(
    () => {
      return uniCommonService(`Api/Dmr/DmrCardBundle/GetEmrIcdeAndOper`, {
        method: 'POST',
        params: {
          hisId: props?.hisId,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          let emrIcdeData = [];
          let index = 1;
          Object.keys(icdeKeyToLabel)?.forEach((key) => {
            response?.data?.[key]?.forEach((item) => {
              emrIcdeData.push({
                IcdeLabel: icdeKeyToLabel?.[key],
                ...item,
                Sort: index,
              });

              index++;
            });
          });
          setEmrIcdeData(emrIcdeData);
        } else {
          setEmrIcdeData([]);
        }
      },
    },
  );

  return (
    <div
      className={'emr-icde-portal-container emr-draggable-container'}
      ref={setNodeRef}
      style={
        {
          display: `${icdePortalShow ? 'flex' : 'none'}`,
          ...style,
          top: `${coordinates?.y}px`,
          left: `${coordinates?.x}px`,
          '--translate-x': `${transform?.x ?? 0}px`,
          '--translate-y': `${transform?.y ?? 0}px`,
        } as React.CSSProperties
      }
    >
      <div className={'emr-data-container'}>
        <div className={`emr-header-container`} {...listeners}>
          <div className={`title-container ${isDragging ? 'grabbing' : ''}`}>
            <HolderOutlined className={'handle'} size={30} />
            <span className={'title'}>
              {dmrIcdeDataShow === true ? '首页' : '医生端'}诊断
            </span>
          </div>
          <CloseOutlined
            onClick={() => {
              setIcdePortalShow(false);
            }}
          />
        </div>

        <UniTable
          id={'icde-portal-table'}
          rowKey={'rowId'}
          scroll={{ y: 300 }}
          loading={emrIcdeLoading}
          columns={icdePortalColumns}
          dataSource={dmrIcdeDataShow === true ? dmrIcdeData : emrIcdeData}
          pagination={false}
        />
      </div>
    </div>
  );
};

export default IcdeDataPortal;
