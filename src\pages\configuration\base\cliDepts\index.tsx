import { UniTable } from '@uni/components/src';
import { cliDeptsDictColumns } from '@/pages/configuration/base/columns';
import { Button, Card, Form, Modal, TableProps, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { cliDeptTypes } from '@/pages/configuration/base/cliDepts/constants';
import { useRequest } from 'umi';
import {
  DictionaryItem,
  RespVO,
  TableColumns,
} from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import './index.less';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { DictionaryItemCompare } from '@/pages/configuration/base/interfaces';
import { CliDeptItem } from '@/pages/configuration/base/interfaces';
import {
  cliDeptsAddEditRequestProcessor,
  cliDeptsTypesProcessor,
} from '@/pages/configuration/base/cliDepts/processor';
import CliDeptItemAdd from '@/pages/configuration/base/cliDepts/components/cliDept-add';
import HierarchyBedIndex from '@/pages/configuration/base/cliDepts/components/hierarchy-bed';
import CliDeptsRelationAdd from '@/pages/configuration/base/cliDepts/components/relation-add';
import { useModel } from '@@/plugin-model/useModel';
import dayjs from 'dayjs';
import { getMinusCompareCollectionsWithDoubleCollections } from '@/pages/configuration/utils';
import cloneDeep from 'lodash/cloneDeep';
import _ from 'lodash';
import { useUpdateEffect } from 'ahooks';
import './index.less';
import UniEditableTable from '@uni/components/src/table/edittable';
import { v4 as uuidv4 } from 'uuid';

interface CliDeptsDictionaryProps {}

interface DmrManagementSummaryItemProps {
  className?: string;
  label?: string;
  count?: number;
  itemKey?: string;
  onClick?: () => any;
}

const DmrManagementSummaryItem = (props: DmrManagementSummaryItemProps) => {
  return (
    <div
      className={`dmr-management-summary-item-container ${props?.className}`}
      onClick={() => {
        props?.onClick && props?.onClick();
      }}
    >
      <span className={'label'}>{props?.label}</span>
    </div>
  );
};

const CliDeptsDictionary = (props?: CliDeptsDictionaryProps) => {
  const moduleGroup = 'Dmr';

  const [form] = Form.useForm();

  const [cliDeptDictionaryEditItemIndex, setCliDeptDictionaryEditItemIndex] =
    useState(undefined);

  const [cliDeptDictionaryAddEdit, setCliDeptDictionaryAddEdit] =
    useState(false);

  const [hierarchyBedIndex, setHierarchyBedIndex] = useState(false);
  const [hierarchyBedItem, setHierarchyBedItem] = useState<any>({});

  // relation
  const [cliDeptsRelationAdd, setCliDeptsRelationAdd] = useState(false);

  // dict data
  const [cliDeptsDataSource, setCliDeptsDataSource] = useState([]);

  // dict compare data
  const [cliDeptsCompareDataSource, setCliDeptsCompareDataSource] = useState(
    [],
  );

  const [cliDeptsTableDataSource, setCliDeptsTableDataSource] = useState([]);

  const [cliDeptsColumns, setCliDeptsColumns] = useState([]);

  const { globalState } = useModel('@@qiankunStateForSlave');

  const hospitalData = globalState?.dictData?.Hospital || [];

  const [frontPagination, setFrontPagination] = useState({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
  });
  // 前端分页OnChange
  const frontTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setFrontPagination({
      ...frontPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });
  };

  useEffect(() => {
    cliDeptsDictionaryColumnsReq();
    cliDeptsDictionaryReq();
  }, []);

  useEffect(() => {
    Emitter.on(
      ConfigurationEvents.CLI_DEPTS_CONFIGURATION_DICTIONARY_EDIT,
      (index) => {
        let currentEditItem = cliDeptsTableDataSource?.at(index);
        console.log(currentEditItem);

        setCliDeptDictionaryEditItemIndex(index);
        if (currentEditItem) {
          setCliDeptDictionaryAddEdit(true);
          form.setFieldsValue({
            ...currentEditItem,
            dates: [
              currentEditItem?.Sdate
                ? dayjs(currentEditItem?.Sdate)
                : undefined,
              currentEditItem?.Edate
                ? dayjs(currentEditItem?.Edate)
                : undefined,
            ],
          });
        }
      },
    );

    return () => {
      Emitter.off(ConfigurationEvents.CLI_DEPTS_CONFIGURATION_DICTIONARY_EDIT);
    };
  }, [cliDeptsTableDataSource]);

  useEffect(() => {
    processCompares(cliDeptsDataSource, cliDeptsCompareDataSource);
  }, [cliDeptsDataSource, cliDeptsCompareDataSource]);

  const processCompares = (
    dictionaries: DictionaryItem[],
    compares: DictionaryItemCompare[],
  ) => {
    let dataSource = [];
    let codeToCompares = {};

    dictionaries.forEach((dictItem) => {
      let codeCompares = compares
        .filter((item) => item.Code === dictItem.Code)
        .map((item) => {
          let codeCompareItem = {
            ...item,
            ...dictItem,
          };

          delete codeCompareItem['span'];
          delete codeCompareItem['index'];

          return codeCompareItem;
        });

      if (codeCompares?.length > 0) {
        codeToCompares[dictItem?.Code] = codeCompares;
      } else {
        codeToCompares[dictItem?.Code] = [dictItem];
      }
    });

    Object.keys(codeToCompares).forEach((key, index) => {
      let codeData = codeToCompares[key];
      if (codeData?.length > 0) {
        codeData[0]['span'] = codeData?.length;
        codeData[0]['index'] = index + 1;
      }
      dataSource.push(...codeData.slice());
    });

    setCliDeptsTableDataSource(
      dataSource.map((item, index) => {
        return {
          ...item,
          actualIndex: index,
          id: uuidv4(),
        };
      }),
    );
  };

  const { loading: cliDeptsDictionaryLoading, run: cliDeptsDictionaryReq } =
    useRequest(
      () => {
        let data = {
          ModuleGroup: moduleGroup,
        };
        return uniCommonService(
          'Api/Sys/HospHierarchySys/GetHierarchyCliDepts',
          {
            method: 'POST',
            data: data,
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<CliDeptItem[]>) => {
          if (response.code === 0 && response?.statusCode === 200) {
            let cliDeptDataSource = response?.data?.slice();
            setCliDeptsDataSource(
              cliDeptDataSource.map((item) => {
                item['CliDeptTypes'] = cliDeptsTypesProcessor(item);
                item['HospName'] = hospitalData?.find(
                  (hospitalItem) => hospitalItem?.Code === item?.HospCode,
                )?.Name;

                return item;
              }),
            );
          } else {
            setCliDeptsDataSource([]);
          }
        },
      },
    );

  const { data: cliDeptsDictionaryColumns, run: cliDeptsDictionaryColumnsReq } =
    useRequest(
      () => {
        return uniCommonService(
          'Api/Sys/HospHierarchySys/GetHierarchyCliDepts',
          {
            method: 'POST',
            headers: {
              'Retrieve-Column-Definitions': 1,
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<TableColumns>) => {
          if (response.code === 0) {
            return response?.data?.Columns;
          } else {
            return [];
          }
        },
      },
    );

  const { loading: cliDeptsUpsertLoading, run: cliDeptsUpsertReq } = useRequest(
    (values) => {
      let data = {};
      if (cliDeptDictionaryEditItemIndex !== undefined) {
        let currentEditItem = cliDeptsTableDataSource?.at(
          cliDeptDictionaryEditItemIndex,
        );
        if (currentEditItem) {
          data = {
            ...currentEditItem,
          };
        }
      }

      data = {
        ...data,
        ...cliDeptsAddEditRequestProcessor(values),
        ModuleGroup: moduleGroup,
      };

      return uniCommonService(
        'Api/Sys/HospHierarchySys/UpsertHierarchyCliDept',
        {
          method: 'POST',
          data: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.statusCode === 200) {
          setCliDeptDictionaryAddEdit(false);
          setCliDeptDictionaryEditItemIndex(undefined);
          cliDeptsDictionaryReq();
        }
      },
    },
  );

  const onCliDeptsDictionaryItemAdd = (values: any) => {
    cliDeptsUpsertReq(values);
  };

  const [dmrCardsSummarySelectedKey, setDmrCardsSummarySelectedKey] =
    useState('999');
  const ref = useRef<any>();
  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);

  const { run: deleteReq } = useRequest(
    (values) => {
      let data = {};
      data = {
        hierarchyId: values.HierarchyId,
      };
      return uniCommonService(`Api/Sys/HospHierarchySys/DeleteHierarchy`, {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('删除成功');
        }
      },
      onSuccess: (response, params) => {
        cliDeptsDictionaryReq();
      },
    },
  );

  // columns 处理
  useEffect(() => {
    if (
      cliDeptsDictionaryColumns?.length > 0 &&
      globalState?.dictData?.Hospital?.length > 0
    ) {
      setCliDeptsColumns(
        tableColumnBaseProcessor(
          cliDeptsDictColumns(globalState?.dictData),
          cliDeptsDictionaryColumns,
        ),
      );
    }
  }, [cliDeptsDictionaryColumns, globalState?.dictData]);

  useEffect(() => {
    if (!cliDeptDictionaryAddEdit) {
      form.resetFields();
    }
  }, [cliDeptDictionaryAddEdit]);

  useEffect(() => {
    Emitter.on(
      ConfigurationEvents.CLI_DEPTS_CONFIGURATION_ITEM_DELETE,
      (data) => {
        if (data?.index > -1) {
          deleteReq(data.record);
        }
      },
    );

    Emitter.on(ConfigurationEvents.HIERARCHY_BED_EDIT, (data) => {
      let currentEditItem = cliDeptsTableDataSource?.at(data.index);
      if (currentEditItem) {
        setHierarchyBedIndex(true);
        setHierarchyBedItem(currentEditItem);
      }
    });

    return () => {
      Emitter.off(ConfigurationEvents.CLI_DEPTS_CONFIGURATION_ITEM_DELETE);
      Emitter.off(ConfigurationEvents.HIERARCHY_BED_EDIT);
    };
  }, [cliDeptsTableDataSource]);

  console.log('cliDeptsColumns', cliDeptsColumns);

  return (
    <>
      <Card
        className={'cli-depts-dictionary-container'}
        title="科室列表"
        extra={
          <>
            <Button
              key="add"
              loading={false}
              onClick={(e) => {
                setCliDeptDictionaryAddEdit(true);
                form.setFieldValue('IsValid', true);
              }}
            >
              新增科室
            </Button>
          </>
        }
      >
        <div className="dmr-management-summary-container d-flex">
          {[{ label: '全部', value: '999' }, ...cliDeptTypes]?.map((item) => {
            return (
              <DmrManagementSummaryItem
                className={
                  item?.value === dmrCardsSummarySelectedKey
                    ? 'card-selected'
                    : ''
                }
                label={item?.label}
                itemKey={item?.value}
                onClick={() => setDmrCardsSummarySelectedKey(item?.value)}
              />
            );
          })}
        </div>
        <UniEditableTable
          actionRef={ref}
          id={`cli-depts-dictionary-table`}
          className={'cli-depts-dictionary-table'}
          rowKey={'id'}
          scroll={{ y: 540, x: 'max-content' }}
          bordered={true}
          dictionaryData={globalState?.dictData}
          widthDetectAfterDictionary
          loading={cliDeptsDictionaryLoading || cliDeptsUpsertLoading}
          columns={cliDeptsColumns}
          value={cliDeptsTableDataSource?.filter((item, index) => {
            if (dmrCardsSummarySelectedKey === '999') {
              return item;
            } else {
              if (item?.CliDeptTypes?.includes(dmrCardsSummarySelectedKey)) {
                return item;
              }
            }
          })}
          clickable={false}
          // pagination={frontPagination}
          // onChange={frontTableOnChange}
          toolBarRender={null}
          recordCreatorProps={false}
          editable={{
            form: form,
            type: 'multiple',
            editableKeys: editableColumnKeys,
            onSave: async (rowKey, data, row) => {
              cliDeptsUpsertReq({
                ...data,
                MajorPerfDeptName: _.isObject(data?.MajorPerfDeptName)
                  ? data?.MajorPerfDeptName?.label
                  : data?.MajorPerfDeptName,
                MajorPerfDept: _.isObject(data?.MajorPerfDeptName)
                  ? data?.MajorPerfDeptName?.value
                  : data?.MajorPerfDept,
                HospName: _.isObject(data?.HospName)
                  ? data?.HospName?.label
                  : data?.HospName,
                HospCode: _.isObject(data?.HospName)
                  ? data?.HospName?.value
                  : data?.HospCode,
              });
            },
            actionRender: (row, config, defaultDoms) => {
              return [defaultDoms.save, defaultDoms.cancel];
            },
            onChange: setEditableColumnKeys,
          }}
        />
      </Card>

      <Modal
        title={`新增科室`}
        open={cliDeptDictionaryAddEdit}
        onOk={async () => {
          form.validateFields().then((values) => {
            onCliDeptsDictionaryItemAdd({
              ...values,
              Sdate:
                values?.dates &&
                dayjs(values?.dates?.at(0))?.format('YYYY-MM-DD'),
              Edate:
                values?.dates &&
                dayjs(values?.dates?.at(1))?.format('YYYY-MM-DD'),
            });
          });
        }}
        onCancel={() => {
          setCliDeptDictionaryAddEdit(false);
          setCliDeptDictionaryEditItemIndex(undefined);
        }}
        destroyOnClose={true}
        getContainer={document.getElementById('cli-depts-dictionary-table')}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <CliDeptItemAdd
          form={form}
          editIndex={cliDeptDictionaryEditItemIndex}
        />
      </Modal>

      <Modal
        title={`${hierarchyBedItem?.Name} 床位数列表`}
        open={hierarchyBedIndex}
        width={800}
        footer={[
          <Button
            type="primary"
            loading={false}
            onClick={() => setHierarchyBedIndex(false)}
          >
            确认
          </Button>,
        ]}
        onOk={async () => {
          setHierarchyBedIndex(false);
        }}
        onCancel={() => {
          setHierarchyBedIndex(false);
        }}
        destroyOnClose={true}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <HierarchyBedIndex data={hierarchyBedItem} />
      </Modal>
    </>
  );
};

export default CliDeptsDictionary;
