.uni-drag-edit-table-container {
  position: relative;
  width: 100%;

  .grab-handle {
    padding: 6px 4px;
    cursor: grab;
  }
  //drag
  .row-dragging {
    border: 1px solid #ccc;
    display: flex;
    flex-direction: row;
  }

  .row-dragging td {
    display: flex;
    align-items: center;
    padding: 12px 15px !important;
    flex: 1;
  }

  .row-dragging .drag-visible {
    visibility: visible;
  }

  .ant-table-body {
    min-height: var(--tableMinHeight);
  }

  .ant-table-body > table {
    position: relative;
  }
}

.grid-comment-custom {
  .ant-table-cell {
    pointer-events: all !important;
    cursor: pointer !important;
  }

  .icde-add-container,
  .operation-add-container {
    pointer-events: all !important;
  }
}
