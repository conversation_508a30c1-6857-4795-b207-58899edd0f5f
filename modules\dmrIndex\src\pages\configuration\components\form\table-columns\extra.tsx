import React from 'react';
import { Form, Modal } from 'antd';
import {
  ProFormDependency,
  ProFormDigit,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@uni/components/src/pro-form';

const extraPropertyComponentMapping = {
  Input: ProFormText,
  Select: ProFormSelect,
  Digit: ProFormDigit,
  Switch: ProFormSwitch,
};

export const TableItemDateSelectProperties = (defaultChecked: boolean) => {
  return [
    {
      key: 'showHours',
      label: '显示小时',
      component: 'Switch',
      fieldProps: {
        defaultChecked: defaultChecked,
      },
    },
    {
      key: 'showMinutes',
      label: '显示分钟',
      component: 'Switch',
      fieldProps: {
        defaultChecked: defaultChecked,
      },
    },
    {
      key: 'showSeconds',
      label: '显示秒数',
      component: 'Switch',
      fieldProps: {
        defaultChecked: defaultChecked,
      },
    },
  ];
};

export const TableItemExtraConfig = {
  OprnOprtBegntime: TableItemDateSelectProperties(true),
  OprnOprtEndtime: TableItemDateSelectProperties(true),
  AnstBegntime: TableItemDateSelectProperties(true),
  AnstEndtime: TableItemDateSelectProperties(true),
  InpoolIcuTime: TableItemDateSelectProperties(true),
  OutIcuTime: TableItemDateSelectProperties(true),
  TransferInDate: TableItemDateSelectProperties(false),
  TransferOutDate: TableItemDateSelectProperties(false),
};

interface TableColumnItemExtraConfigContainerProps {
  containerRef: any;
  form: any;
}

export const TableColumnItemExtraConfigContainer = (
  props: TableColumnItemExtraConfigContainerProps,
) => {
  const [tableColumnItemExtraConfigShow, setTableColumnItemExtraConfigShow] =
    React.useState<boolean>(false);

  const [recordData, setRecordData] = React.useState<any>({});

  const [extraConfigForm] = Form.useForm();

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      showExtraConfig: (payload: any) => {
        setTableColumnItemExtraConfigShow(payload?.status);
        setRecordData(payload?.data);
        extraConfigForm.setFieldsValue(payload?.data?.extraProps ?? {});
      },
    };
  });

  return (
    <Modal
      wrapClassName={'table-column-extra-container'}
      zIndex={1010}
      title={`${recordData?.title} Extra属性修改`}
      open={tableColumnItemExtraConfigShow}
      width={600}
      cancelButtonProps={{
        style: { display: 'none' },
      }}
      onOk={() => {
        props?.form?.setFieldValue(
          [recordData?.id, 'extraProps'],
          extraConfigForm?.getFieldsValue(),
        );
        setTableColumnItemExtraConfigShow(false);
      }}
      onCancel={() => {
        props?.form?.setFieldValue(
          [recordData?.id, 'extraProps'],
          extraConfigForm?.getFieldsValue(),
        );
        setTableColumnItemExtraConfigShow(false);
      }}
      getContainer={() => {
        return document.getElementById('table-columns-edit-table');
      }}
    >
      <Form
        form={extraConfigForm}
        onValuesChange={(changeValues: any, values: any) => {
          props?.form?.setFieldValue([recordData?.id, 'extraProps'], values);
        }}
      >
        <div className={'extra-container-content'}>
          {TableItemExtraConfig?.[recordData?.dataIndex]?.map(
            (propertyItem: any) => {
              const PropertyComponent = extraPropertyComponentMapping[
                propertyItem?.component
              ] as React.FC<any>;

              return (
                <>
                  {propertyItem?.dependencyKey ? (
                    <ProFormDependency name={propertyItem?.dependencyKey}>
                      {(record) => {
                        if (
                          record[propertyItem?.dependencyKey]?.toString() ===
                          propertyItem?.dependencyValue
                        ) {
                          return (
                            <PropertyComponent
                              {...propertyItem}
                              form={props?.form}
                              name={propertyItem?.key}
                              rules={[
                                {
                                  required: propertyItem?.required,
                                  message: `${propertyItem?.label}不能为空`,
                                },
                              ]}
                              width={propertyItem?.width ?? 'md'}
                              label={propertyItem.label}
                              tooltip={propertyItem?.description}
                              placeholder="请输入"
                              fieldProps={propertyItem?.fieldProps}
                            />
                          );
                        }
                        return <></>;
                      }}
                    </ProFormDependency>
                  ) : (
                    <PropertyComponent
                      {...propertyItem}
                      form={props?.form}
                      name={propertyItem?.key}
                      rules={[
                        {
                          required: propertyItem?.required,
                          message: `${propertyItem?.label}不能为空`,
                        },
                        ...(propertyItem?.rules ?? []),
                      ]}
                      width={propertyItem?.width ?? 'md'}
                      label={propertyItem.label}
                      tooltip={propertyItem?.description}
                      placeholder="请输入"
                      fieldProps={{
                        ...propertyItem?.fieldProps,
                        disabled: false,
                      }}
                    />
                  )}
                </>
              );
            },
          )}
        </div>
      </Form>
    </Modal>
  );
};

export const tableExtraColumns = (tableItemExtraConfigContainerRef: any) => [
  {
    dataIndex: 'extra',
    title: 'extra参数',
    visible: true,
    align: 'center',
    width: 80,
    readonly: true,
    renderFormItem: (node, item, index) => {
      if (
        Object.keys(TableItemExtraConfig)?.includes(item?.record?.dataIndex)
      ) {
        return (
          <div className={'table-column-item-extra'}>
            <Form.Item hidden={true} name={[item?.record?.id, 'extraProps']} />
            <span
              onClick={() => {
                tableItemExtraConfigContainerRef?.current?.showExtraConfig({
                  status: true,
                  data: item?.record,
                });
              }}
            >
              设定
            </span>
          </div>
        );
      }

      return (
        <div
          className={'table-column-item-extra table-column-item-extra-disabled'}
        >
          <Form.Item hidden={true} name={[item?.record?.id, 'extraProps']} />
          <span>设定</span>
        </div>
      );
    },
  },
];
