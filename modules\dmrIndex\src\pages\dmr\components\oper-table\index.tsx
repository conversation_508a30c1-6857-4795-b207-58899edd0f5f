import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { UniDmrDragEditOnlyTable, UniDragEditTable } from '@uni/components/src';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { Form, Modal } from 'antd';
import { dmrOperationReport } from '@/pages/dmr/network/save';
import { icdeColumns, operationColumns } from '@/pages/dmr/columns';
import { tableHotKeys } from '@uni/grid/src/common';
import { useHotkeys } from 'react-hotkeys-hook';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import { mergeColumnsInDmrTable } from '@/pages/dmr/utils';
import {
  calculateNextIndex,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
  triggerFormValueChangeEvent,
  waitFocusElementRefocus,
  waitFocusElementRefocusBySelector,
} from '@uni/grid/src/utils';
import { isEmptyValues } from '@uni/utils/src/utils';
import cloneDeep from 'lodash/cloneDeep';
import { getLineArrowUpDownEventKey } from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import { resizeContentById } from '@uni/grid/src/core/custom-utils';
import pick from 'lodash/pick';
import omit from 'lodash/omit';
import {
  filterDuplicatedOperCodesOnComboSelect,
  OperationComboRules,
} from '@/pages/dmr/operation-combo';
import merge from 'lodash/merge';
import mergeWith from 'lodash/mergeWith';
import EmrExtraTable from '@/pages/dmr/components/emr-extra-table';

interface OperDragTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];

  underConfiguration?: boolean;

  onChange?: (value: any) => void;

  emrDataTable?: boolean;
  emrTableWidth?: string | number;
  emrColumns?: any[];
}

export const operTypeToClassName = {
  '1': 'operation-tr-color-diagnosis',
  '2': 'operation-tr-color-treatment',
  '3': 'operation-tr-color-intervention',
  '4': 'operation-tr-color-basic',
};

// TODO 数据 data 改动

const operationCopyKeys =
  (window as any).externalConfig?.['dmr']?.operationCopyKeys ?? [];

const icdeOperFirstMain =
  (window as any).externalConfig?.['dmr']?.icdeOperFirstMain ?? false;

const operCopyFocusKey =
  (window as any).externalConfig?.['dmr']?.operCopyFocusKey ?? undefined;
const operDeleteConfirm =
  (window as any).externalConfig?.['dmr']?.operDeleteConfirm ?? false;

const operationComboDefaultMap =
  (window as any).externalConfig?.['dmr']?.operationComboDefaultMap ?? {};

interface OperationItem {
  OperId?: number;
  id: string | number;

  OperGroupNo?: string;
  OperCode?: string;
  OprnOprtBegntime?: string;
  OprnOprtEndtime?: string;
  OperRate?: string;
  OperRateLabel?: string;
  OperName?: string;
  Operator?: string;
  Firstasst?: string;
  Secondasst?: string;
  WoundHealingRate?: string;
  AnaType?: string;
  AnaTypeLabel?: string;
  AnaDoc?: string;
  OperType?: string;

  WoundHealingRateClass?: string;

  IsReported?: boolean;

  UniqueId?: string;
}
// 复制键集合
const copyKeys = !isEmptyValues(operationCopyKeys)
  ? operationCopyKeys
  : [
      'OperGroupNo',
      'OprnOprtBegntime',
      'OprnConTime',
      'Operator',
      'Firstasst',
      'Secondasst',
      'AnaType',
      'AnstLvCode',
      'AnaDoc',
      'OprnPatnType',
      'OprnOprtEndtime',
      'OperNote',
      'AnstBegntime',
      'AnstEndtime',
      'OperDept',
      'WoundHealingRateClass',
      'OperAccord',
    ];

const hotKeyToEvents = {
  ADD: (event) => {
    Emitter.emit(EventConstant.DMR_OPER_ADD, event.target.id);
  },
  DELETE: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);

    let index = parseInt(indexString);

    if (index > -1) {
      Emitter.emit(EventConstant.DMR_OPER_DELETE, index);
    }
  },
  COPY: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);

    let index = parseInt(indexString);

    if (index > -1) {
      Emitter.emit(EventConstant.DMR_OPER_COPY, index);
    }
  },
  SCROLL_LEFT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#operationTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: -100,
      behavior: 'smooth',
    });
  },
  SCROLL_RIGHT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#operationTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: 100,
      behavior: 'smooth',
    });
  },
  SCROLL_UP: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#operationTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: -50,
      behavior: 'smooth',
    });
  },
  SCROLL_DOWN: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#operationTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: 50,
      behavior: 'smooth',
    });
  },
  UP: (event) => {
    Emitter.emit(getArrowUpDownEventKey('operationTable'), {
      event: event,
      type: 'UP',
      trigger: 'hotkey',
    });
  },
  DOWN: (event) => {
    Emitter.emit(getArrowUpDownEventKey('operationTable'), {
      event: event,
      type: 'DOWN',
      trigger: 'hotkey',
    });
  },
};
// 清除键映射
const clearKeysMap = {
  // 仅用于联动删除使用
  OperCode: [
    'OperName',
    'OperCode',
    'InsurName',
    'InsurCode',
    'HqmsName',
    'HqmsCode',
    'OperExtra',
    'HqmsDegree',
    'DegreeRemark',
    'DrgsDegree',
    'RowClassName',
  ],
};

const OperationDragTable = (props: OperDragTableProps) => {
  const itemRef = React.useRef<any>();

  const operationDragTableRef = useRef(null);

  const [form] = Form.useForm();

  const emrContainerRef = useRef(null);

  const operationDataSource =
    Form.useWatch('operationTable', props?.form) ?? [];

  // const [operationDataSource, setOperationDataSource] = useState<
  //   OperationItem[]
  // >([]);

  const [waitFocusId, setWaitFocusId] = useState<string>(undefined);

  const [tableDataSourceSize, setTableDataSourceSize] = useState<number>(
    operationDataSource?.length,
  );

  const [tableColumns, setTableColumns] = useState<any[]>([]);

  useEffect(() => {
    setTableDataSourceSize(operationDataSource?.length);
  }, [operationDataSource]);

  React.useImperativeHandle(operationDragTableRef, () => {
    return {
      // 处理组合手术选择，包括添加多个手术项目和处理规则
      onOperationComboItemSelect: (
        comboItem: any,
        waitForAddToTableOperationData: any[],
        currentTriggeredRecordId: string,
      ) => {
        console.log(
          'onOperationComboItemSelect',
          comboItem,
          waitForAddToTableOperationData,
        );
        let tableData = props?.form?.getFieldValue('operation-table');

        // 换Index 也就是当前这条
        let currentTriggeredRecordIndex = tableData?.findIndex(
          (item) =>
            item.id?.toString() === currentTriggeredRecordId?.toString(),
        );

        let comboOperationCodes = waitForAddToTableOperationData
          ?.map((item) => {
            return item?.OperCode;
          })
          ?.filter((item) => !isEmptyValues(item));

        //  执行规则
        const operationComboRulesInstance = new OperationComboRules(
          comboOperationCodes,
          currentTriggeredRecordIndex,
          copyKeys,
          ['OperNote'],
        );

        let addTableData = [];
        filterDuplicatedOperCodesOnComboSelect(
          tableData,
          waitForAddToTableOperationData,
        )?.forEach((item: any, index: number) => {
          addTableData.push({
            id: tableData?.length + index + 1,
            IsReported: true,
            UniqueId: uuidv4(),

            // 处理完的数据
            ...item,
          });
        });

        if (!isEmptyValues(addTableData)) {
          let sliceCount = 0;
          if (
            !comboOperationCodes?.includes(
              tableData[currentTriggeredRecordIndex]?.OperCode,
            )
          ) {
            sliceCount = 1;
            tableData[currentTriggeredRecordIndex] = mergeWith(
              {},
              operationComboDefaultMap,
              tableData[currentTriggeredRecordIndex],
              omit(
                operationComboRulesInstance.comboSelectedItemsProcess(
                  tableData,
                  addTableData?.at(0),
                ),
                [
                  'UniqueId',
                  'IsReported',
                  'id',
                  'Id',
                  ...Object.keys(operationComboDefaultMap)?.filter(
                    (key) =>
                      !isEmptyValues(
                        tableData[currentTriggeredRecordIndex]?.[key],
                      ),
                  ),
                ],
              ),
              (objValue, srcValue) => {
                if (!isEmptyValues(srcValue)) {
                  return srcValue;
                }

                return objValue;
              },
            );
          }

          addTableData
            ?.slice(sliceCount)
            ?.forEach((waitForAddTableDataItem: any, index: number) => {
              tableData.splice(
                currentTriggeredRecordIndex + 1 + index,
                0,
                mergeWith(
                  {},
                  operationComboDefaultMap,
                  operationComboRulesInstance.comboSelectedItemsProcess(
                    tableData,
                    waitForAddTableDataItem,
                  ),
                  (objValue, srcValue) => {
                    if (!isEmptyValues(srcValue)) {
                      return srcValue;
                    }

                    return objValue;
                  },
                ),
              );
            });

          // 删除对 医保主手术的 设定 source: DMRDEV-816
          // 手术组套 当且仅当 表格内无主 给第一条主
          // let insurMainOperItem = tableData?.find(
          //   (item) => item?.IsMain === true,
          // );
          // if (isEmptyValues(insurMainOperItem) && tableData?.length > 0) {
          //   tableData[0]['IsMain'] = true;
          // }

          setWaitFocusId(
            `div[id=operationTable] tbody > tr:nth-child(${
              currentTriggeredRecordIndex + 1
            }) > td[dataindex="OperCode"] input`,
          );
          setTableDataSourceSize(0);
          setTimeout(() => {
            setTableDataSourceSize(tableData?.length);
          }, 0);
          props?.form?.setFieldValue('operation-table', cloneDeep(tableData));
          triggerFormValueChangeEvent('operation-table');
        }
      },
    };
  });
  // 行上下移动事件
  const lineUpDownEvents = {
    LINE_UP: (event) => {
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let focusId = undefined;
      if (index === 0) {
        focusId = itemId;
      } else {
        ids[2] = index - 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('operationTable'), {
        oldIndex: index,
        newIndex: index - 1,
        focusId: focusId,
      });
    },
    LINE_DOWN: (event) => {
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let dataSourceSize = props?.form?.getFieldValue('operation-table').length;
      let focusId = undefined;
      if (index === dataSourceSize - 1) {
        focusId = itemId;
      } else {
        ids[2] = index + 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('operationTable'), {
        oldIndex: index,
        newIndex: index + 1,
        focusId: focusId,
      });
    },
  };

  const hotKeyRefs = tableHotKeys?.map((item) => {
    return useHotkeys(
      item.key,
      { ...hotKeyToEvents, ...lineUpDownEvents }?.[item?.type],
      {
        preventDefault: true,
        enabled: true,
        enableOnFormTags: true,
        enableOnContentEditable: true,
      },
    );
  });

  useEffect(() => {
    if (!isEmptyValues(waitFocusId)) {
      setTimeout(() => {
        waitFocusElementRefocusBySelector(waitFocusId);
      }, 100);
    }
  }, [waitFocusId]);

  useEffect(() => {
    let columns = mergeColumnsInDmrTable(
      props?.columns,
      operationColumns,
      'OperationDragTable',
    );

    setTableColumns(columns);
  }, [props?.columns]);

  useEffect(() => {
    // delete事件
    Emitter.on(getDeletePressEventKey('operationTable'), (itemId) => {
      // key 包含 index 和 其他的东西
      console.log('operTable', itemId);
      let itemIds = itemId?.split('#');
      let index = parseInt(itemIds?.at(2));
      let key = itemIds?.at(1);

      let clearKeys = [key];
      if (clearKeysMap[key]) {
        clearKeys = clearKeysMap[key];
      }
      clearValuesByKeys(clearKeys, index);

      setTimeout(() => {
        document.getElementById(itemId)?.focus();
      }, 100);
    });

    Emitter.on(EventConstant.DMR_OPER_ADD, (focusId?: string) => {
      let rowData = {
        id: Math.round(Date.now() / 1000),
        IsReported: true,
        UniqueId: uuidv4(),
      };

      let tableData = props?.form?.getFieldValue('operation-table');

      tableData.splice(tableData.length, 0, rowData);

      // 删除对 医保主手术的 设定 source: DMRDEV-816
      // 吃话： 有主的时候 拖能第一个为主 2025/2/18 13:36:10  eaten by 王总
      if (icdeOperFirstMain) {
        setFirstItemIsMainOper(tableData, true);
      }

      setWaitFocusId(
        `div[id=operationTable] tbody > tr:nth-child(${tableData?.length}) > td input`,
      );
      setTableDataSourceSize(tableData?.length);
      props?.form?.setFieldValue('operation-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('operation-table');
    });

    Emitter.on(EventConstant.DMR_OPER_DELETE, (index) => {
      if (operDeleteConfirm === true) {
        Modal.confirm({
          title: `确定删除第${index + 1} 条手术数据？`,
          content: '',
          onOk: () => {
            onOperationItemDelete(index);
          },
          getContainer: () => document.getElementById('dmr-main-container'),
        });
      } else {
        onOperationItemDelete(index);
      }
    });

    Emitter.on(EventConstant.DMR_OPER_INSURE_MAIN, (data) => {
      let index = data?.index;
      console.warn('DMR_OPER_INSURE_MAIN', data);
      if (index > -1) {
        let tableData = props?.form?.getFieldValue('operation-table');
        console.warn('DMR_OPER_INSURE_MAIN with tableData', data, tableData);

        tableData
          ?.filter((item) => item?.id !== 'ADD')
          .forEach((item, rowDataIndex) => {
            let currentFormValue =
              form.getFieldsValue()?.[item?.id]?.['IsMain'];
            if (index === rowDataIndex) {
              item['IsMain'] = currentFormValue;
              form.setFieldValue([item?.id, 'IsMain'], currentFormValue);

              // 同步勾选 上报
              if (currentFormValue === true) {
                item['IsReported'] = true;
                form.setFieldValue([item?.id, 'IsReported'], true);
              }
            } else {
              item['IsMain'] = false;
              form.setFieldValue([item?.id, 'IsMain'], false);
            }
          });

        props?.form?.setFieldValue('operation-table', cloneDeep(tableData));
        triggerFormValueChangeEvent('operation-table');
      }
    });

    // 处理全选事件
    Emitter.on(`DMR_ROW_SELECTION_SELECT_ALL_operationTable`, (data) => {
      const tableData = props?.form?.getFieldValue('operation-table') || [];
      const validRows = tableData.filter((row) => row.id !== 'ADD');

      validRows.forEach((row, index) => {
        form.setFieldValue([row.id, 'RowSelection'], data.checked);
      });

      // 延迟更新状态，确保表单值已经更新
      setTimeout(() => {
        updateRowSelectionState();
      }, 100);
    });

    // 处理单个行选择变化事件
    Emitter.on(`DMR_ROW_SELECTION_ITEM_CHANGE_operationTable`, (data) => {
      // 延迟更新状态，确保表单值已经更新
      setTimeout(() => {
        updateRowSelectionState();
      }, 150);
    });

    // 处理批量删除事件
    Emitter.on(`DMR_BATCH_DELETE_operationTable`, () => {
      const tableData = props?.form?.getFieldValue('operation-table') || [];
      const validRows = tableData.filter((row) => row.id !== 'ADD');

      // 获取选中的行索引
      const selectedIndexes = [];
      validRows.forEach((row, index) => {
        const rowSelectionValue = form.getFieldValue([row.id, 'RowSelection']);
        if (rowSelectionValue === true) {
          selectedIndexes.push(index);
        }
      });

      if (selectedIndexes.length > 0) {
        // 从后往前删除，避免索引变化问题
        const sortedIndexes = selectedIndexes.sort((a, b) => b - a);
        let newTableData = [...tableData];

        sortedIndexes.forEach((index) => {
          // 删除当前 index 对应的 form 中数据
          form.resetFields([
            newTableData?.at(index)?.Id ?? newTableData?.at(index)?.id,
          ]);
          newTableData.splice(index, 1);
        });

        // TODO 设定主手术为第一个
        if (icdeOperFirstMain && newTableData.length > 0) {
          setFirstItemIsMainOper(newTableData);
        }

        // 更新form
        props?.form?.setFieldValue('operation-table', cloneDeep(newTableData));
        triggerFormValueChangeEvent('operation-table');

        setTableDataSourceSize(newTableData?.length);

        // 延迟更新选择状态
        setTimeout(() => {
          updateRowSelectionState();
        }, 100);
      }
    });

    // 复制一条
    Emitter.on(EventConstant.DMR_OPER_COPY, (payload) => {
      let currentCopyItem = form.getFieldValue(payload?.['id']);
      let index = payload?.index;
      let tableData = props?.form?.getFieldValue('operation-table');

      let copiedItem = {
        id: Math.round(Date.now() / 1000),
        IsReported: true,
        UniqueId: uuidv4(),
      };

      Object.keys(currentCopyItem)?.forEach((key) => {
        if (copyKeys?.includes(key)) {
          copiedItem[key] = currentCopyItem[key];
        }
      });

      tableData.splice(index + 1, 0, copiedItem);

      // 删除对 医保主手术的 设定 source: DMRDEV-816
      // 吃话： 有主的时候 拖能第一个为主 2025/2/18 13:36:10  eaten by 王总
      if (icdeOperFirstMain) {
        setFirstItemIsMainOper(tableData);
      }

      if (!isEmptyValues(operCopyFocusKey)) {
        setWaitFocusId(
          `div[id=operationTable] tbody > tr:nth-child(${
            index + 2
          }) > td input[id*=${operCopyFocusKey}]`,
        );
      } else {
        // 跳到最新一行的第一个上
        setWaitFocusId(
          `div[id=operationTable] tbody > tr:nth-child(${
            index + 2
          }) > td input`,
        );
      }
      setTableDataSourceSize(tableData?.length);

      // setOperationDataSource(tableData);

      // props?.onChange && props?.onChange(tableData);
      props?.form?.setFieldValue('operation-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('operation-table');
    });

    Emitter.on(getArrowUpDownEventKey('operationTable'), (payload) => {
      let type = payload?.type;
      const operationDataSource = props?.form?.getFieldValue('operation-table');
      console.log('payload', payload);
      if (
        payload?.event?.target?.className?.indexOf('ant-select') > -1 &&
        payload?.trigger === 'hotkey'
      ) {
        // 表示是 下拉框 需要定制
        return;
      }

      payload?.event?.stopPropagation();

      let { nextIndex, activePaths } = calculateNextIndex(type);
      if (type === 'UP') {
        if (nextIndex < 0) {
          nextIndex = undefined;
        }
      }

      if (type === 'DOWN') {
        if (nextIndex > operationDataSource?.length - 1) {
          nextIndex = undefined;
        }
      }

      if (nextIndex !== undefined) {
        activePaths[2] = nextIndex.toString();
        document.getElementById(activePaths?.join('#'))?.focus();
      }
    });

    let resizeObserver = null;
    if (itemRef.current) {
      resizeObserver = new ResizeObserver(() => {
        // Do what you want to do when the size of the element changes
        resizeContentById('operationTable');
      });
      resizeObserver.observe(itemRef.current);
    }

    return () => {
      resizeObserver?.disconnect();
      Emitter.off(EventConstant.DMR_OPER_ADD);
      Emitter.off(EventConstant.DMR_OPER_DELETE);
      Emitter.off(EventConstant.DMR_OPER_REPORT);
      Emitter.off(EventConstant.DMR_OPER_INSURE_MAIN);
      Emitter.off(EventConstant.DMR_OPER_COPY);
      Emitter.off(`DMR_ROW_SELECTION_SELECT_ALL_operationTable`);
      Emitter.off(`DMR_ROW_SELECTION_ITEM_CHANGE_operationTable`);
      Emitter.off(`DMR_BATCH_DELETE_operationTable`);

      Emitter.off(getDeletePressEventKey('operationTable'));
      Emitter.off(getArrowUpDownEventKey('operationTable'));
    };
  }, []);
  // 删除表格中的一行数据
  const onOperationItemDelete = (index: number) => {
    if (index > -1) {
      let tableData = props?.form?.getFieldValue('operation-table');

      // 删除当前 index 对应的 form 中数据
      form.resetFields([tableData?.at(index)?.Id ?? tableData?.at(index)?.id]);

      tableData.splice(index, 1);

      // 删除对 医保主手术的 设定 source: DMRDEV-816
      // 吃话： 有主的时候 拖能第一个为主 2025/2/18 13:36:10  eaten by 王总
      if (icdeOperFirstMain) {
        setFirstItemIsMainOper(tableData);
      }

      props?.form?.setFieldValue('operation-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('operation-table');

      // 删除的时候 给出当前那个选中的
      // 当前选中的意思是表格中的上一个存在即是上表格中上一个的最后一个
      // 表格中不存在即写第0个的icdeName 建议写死
      // TODO 当前表格上一个的数据
      let dataItems = tableData?.filter((item) => item?.id !== 'ADD');
      if (dataItems?.length > 0) {
        setWaitFocusId(
          `div[id=operationTable] tbody > tr:nth-child(${
            index >= dataItems.length - 1 ? dataItems.length : index + 1
          }) > td input`,
        );
      }
      setTableDataSourceSize(tableData?.length);
    }
  };

  const columnsProcessor = (columns) => {
    return columns.map((columnItem) => {
      if (!columnItem.onCell) {
        columnItem.onCell = (record, index) => {
          if (record?.id === 'ADD') {
            return {
              colSpan: 0,
            };
          }

          return {};
        };

        if (columnItem.children) {
          columnItem.children = columnsProcessor(columnItem.children);
        }
      }

      return columnItem;
    });
  };
  // 根据键数组清除值
  const clearValuesByKeys = (keys, index) => {
    const operationDataSource = props?.form?.getFieldValue('operation-table');
    let formItemId = operationDataSource?.[index]?.id;
    let currentFormValues = cloneDeep(form.getFieldsValue(true));
    keys?.forEach((key) => {
      currentFormValues[formItemId][key] = '';
    });

    form.setFieldsValue({
      ...currentFormValues,
    });

    // 更新form
    let tableData = props?.form?.getFieldValue('operation-table');
    keys?.forEach((key) => {
      tableData[index][key] = '';
    });
    // props?.onChange && props?.onChange(tableData);
    props?.form?.setFieldValue('operation-table', cloneDeep(tableData));
    triggerFormValueChangeEvent('operation-table');
  };

  const setFirstItemIsMainOper = (tableData: any[], formAdd = false) => {
    // 当且仅当 表格内有主 才给第一条主（并且不是通过添加产生的数据）
    let hasIsMainTrue =
      tableData?.filter((item) => item?.IsMain === true)?.length > 0;
    // 若添加时是第一条手术，则给他设置成主
    if (tableData?.length === 1) {
      console.log('qweeeqwee');
      tableData[0]['IsMain'] = true;
      form.setFieldValue([tableData?.[0]?.id, 'IsMain'], true);
      form.setFieldValue([tableData?.[0]?.id, 'IsReported'], true);
      return;
    }

    // 若没主情况下 只要不是添加进来的都直接返回
    if (hasIsMainTrue === false && !formAdd) {
      return;
    }

    // 若是添加进来的，则默认第一条主
    let firstItem = tableData?.[0];
    tableData?.forEach((item) => {
      item['IsMain'] = false;
      form.setFieldValue([item?.id, 'IsMain'], false);
    });
    if (firstItem) {
      firstItem['IsMain'] = true;
      form.setFieldValue([firstItem?.id, 'IsMain'], true);
      form.setFieldValue([firstItem?.id, 'IsReported'], true);
    }
  };

  // 更新行选择状态的函数
  const updateRowSelectionState = () => {
    const tableData = props?.form?.getFieldValue('operation-table') || [];
    const validRows = tableData.filter((row) => row.id !== 'ADD');

    // 计算选中的行
    const selectedRows = validRows.filter((row, index) => {
      const rowSelectionValue = form.getFieldValue([row.id, 'RowSelection']);
      return rowSelectionValue === true;
    });

    const allSelected =
      selectedRows.length === validRows.length && validRows.length > 0;
    const indeterminate =
      selectedRows.length > 0 && selectedRows.length < validRows.length;
    const hasSelection = selectedRows.length > 0;

    Emitter.emit(`DMR_ROW_SELECTION_STATE_UPDATE_operationTable`, {
      allSelected: allSelected,
      indeterminate: indeterminate,
    });

    // 通知批量删除按钮状态
    Emitter.emit(`DMR_ROW_SELECTION_BATCH_UPDATE_operationTable`, {
      hasSelection: hasSelection,
    });
  };

  // 监听表单值变化以更新选择状态
  useEffect(() => {
    updateRowSelectionState();
  }, [tableDataSourceSize]);

  return (
    <div style={{ display: 'flex', flexDirection: 'row' }}>
      {props?.emrDataTable === true && (
        <EmrExtraTable
          prefix={props?.prefix}
          containerRef={emrContainerRef}
          width={props?.emrTableWidth}
          baseColumns={operationColumns}
          emrColumns={props?.emrColumns}
          dmrTableId={'operationTable'}
          containerForm={props?.form}
          emrItemKey={'Opers'}
          componentId={'OperationDragTable'}
          onVisibleChange={(status: boolean) => {
            console.log('onVisibleChange', status);
            let dmrTable = document.querySelector(
              "#operationTable div[class~='uni-drag-edit-table-container']",
            );
            if (!isEmptyValues(dmrTable)) {
              requestAnimationFrame(() => {
                itemRef.current.style.width = `calc(100% - ${emrContainerRef?.current?.getWidthStyle()} - 5px)`;
              });
            }
          }}
        />
      )}

      <UniDmrDragEditOnlyTable
        containerStyle={{
          width: `calc(100% - ${emrContainerRef?.current?.getWidthStyle()} - 5px)`,
          marginLeft: 5,
        }}
        {...props}
        dmrTableContainerRef={operationDragTableRef}
        formItemContainerClassName={'form-content-item-container'}
        itemRef={mergeRefs([itemRef, ...hotKeyRefs])}
        form={form}
        key={props?.id}
        id={props?.id}
        tableId={props?.id}
        formKey={'operationTable'}
        tableLayout={'auto'}
        forceColumnsUpdate={props?.underConfiguration ?? false}
        scroll={{
          x: 'max-content',
        }}
        pagination={false}
        className={`table-container ${props?.className || ''}`}
        columns={columnsProcessor(tableColumns || [])}
        dataSource={(props?.form?.getFieldValue('operation-table') ?? [])
          ?.filter((item) => item.id !== 'ADD')
          ?.map((item) => {
            if (!item['id']) {
              item['id'] = Math.round(Date.now() / 1000);
            }

            return item;
          })
          ?.concat({
            id: 'ADD',
          })}
        rowKey={'id'}
        onValuesChange={(recordList, changedValues) => {
          // setOperationDataSource(tableData);

          Object.keys(changedValues)?.forEach((key) => {
            let item = changedValues?.[key];
            Object.keys(item)?.forEach((itemKey) => {
              // if (itemKey === 'OperCode') {
              //   // 删除对 医保主手术的 设定 source: DMRDEV-816
              //   // 吃话： 有主的时候 拖能第一个为主 2025/2/18 13:36:10  eaten by 王总
              //   if (icdeOperFirstMain) {
              //     setFirstItemIsMainOper(recordList);
              //   }
              // }
            });
          });

          props?.form?.setFieldValue('operation-table', recordList);
          triggerFormValueChangeEvent('operation-table');
        }}
        onDragExtra={(tableData) => {
          // 删除对 医保主手术的 设定 source: DMRDEV-816
          // 吃话： 有主的时候 拖能第一个为主 2025/2/18 13:36:10  eaten by 王总
          if (icdeOperFirstMain) {
            setFirstItemIsMainOper(tableData);
          }
        }}
        onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
          props?.form?.setFieldValue('operation-table', cloneDeep(tableData));
          // props?.form?.setFieldValue('operationTable', cloneDeep(newTableData));
          if (focusId) {
            setTimeout(() => {
              waitFocusElementRefocus(focusId);
            }, 100);
          }
          triggerFormValueChangeEvent('operation-table');
        }}
        rowClassName={(record, index) => {
          if (record?.OperType) {
            let operTypeClassName = operTypeToClassName?.[record?.OperType];
            if (operTypeClassName) {
              return operTypeClassName;
            }
          }

          return '';
        }}
      />
    </div>
  );
};

export default React.memo(OperationDragTable);
