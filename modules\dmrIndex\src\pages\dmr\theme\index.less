@import './custom/index';

@import 'focus';

#dmr-main-container {
  // 输入框 / 选择框
  & input {
    color: var(--input-font-color);
    font-weight: var(--input-font-weight);
  }

  & .ant-select-selection-item {
    color: var(--input-font-color);
    font-weight: var(--input-font-weight);
  }

  // label
  & .prefix,
  & .suffix,
  & .separate-province-selector-container .label,
  & .suffix-item-container .label,
  & .time-range-item-container .label,
  & .fee-item-container .label,
  & .baby-item-container .label,
  & .medical-payment-container .label,
  & .fee-item-brackets {
    font-weight: var(--form-label-font-weight);
    color: var(--form-label-font-color);
  }

  //& .dmr-content-container {
  & #dmr-content-grid-layout {
    //border: 1px solid var(--form-border-color);
    border: 0px solid var(--form-border-color);
    background: var(--form-background-color);
  }

  & #dmr-header-grid-layout {
    //border: 1px solid var(--form-border-color);
    border-bottom: 1px solid var(--separator-color);
  }

  & .separator-container .separator {
    background: var(--separator-color);
  }

  // 表格内 文字

  & .ant-table .ant-table-thead th[class^='ant-table-cell'] .header-item span,
  & .ant-table .ant-table-header th[class^='ant-table-cell'] {
    color: var(--form-label-font-color);
    font-weight: var(--form-label-font-weight);
  }

  // 只读文字
  & .ant-table tbody[class='ant-table-tbody'] tr .icde-oper-readonly-item {
    color: var(--input-font-color);
    font-weight: var(--input-font-weight);
  }

  // 手术表格
  & #operationTable {
    tr[class*='operation-tr-color-diagnosis'] {
      & .ant-select-selection-item,
      & .dmr-oper-name,
      & .dmr-oper-type,
      & .icde-oper-readonly-item,
      & input,
      & .compact-date-container {
        color: var(--operation-diagnosis-font-color);
      }
    }

    tr[class*='operation-tr-color-treatment'] {
      & .ant-select-selection-item,
      & .dmr-oper-name,
      & .dmr-oper-type,
      & .icde-oper-readonly-item,
      & input,
      & .compact-date-container {
        color: var(--operation-treatment-font-color);
      }
    }

    tr[class*='operation-tr-color-intervention'] {
      & .ant-select-selection-item,
      & .dmr-oper-name,
      & .dmr-oper-type,
      & .icde-oper-readonly-item,
      & input,
      & .compact-date-container {
        color: var(--operation-intervention-font-color);
      }
    }

    tr[class*='operation-tr-color-basic'] {
      & .ant-select-selection-item,
      & .dmr-oper-name,
      & .dmr-oper-type,
      & .icde-oper-readonly-item,
      & input,
      & .compact-date-container {
        color: var(--operation-font-color);
      }
    }
  }

  .dmr-oper-code .ant-select-selection-item {
    font-weight: bold;
  }
}
