import { Reducer, useEffect, useReducer } from 'react';
import { <PERSON><PERSON>, <PERSON>, message } from 'antd';
import { useSafeState } from 'ahooks';
import {
  IReducer,
  ITableState,
  IEditableState,
} from '@uni/reducers/src/interface';
import _ from 'lodash';
import {
  InitTableState,
  TableAction,
  tableReducer,
  tableEditPropsReducer,
  EditableTableAction,
} from '@uni/reducers/src';
import { ReqActionType } from '../constants';
import { useRequest } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { SwagPerfDeptItem } from './interface';
import UniEditableTable from '@uni/components/src/table/edittable';
import { ModalForm } from '@uni/components/src/pro-form';
import ProFormContainer from '@uni/components/src/pro-form-container';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { hierarchyPerfDeptColumns } from './columns';
import { PerfDeptItems } from './formItems';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';

const HierarchyPerfDept = () => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateForSlave');

  const [TableState, TableDispatch] = useReducer<
    Reducer<ITableState<SwagPerfDeptItem>, IReducer>
  >(tableReducer, InitTableState);

  const [EditableState, EditableStateDispatch] = useReducer<
    Reducer<
      IEditableState<SwagPerfDeptItem>,
      IReducer<IEditableState<SwagPerfDeptItem>>
    >
  >(tableEditPropsReducer, {
    value: [],
    editableKeys: [],
  });

  const [formItems, setFormItems] = useSafeState([]);

  const { data: originalColumns, run: employeeConfigurationColumnsReq } =
    useRequest(
      () => {
        return uniCommonService(ReqActionType.GetHierarchyPerfDepts, {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        });
      },
      {
        formatResult: (response: RespVO<TableColumns>) => {
          if (response.code === 0) {
            TableDispatch({
              type: TableAction.columnsChange,
              payload: {
                columns: tableColumnBaseProcessor(
                  hierarchyPerfDeptColumns(dictData?.MajorPerfDepts),
                  response?.data?.Columns,
                ),
              },
            });
          }
        },
      },
    );

  const { loading: dataLoading, run: dataReq } = useRequest(
    () => {
      return uniCommonService(ReqActionType.GetHierarchyPerfDepts, {
        method: 'POST',
        data: {},
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<SwagPerfDeptItem[]>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          TableDispatch({
            type: TableAction.dataChange,
            payload: {
              data: response?.data,
            },
          });
        } else {
          TableDispatch({
            type: TableAction.dataChange,
            payload: {
              data: [],
            },
          });
        }
      },
    },
  );

  // action req
  const { loading: upsertLoading, run: upsertReq } = useRequest(
    (values) => {
      let data = {};

      data = {
        ...values,
      };

      return uniCommonService(ReqActionType.UpsertHierarchyPerfDept, {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.statusCode === 200) {
          message.success('操作成功');
          dataReq();
          return 'done';
        }
        return 'failed';
      },
    },
  );

  useEffect(() => {
    dataReq();
  }, []);

  useEffect(() => {
    if (TableState.data) {
      setFormItems(
        PerfDeptItems(
          TableState.data,
          dictData?.Hospital,
          dictData?.MajorPerfDepts,
        ),
      );
    }
  }, [TableState.data, dictData?.MajorPerfDepts]);

  const { run: deleteReq } = useRequest(
    (values) => {
      let data = {};
      data = {
        hierarchyId: values.HierarchyId,
      };
      return uniCommonService(`Api/Sys/HospHierarchySys/DeleteHierarchy`, {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('删除成功');
        }
      },
      onSuccess: (response, params) => {
        dataReq();
      },
    },
  );

  useEffect(() => {
    Emitter.on(ConfigurationEvents.HIERARCHY_PERFDEPT_DELETE, (data) => {
      if (data?.index > -1) {
        deleteReq(data.record);
      }
    });

    return () => {
      Emitter.off(ConfigurationEvents.HIERARCHY_PERFDEPT_DELETE);
    };
  }, [TableState.data]);

  return (
    <Card
      title="绩效科室列表"
      extra={
        <ModalForm<{
          name: string;
          company: string;
        }>
          title="新增绩效科室"
          trigger={<Button>新增绩效科室</Button>}
          // form={form}
          grid
          layout="horizontal"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          key={'add'}
          autoFocusFirstInput
          modalProps={{
            width: 500,
            destroyOnClose: true,
            onCancel: () => console.log('run'),
          }}
          onFinish={async (values) => {
            const result = await upsertReq(values);
            if (result === 'done') return true;
            return false;
          }}
        >
          <ProFormContainer isModalForm searchOpts={formItems} />
        </ModalForm>
      }
    >
      <UniEditableTable
        id="hierarchy_perf_dept"
        rowKey="HierarchyId"
        loading={dataLoading || false}
        recordCreatorProps={false}
        columns={TableState.columns}
        dataSource={TableState.data}
        editable={{
          type: 'multiple',
          editableKeys: EditableState.editableKeys,
          onSave: async (rowKey, data, row) => {
            let upsertResponse = await upsertReq(
              _.omit(
                { ...data, MajorPerfDept: data?.MajorPerfDeptName },
                'MajorPerfDeptName',
              ),
            );
            if (upsertResponse === 'done') {
              return new Promise((res, rej) => {
                res(true);
              });
            }
            return new Promise((res, rej) => {
              rej(true);
            });
          },
          actionRender: (row, config, defaultDoms) => {
            return [defaultDoms.save, defaultDoms.cancel];
          },
          onChange: (keys, rows) => {
            console.log(keys);
            EditableStateDispatch({
              type: EditableTableAction.editableKeysChange,
              payload: {
                editableKeys: keys,
              },
            });
          },
        }}
      />
    </Card>
  );
};

export default HierarchyPerfDept;
