import { Sorta<PERSON><PERSON>andle } from 'react-sortable-hoc';
import React from 'react';
import { MenuOutlined, PlusCircleTwoTone } from '@ant-design/icons';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import IconBtn from '@uni/components/src/iconBtn';
import { UniSelect } from '@uni/components/src';

export const DmrConfigurationConstants = {
  DMR_CONFIGURATION_COLOR_CHANGE: 'DMR_CONFIGURATION_COLOR_CHANGE',

  DMR_CONFIGURATION_DRAWER_STATUS: 'DMR_CONFIGURATION_DRAWER_STATUS',
  DMR_CONFIGURATION_DRAWER_DATA: 'DMR_CONFIGURATION_DRAWER_DATA',
  DMR_CONFIGURATION_ITEM_DATA_CHANGE: 'DMR_CONFIGURATION_ITEM_DATA_CHANGE',
  DMR_CONFIGURATION_ITEM_LAYOUT_CHANGE: 'DMR_CONFIGURATION_ITEM_LAYOUT_CHANGE',

  DMR_CONFIGURATION_ITEM_LAYOUT_ADD: 'DMR_CONFIGURATION_ITEM_LAYOUT_ADD',

  DMR_CONFIGURATION_MENU_DELETE: 'DMR_CONFIGURATION_MENU_DELETE',
  DMR_CONFIGURATION_MENU_ADD: 'DMR_CONFIGURATION_MENU_ADD',

  DMR_CONFIGURATION_PRE_CHECK_RULE_STATUS:
    'DMR_CONFIGURATION_PRE_CHECK_RULE_STATUS',

  DMR_CONFIGURATION_OPERATORS_EDIT_STATUS:
    'DMR_CONFIGURATION_OPERATORS_EDIT_STATUS',

  DMR_CONFIGURATION_MENU_EDIT: 'DMR_CONFIGURATION_MENU_EDIT',
  DMR_CONFIGURATION_MENU_EDIT_COMPLETE: 'DMR_CONFIGURATION_MENU_EDIT_COMPLETE',

  DMR_CONFIGURATION_MENU_LAYOUT_CHANGE: 'DMR_CONFIGURATION_MENU_LAYOUT_CHANGE',

  DMR_CONFIGURATION_PRE_CHECK_RULES_EDIT:
    'DMR_CONFIGURATION_PRE_CHECK_RULES_EDIT',

  DMR_CONFIGURATION_OPERATORS_EDIT: 'DMR_CONFIGURATION_OPERATORS_EDIT',

  DMR_CONFIGURATION_PRE_CHECK_MODULE_EDIT:
    'DMR_CONFIGURATION_PRE_CHECK_MODULE_EDIT',

  DMR_CONFIGURATION_IMPORT_COMPLETE: 'DMR_CONFIGURATION_IMPORT_COMPLETE',

  DMR_CONFIGURATION_LOADING: 'DMR_CONFIGURATION_LOADING',

  DMR_CONFIGURATION_DATA_PREVIEW: 'DMR_CONFIGURATION_DATA_PREVIEW',

  DMR_CONFIGURATION_THEME_EDIT: 'DMR_CONFIGURATION_THEME_EDIT',

  DMR_CONFIGURATION_RECORD_SELECT_PREVIEW:
    'DMR_CONFIGURATION_RECORD_SELECT_PREVIEW',
};

export const PropertyItemConstants = {
  ProvinceTypeOptions: {
    separate: '下拉框',
    input: '输入框',
  },
  ModelDataGroupOptions: {
    Dmr: '病案首页',
    Insur: '结算清单',
  },
  RelativePositionOptions: {},
  DateCalculateUnitOptions: {
    year: '按年',
    month: '按月',
    day: '按日',
    hour: '按时',
    minute: '按分',
    second: '按秒',
  },
  IcdeSearchTypeOptions: {
    isOtp: '门（急）诊 诊断',
    IsDamg: '损伤中毒诊断',
    IsAdm: '入院诊断',
    IsTcmAdm: '入院中医诊断',
    IsDscg: '出院诊断',
    IsMain: '主诊',
    IsPathoAndMor: '病理和形态学诊断',
    IsPatho: '病理诊断',
    IsMorphology: '肿瘤形态学诊断',
    IsTumor: '肿瘤诊断',
    isTcm: '中医诊断（证候）',
    isTcmMain: '中医诊断',
  },

  // columns
  ColumnAlignOptions: {
    left: '居左',
    right: '居右',
    center: '居中',
  },

  ColumnFixedOptions: {
    left: '左固定',
    right: '右固定',
  },
};

export const nonAddCell = (record, index) => {
  if (record?.id === 'ADD') {
    return {
      colSpan: 0,
    };
  }

  return {};
};

export const DragHandler = (node) => {
  return SortableHandle(() => <div className={'grab-handle'}>{node}</div>);
};

export const menuColumns = (anchorDataSource) => {
  return [
    {
      dataIndex: 'order',
      title: '顺序',
      visible: false,
    },
    {
      dataIndex: 'handle',
      title: '',
      visible: true,
      align: 'center',
      width: 60,
      readonly: true,
      render: (node, record, index) => {
        if (record?.id === 'ADD') {
          return (
            <div
              className={'operation-add-container'}
              onClick={() => {
                // 新增传空对象
                Emitter.emit(
                  DmrConfigurationConstants.DMR_CONFIGURATION_MENU_ADD,
                );
              }}
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          );
        } else {
          if (record?.id !== 'ADD') {
            const SortDragHandler = DragHandler(
              <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />,
            );
            return <SortDragHandler />;
          }
        }
      },
      renderFormItem: (node, record, index) => {
        if (record?.id === 'ADD') {
          return (
            <div
              className={'operation-add-container'}
              onClick={() => {
                // 新增传空对象
                Emitter.emit(
                  DmrConfigurationConstants.DMR_CONFIGURATION_MENU_ADD,
                );
              }}
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          );
        } else {
          if (record?.id !== 'ADD') {
            const SortDragHandler = DragHandler(
              <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />,
            );
            return <SortDragHandler />;
          }
        }
      },
      onCell: (record, index) => {
        if (record?.id === 'ADD') {
          return {
            // colSpan: 18,
            colSpan: 4,
          };
        }

        return {};
      },
    },
    {
      dataIndex: 'focusId',
      title: '菜单标题',
      visible: false,
    },
    {
      dataIndex: 'title',
      title: '菜单标题',
      visible: true,
      valueType: 'text',
      onCell: nonAddCell,
    },
    {
      dataIndex: 'key',
      title: '锚点',
      visible: true,
      renderFormItem: ({ index, entity }) => {
        return (
          <UniSelect
            dataSource={anchorDataSource}
            allowClear={false}
            placeholder={'请选择'}
            value={entity['key']}
            showSearch={true}
            onChange={(value) => {
              Emitter.emit('1');
            }}
          />
        );
      },
      onCell: nonAddCell,
    },
    {
      dataIndex: 'operation',
      title: '操作',
      visible: true,
      align: 'center',
      width: 70,
      renderFormItem: ({ index, entity }) => {
        return (
          <div className={'flex-row-center'}>
            <IconBtn
              style={{ width: '100%' }}
              type="delete"
              openPop={true}
              popOnConfirm={() => {
                Emitter.emit(
                  DmrConfigurationConstants.DMR_CONFIGURATION_MENU_DELETE,
                  index,
                );
              }}
            />
          </div>
        );
      },
      onCell: nonAddCell,
      fixed: 'right',
    },
  ];
};

export const menuLabelMapping = {
  diagnosisTable: {
    label: '诊断表格',
    focusId: 'formItem#IcdeCode#0#IcdeSelect',
  },
  operationTable: {
    label: '手术表格',
    focusId: 'formItem#OperCode#0#OperSelect',
  },
  OutHospital: {
    label: '出院计划',
  },
  pathologicalDiagnosisTable: {
    label: '病理表格',
    focusId: 'formItem#PathologyIcdeCode#0#IcdeSelect',
  },
  admsDiagnosisTable: {
    label: '入院表格',
    focusId: 'formItem#AdmsIcdeCode#0#IcdeSelect',
  },
  otpsDiagnosisTable: {
    label: '门急诊表格',
    focusId: 'formItem#OtpsIcdeCode#0#IcdeSelect',
  },
  icuTable: {
    label: 'ICU表格',
    focusId: 'formItem#InpoolIcuTime#0#CompactInput',
  },
  tcmDiagnosisTable: {
    label: '中医诊断表格',
    focusId: 'formItem#TcmIcdeCode#0#IcdeSelect',
  },
};

export const tableEditColumns = () => [
  {
    key: 'sort',
    title: '',
    visible: true,
    align: 'center',
    width: 60,
    readonly: true,
    renderFormItem: (node, record, index) => {
      return <MenuOutlined />;
    },
  },
];
