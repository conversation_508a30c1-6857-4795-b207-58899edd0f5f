import React, { useEffect, useState } from 'react';
import { Button, Popconfirm } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { Emitter } from '@uni/utils/src/emitter';
import './index.less';

interface BatchDeleteButtonProps {
  tableId?: string;
}

export const BatchDeleteButton = ({
  tableId = 'diagnosisTable',
}: BatchDeleteButtonProps) => {
  const [hasSelection, setHasSelection] = useState(false);

  useEffect(() => {
    const updateButtonState = (data: any) => {
      setHasSelection(data.hasSelection);
    };

    Emitter.on(`DMR_ROW_SELECTION_BATCH_UPDATE_${tableId}`, updateButtonState);

    return () => {
      Emitter.off(`DMR_ROW_SELECTION_BATCH_UPDATE_${tableId}`);
    };
  }, [tableId]);

  const handleBatchDelete = () => {
    Emitter.emit(`DMR_BATCH_DELETE_${tableId}`);
  };

  return (
    <Popconfirm
      title="确定删除选中的诊断数据？"
      onConfirm={handleBatchDelete}
      disabled={!hasSelection}
      getPopupContainer={(triggerNode) =>
        document.getElementById('dmr-content-container')
      }
    >
      <Button
        className="batch-delete-button"
        icon={<DeleteOutlined />}
        size="small"
        disabled={!hasSelection}
        // danger={hasSelection}
      >
        <span className="batch-delete-text">批量删除</span>
      </Button>
    </Popconfirm>
  );
};
