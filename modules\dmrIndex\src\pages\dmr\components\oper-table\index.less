@import '~@uni/commons/src/style/variables.less';
.dmr-container-common .uni-drag-edit-table-container {
  width: 100%;
  .fixed-table {
    table {
      table-layout: fixed !important;
    }
  }

  .auto-table {
    table {
      table-layout: auto !important;
    }
  }

  .operation-input {
    // border: 1px solid @border-color !important;
  }

  .operation-add-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    padding: 8px 8px;

    span {
      margin-right: 10px;
    }
  }

  .ant-select {
    //border: 1px solid @grey-border-color !important;
    border-radius: 2px;
  }

  .ant-select-selection-item {
    color: rgba(0, 0, 0, 0.85) !important;
    text-align: center;
  }

  .compact-date-container {
    border-bottom: none !important;
  }

  .operation-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-evenly;
    a {
      margin: 0px 5px;
    }
  }
}
