import React, { ReactNode, useEffect, useState, useCallback } from 'react';
import './index.less';
import {
  Form,
  InputNumber,
  Modal,
  notification,
  Space,
  Tag,
  Tooltip,
  Card,
} from 'antd';
import {
  departmentTransferColumns,
  departmentTransferWardColumns,
} from '../../common/columns';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { tableHotKeys } from '../../common';
import { useHotkeys } from 'react-hotkeys-hook';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import {
  calculateNextIndex,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
  triggerFormValueChangeEvent,
  waitFocusElementRefocus,
  waitFocusElementRefocusBySelector,
} from '../../utils';
import dayjs from 'dayjs';
import isEqual from 'lodash/isEqual';
import { isEmptyValues } from '@uni/utils/src/utils';
import UniEditableTable from '@uni/components/src/table/edittable';
import cloneDeep from 'lodash/cloneDeep';
import Overflow from 'rc-overflow';
//@ts-ignore
import { useModel } from '@@/plugin-model/useModel';
import { v4 as uuidv4 } from 'uuid';
import GridItemContext from '@uni/commons/src/grid-context';
import { UniDmrDragEditOnlyTable } from '@uni/components/src';
import { mergeColumnsInDepartmentTransferTable } from './utils';
import { useAsyncEffect, useUpdateEffect } from 'ahooks';
import {
  cliDeptDepartmentTransferValidator,
  wardDepartmentTransferValidator,
} from './validator';
import { resizeContentById } from '../../core/custom-utils';

const departmentTransferWard =
  (window as any).externalConfig?.['dmr']?.departmentTransferWard ?? false;

interface DepartmentTransferInputProps {
  index: number;
  disabled?: boolean;

  formKey: string;
  form?: any;

  value?: any;
  onChange?: (value: any) => void;
  inputIdExtra?: string;
}

export const showNotification = (description: string | ReactNode) => {
  let key = uuidv4();
  notification.warning({
    message: '转科表格数据校验不通过',
    description: description,
    key: key,
  });
};

export const DepartmentTransferFieldInput = (
  props: DepartmentTransferInputProps,
) => {
  const [value, setValue] = useState(props?.value);

  const formValue = Form.useWatch(props?.formKey, props?.form);

  useEffect(() => {
    setValue(formValue ?? props?.value);
  }, [formValue, props?.value]);

  return (
    <InputNumber
      id={`formItem#${props?.formKey}#${props?.index}#departmentTransferTable${
        props?.inputIdExtra ?? ''
      }`}
      className={'department-transfer-input'}
      bordered={true}
      value={props?.value}
      min={1}
      precision={0}
      controls={false}
      keyboard={false}
      contentEditable={true}
      disabled={props?.disabled ?? false}
      onChange={(event) => {
        props?.onChange && props?.onChange(event);
      }}
      // onChange={(event) => {
      //   setValue(event);
      // }}
    />
  );
};

interface DepartmentTransferProps {
  form: any;

  formKey?: string;

  onChange?: (value) => any;
}

interface DepartmentTransferTableProps {
  form: any;
  formKey?: string;
  open: boolean;

  onChange?: (value) => any;
  columns?: any[];

  tableType: 'Ward' | 'CliDept';
  scrollY?: number;

  transferTile?: boolean;

  onForceUpdate?: () => void;
}

const DepartmentTransferTable = (props: DepartmentTransferTableProps) => {
  const [form] = Form.useForm();

  console.log('DepartmentTransferTable', props);

  const tableOnlyAddIconTrigger =
    context?.externalConfig?.tableOnlyAddIconTrigger ?? false;

  const tableTypeToItemMap = {
    Ward: {
      defaultColumns: departmentTransferWardColumns(tableOnlyAddIconTrigger),
      uniqueSuffix: 'Ward',
      watchFormKey: 'departmentTransferWardTable',
      editFormKey: 'department-transfer-table-ward',
    },
    CliDept: {
      defaultColumns: departmentTransferColumns(tableOnlyAddIconTrigger),
      uniqueSuffix: 'CliDept',
      watchFormKey: 'departmentTransferTable',
      editFormKey: 'department-transfer-table',
    },
  };

  const hotKeyToEvents = {
    ADD: (event) => {
      Emitter.emit(
        `${EventConstant.DMR_DEPARTMENT_TRANSFER}_${
          tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix
        }`,
        event.target.id,
      );
    },
    SAVE: (event) => {
      Emitter.emit(EventConstant.DMR_DEPARTMENT_TRANSFER_OK, event);
    },
    DELETE: (event) => {
      let id = event?.target?.id;
      let indexString = id?.split('#')?.at(2);

      let index = parseInt(indexString);

      if (index > -1) {
        Emitter.emit(
          `${EventConstant.DMR_DEPARTMENT_TRANSFER_DELETE}_${
            tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix
          }`,
          index,
        );
      }
    },
    SCROLL_UP: (event) => {
      let tableBody = document.querySelector(
        "div[id='formItem#department-transfer'] div[class='ant-table-body']",
      );
      tableBody?.scrollBy({
        top: -50,
        behavior: 'smooth',
      });
    },
    SCROLL_DOWN: (event) => {
      let tableBody = document.querySelector(
        "div[id='formItem#department-transfer'] div[class='ant-table-body']",
      );
      tableBody?.scrollBy({
        top: 50,
        behavior: 'smooth',
      });
    },
    UP: (event) => {
      Emitter.emit(
        getArrowUpDownEventKey(
          `transferTable_${
            tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix
          }`,
        ),
        {
          event: event,
          type: 'UP',
          trigger: 'hotkey',
        },
      );
    },
    DOWN: (event) => {
      console.log('DOWN', event);
      Emitter.emit(
        getArrowUpDownEventKey(
          `transferTable_${
            tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix
          }`,
        ),
        {
          event: event,
          type: 'DOWN',
          trigger: 'hotkey',
        },
      );
    },
    LINE_UP: (event) => {},
    LINE_DOWN: (event) => {},
  };

  const okStatusRef = React.useRef(false);

  const departmentTransferDataSource =
    Form.useWatch(
      tableTypeToItemMap?.[props?.tableType]?.watchFormKey,
      props?.form,
    ) ?? [];

  const [tableDataSourceSize, setTableDataSourceSize] = useState<number>(
    departmentTransferDataSource?.length,
  );

  const [_, setForceUpdate] = useState<number>(0);

  const [waitFocusId, setWaitFocusId] = useState<string>(undefined);

  const [tableColumns, setTableColumns] = useState<any[]>([]);

  const context = React.useContext(GridItemContext);

  useEffect(() => {
    let columns = mergeColumnsInDepartmentTransferTable(
      context?.configurableDataIndex,
      props?.columns,
      tableTypeToItemMap?.[props?.tableType]?.defaultColumns,
    );

    setTableColumns(columns);
  }, [props?.columns]);

  useUpdateEffect(() => {
    props?.onForceUpdate && props?.onForceUpdate();
  }, [tableDataSourceSize]);

  useEffect(() => {
    if (props?.open === true) {
      okStatusRef.current = false;
    }
  }, [props?.open]);

  const onTableDataSourceEmpty = (currentDataSource: any[]) => {
    if (currentDataSource?.length === 0 && okStatusRef?.current === false) {
      let addItem = {
        Id: Math.round(Date.now() / 1000),
      };

      if (props?.form?.getFieldValue(`In${props?.tableType}`)) {
        addItem[`Out${props?.tableType}`] = props?.form?.getFieldValue(
          `In${props?.tableType}`,
        );
      }

      if (props?.form?.getFieldValue('InDate')) {
        if (
          tableColumns?.filter(
            (item) =>
              item?.dataIndex === 'TransferInDate' && item?.visible === true,
          )?.length > 0
        ) {
          addItem['TransferInDate'] = props?.form
            ?.getFieldValue('InDate')
            .slice(0, 10);
        }

        if (
          tableColumns?.filter(
            (item) =>
              item?.dataIndex === 'TransferOutDate' && item?.visible === true,
          )?.length > 0
        ) {
          addItem['TransferOutDate'] = props?.form
            ?.getFieldValue('InDate')
            .slice(0, 10);
        }

        addItem['InDeptHours'] = 1;
      }

      if (addItem?.[`Out${props?.tableType}`]) {
        setWaitFocusId(
          `formItem#In${props?.tableType}#0#Input#departmentTransferTable`,
        );
      } else {
        setWaitFocusId(
          `formItem#Out${props?.tableType}#0#Input#departmentTransferTable`,
        );
      }
      currentDataSource.push(addItem);
    }
  };

  useEffect(() => {
    if (props?.transferTile === true) {
      return;
    }
    // 当为空的时候 沿用原有新增的逻辑
    onTableDataSourceEmpty(departmentTransferDataSource);
    let departmentTransferTableOtherSource = props?.form?.getFieldValue(
      tableTypeToItemMap?.[props?.tableType]?.editFormKey,
    );
    if (
      !isEqual(departmentTransferDataSource, departmentTransferTableOtherSource)
    ) {
      props?.form?.setFieldValue(
        tableTypeToItemMap?.[props?.tableType]?.editFormKey,
        cloneDeep(departmentTransferDataSource),
      );
      setTableDataSourceSize(0);
    }
    setTableDataSourceSize(departmentTransferDataSource?.length);
    setForceUpdate((value) => value + 1);
  }, [departmentTransferDataSource, tableColumns]);

  const inDateDaysCalculator = (data: any, tableData: any[]) => {
    if (data.key === 'TransferInDate') {
      if (data?.index === 0) {
        if (
          tableData?.at(data?.index)?.['TransferInDate'] &&
          props?.form.getFieldValue('InDate')
        ) {
          let currentDate = dayjs(
            tableData?.at(data?.index)?.['TransferInDate'],
          );
          let previousDate = dayjs(props?.form.getFieldValue('InDate'));
          if (currentDate?.isValid() && previousDate?.isValid()) {
            let diffDay = currentDate
              .startOf('day')
              .diff(previousDate.startOf('day'), 'day');
            tableData[data?.index]['InDeptHours'] =
              diffDay > 0 ? diffDay : diffDay === 0 ? 1 : 0;
          }
        }
      } else {
        if (
          tableData?.at(data?.index)?.['TransferInDate'] &&
          tableData?.at(data?.index - 1)?.['TransferInDate']
        ) {
          let currentDate = dayjs(
            tableData?.at(data?.index)?.['TransferInDate'],
          );
          let previousDate = dayjs(
            tableData?.at(data?.index - 1)?.['TransferInDate'],
          );
          if (currentDate?.isValid() && previousDate?.isValid()) {
            let diffDay = currentDate
              .startOf('day')
              .diff(previousDate.startOf('day'), 'day');
            tableData[data?.index - 1]['InDeptHours'] =
              diffDay > 0 ? diffDay : diffDay === 0 ? 1 : 0;
          }
        }
      }
    }
  };

  const outDateDaysCalculator = (data: any, tableData: any[]) => {
    if (data.key === 'TransferOutDate') {
      if (data?.index === 0) {
        if (
          tableData?.at(data?.index)?.['TransferOutDate'] &&
          props?.form.getFieldValue('InDate')
        ) {
          let currentDate = dayjs(
            tableData?.at(data?.index)?.['TransferOutDate'],
          );
          let previousDate = dayjs(props?.form.getFieldValue('InDate'));
          if (currentDate?.isValid() && previousDate?.isValid()) {
            let diffDay = currentDate
              .startOf('day')
              .diff(previousDate.startOf('day'), 'day');
            tableData[data?.index]['InDeptHours'] =
              diffDay > 0 ? diffDay : diffDay === 0 ? 1 : 0;
          }
        }
      } else {
        if (
          tableData?.at(data?.index)?.['TransferOutDate'] &&
          tableData?.at(data?.index - 1)?.['TransferOutDate']
        ) {
          let currentDate = dayjs(
            tableData?.at(data?.index)?.['TransferOutDate'],
          );
          let previousDate = dayjs(
            tableData?.at(data?.index - 1)?.['TransferOutDate'],
          );
          if (currentDate?.isValid() && previousDate?.isValid()) {
            let diffDay = currentDate
              .startOf('day')
              .diff(previousDate.startOf('day'), 'day');
            tableData[data?.index]['InDeptHours'] =
              diffDay > 0 ? diffDay : diffDay === 0 ? 1 : 0;
          }
        }
      }
    }
  };

  useEffect(() => {
    setTimeout(() => {
      waitFocusElementRefocus(waitFocusId);
    }, 100);
  }, [waitFocusId]);

  useEffect(() => {
    Emitter.on(
      getDeletePressEventKey(
        `transferTable_${tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix}`,
      ),
      (itemId) => {
        // key 包含 index 和 其他的东西
        console.log('department-transfer', itemId);
        let itemIds = itemId?.split('#');
        let index = parseInt(itemIds?.at(2));
        let key = itemIds?.at(1);

        clearValuesByKeys([key], index);
      },
    );

    Emitter.on(
      `${EventConstant.DMR_DEPARTMENT_TRANSFER}_${
        tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix
      }`,
      () => {
        let tableData = props?.form?.getFieldValue(
          tableTypeToItemMap?.[props?.tableType]?.editFormKey,
        );

        let addItem = {
          Id: Math.round(Date.now() / 1000),
        };

        if (tableData[tableData.length - 1]?.[`In${props?.tableType}`]) {
          addItem[`Out${props?.tableType}`] =
            tableData[tableData.length - 1]?.[`In${props?.tableType}`];
        }

        tableData.splice(tableData.length, 0, addItem);

        props?.form?.setFieldValue(
          tableTypeToItemMap?.[props?.tableType]?.editFormKey,
          cloneDeep(tableData),
        );

        if (addItem?.[`Out${props?.tableType}`]) {
          setWaitFocusId(
            `formItem#In${props?.tableType}#${
              tableData?.length - 1
            }#Input#departmentTransferTable`,
          );
        } else {
          setWaitFocusId(
            `formItem#Out${props?.tableType}#${
              tableData?.length - 1
            }#Input#departmentTransferTable`,
          );
        }

        setTableDataSourceSize(tableData?.length);
      },
    );

    Emitter.on(
      `${EventConstant.DMR_DEPARTMENT_TRANSFER_INPUT_ADD}_${
        tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix
      }`,
      (data) => {
        let tableData = props?.form?.getFieldValue(
          tableTypeToItemMap?.[props?.tableType]?.editFormKey,
        );

        tableData[data?.index][data.key] = data?.value;

        if (data.key === 'TransferInDate') {
          inDateDaysCalculator(data, tableData);
        }

        if (data.key === 'TransferOutDate') {
          outDateDaysCalculator(data, tableData);
        }

        props?.form?.setFieldValue(
          tableTypeToItemMap?.[props?.tableType]?.editFormKey,
          cloneDeep(tableData),
        );
        setTableDataSourceSize(-1);
        requestAnimationFrame(() => {
          setTableDataSourceSize(tableData?.length);
        });
      },
    );

    Emitter.on(
      `${EventConstant.DMR_DEPARTMENT_TRANSFER_DELETE}_${
        tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix
      }`,
      (index) => {
        let tableData = props?.form?.getFieldValue(
          tableTypeToItemMap?.[props?.tableType]?.editFormKey,
        );
        // if (tableData?.length <= 2) {
        //   return;
        // }
        if (index > -1) {
          tableData.splice(index, 1);

          let previousId = `formItem#Out${props?.tableType}#${
            tableData?.length - 2
          }#departmentTransferTable`;

          if (tableData?.length > 1) {
            setWaitFocusId(previousId);
          } else {
            // setWaitFocusId("department-transfer-modal-content");
            (
              document
                ?.getElementById('department-transfer-modal-content')
                ?.closest('div[class*=ant-modal-wrap]') as any
            )?.focus();
          }

          props?.form?.setFieldValue(
            tableTypeToItemMap?.[props?.tableType]?.editFormKey,
            cloneDeep(tableData),
          );

          requestAnimationFrame(() => {
            setTableDataSourceSize(tableData?.length);
          });
        }
      },
    );

    // 提交
    Emitter.on(
      getArrowUpDownEventKey(
        `transferTable_${tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix}`,
      ),
      (payload) => {
        let type = payload?.type;

        console.log('payload', payload);

        if (
          payload?.event?.target?.className?.indexOf('ant-select') > -1 &&
          payload?.trigger === 'hotkey'
        ) {
          // 表示是 下拉框 需要定制
          return;
        }

        payload?.event?.stopPropagation();

        let { nextIndex, activePaths } = calculateNextIndex(type);
        if (type === 'UP') {
          if (nextIndex < 0) {
            nextIndex = undefined;
          }
        }

        if (type === 'DOWN') {
          if (nextIndex > departmentTransferDataSource?.length - 1) {
            nextIndex = undefined;
          }
        }

        if (nextIndex !== undefined) {
          activePaths[2] = nextIndex.toString();
          document.getElementById(activePaths?.join('#'))?.focus();
        }
      },
    );

    return () => {
      Emitter.off(
        `${EventConstant.DMR_DEPARTMENT_TRANSFER}_${
          tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix
        }`,
      );
      Emitter.off(
        `${EventConstant.DMR_DEPARTMENT_TRANSFER_DELETE}_${
          tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix
        }`,
      );
      Emitter.off(
        `${EventConstant.DMR_DEPARTMENT_TRANSFER_INPUT_ADD}_${
          tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix
        }`,
      );

      Emitter.off(
        getDeletePressEventKey(
          `transferTable_${
            tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix
          }`,
        ),
      );
      Emitter.off(
        getArrowUpDownEventKey(
          `transferTable_${
            tableTypeToItemMap?.[props?.tableType]?.uniqueSuffix
          }`,
        ),
      );
    };
  }, [tableColumns]);

  const borderMissingItem = (id: string, queryKeys: string[]) => {
    let lineElement = document.querySelector(`tr[data-row-key='${id}']`);
    if (lineElement) {
      let borderErrorElements = lineElement.querySelectorAll(
        queryKeys.join(','),
      );
      if (borderErrorElements) {
        let errorElements = [].slice.call(borderErrorElements);
        errorElements?.forEach((errorElement) => {
          errorElement?.closest('td > div')?.classList?.add('border-error');
        });
      }
    }
  };

  const clearValuesByKeys = (keys, index) => {
    const icdeDataSource = props?.form?.getFieldValue(
      tableTypeToItemMap?.[props?.tableType]?.editFormKey,
    );
    let formItemId = icdeDataSource?.[index]?.id;
    let currentFormValues = cloneDeep(form.getFieldsValue(true));
    keys?.forEach((key) => {
      currentFormValues[formItemId][key] = '';
    });

    form.setFieldsValue({
      ...currentFormValues,
    });

    // 更新form
    let tableData = props?.form?.getFieldValue(
      tableTypeToItemMap?.[props?.tableType]?.editFormKey,
    );
    keys?.forEach((key) => {
      tableData[index][key] = '';
    });
    // props?.onChange && props?.onChange(tableData);
    props?.form?.setFieldValue(
      tableTypeToItemMap?.[props?.tableType]?.editFormKey,
      cloneDeep(tableData),
    );
  };

  const itemRef = React.useRef<any>();

  const hotKeyRefs = tableHotKeys?.map((item) => {
    return useHotkeys(item.key, hotKeyToEvents[item?.type], {
      preventDefault: true,
      enabled: true,
      enableOnFormTags: true,
      enableOnContentEditable: true,
    });
  });

  return (
    <div
      ref={mergeRefs([itemRef, ...hotKeyRefs])}
      id={`department-transfer-modal-content-${props?.tableType}`}
    >
      <UniDmrDragEditOnlyTable
        {...props}
        style={{
          '--tableMinHeight': `${props?.scrollY}px`,
        }}
        formItemContainerClassName={'form-content-item-container'}
        itemRef={mergeRefs([itemRef, ...hotKeyRefs])}
        form={form}
        key={`department-transfer-${props?.tableType}`}
        id={`department-transfer-${props?.tableType}`}
        tableId={`departmentTransferTable_${props?.tableType}`}
        formKey={`departmentTransferTable_${props?.tableType}`}
        scroll={{
          x: 'max-content',
          y: props?.scrollY ?? 376,
        }}
        pagination={false}
        className={`table-container`}
        dataSource={(
          props?.form?.getFieldValue(
            tableTypeToItemMap?.[props?.tableType]?.editFormKey,
          ) ?? []
        )
          ?.filter((item) => item?.id !== 'ADD')
          ?.map((item) => {
            if (!item['id']) {
              item['id'] = Math.round(Date.now() / 1000);
            }

            return item;
          })
          ?.concat({
            id: 'ADD',
          })}
        rowKey={'id'}
        onValuesChange={(recordList, changedValues) => {
          props?.form?.setFieldValue(
            tableTypeToItemMap?.[props?.tableType]?.editFormKey,
            recordList,
          );
          triggerFormValueChangeEvent(
            tableTypeToItemMap?.[props?.tableType]?.editFormKey,
          );
        }}
        onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
          props?.form?.setFieldValue(
            tableTypeToItemMap?.[props?.tableType]?.editFormKey,
            cloneDeep(tableData),
          );
          if (focusId) {
            setTimeout(() => {
              waitFocusElementRefocus(focusId);
            }, 100);
          }
        }}
        columns={tableColumns}
      />

      {/*<UniEditableTable*/}
      {/*  id={'department-transfer'}*/}
      {/*  size={'small'}*/}
      {/*  rowKey={'Id'}*/}
      {/*  scroll={{ y: 330 }}*/}
      {/*  pagination={false}*/}
      {/*  columns={tableColumns}*/}
      {/*  value={departmentTransferDataSource}*/}
      {/*  bordered={true}*/}
      {/*  clickable={false}*/}
      {/*  recordCreatorProps={false}*/}
      {/*  editable={{*/}
      {/*    form: form,*/}
      {/*    type: 'multiple',*/}
      {/*    editableKeys,*/}
      {/*    actionRender: (row, config, defaultDoms) => {*/}
      {/*      return [];*/}
      {/*    },*/}
      {/*    onValuesChange: (record, recordList) => {*/}
      {/*      console.error('table onValuesChange', record, recordList);*/}
      {/*    },*/}
      {/*    onChange: setEditableRowKeys,*/}
      {/*  }}*/}
      {/*/>*/}
    </div>
  );
};

export const DepartmentTransfer = (props: DepartmentTransferProps) => {
  const [departmentTransferAdd, setDepartmentTransferAdd] = useState(false);

  const [, setForceUpdate] = useState(0);

  const forceUpdate = useCallback(() => {
    setForceUpdate((prevState) => prevState + 1);
  }, []);

  const itemRef = React.useRef<any>();

  const context = React.useContext(GridItemContext);

  const departmentTransferDataSource =
    Form.useWatch('departmentTransferTable', props?.form) ?? [];

  const departmentTransferWardDataSource =
    Form.useWatch('departmentTransferWardTable', props?.form) ?? [];

  // TODO BORDERMISSING ITEM
  const borderMissingItem = (errorElements: any[]) => {
    errorElements?.forEach((item) => {
      item
        ?.closest('.form-content-item-container')
        ?.classList?.add('border-error');
    });
  };

  useEffect(() => {
    Emitter.on(EventConstant.DMR_DEPARTMENT_TRANSFER_CLOSE, () => {
      setDepartmentTransferAdd(false);
    });

    // TODO 改造 OK 用来 设定所有数据
    Emitter.on(EventConstant.DMR_DEPARTMENT_TRANSFER_OK, (event) => {
      // TODO 要做验证
      let mergedValidFlag = true;
      let mergedInValidQueryKeys = [];

      triggerFormValueChangeEvent('department-transfer-table');

      let departmentTransfers = props?.form?.getFieldValue(
        'department-transfer-table',
      );

      let cliDeptValidateResult =
        cliDeptDepartmentTransferValidator(departmentTransfers);
      if (cliDeptValidateResult?.valid === false) {
        mergedValidFlag = false;
        mergedInValidQueryKeys.push(...cliDeptValidateResult?.invalidQueryKeys);
      }

      if (departmentTransferWard === true) {
        let departmentTransferWards = props?.form?.getFieldValue(
          'department-transfer-table-ward',
        );

        let wardValidateResult = wardDepartmentTransferValidator(
          departmentTransferWards,
        );

        if (wardValidateResult?.valid === false) {
          mergedValidFlag = false;
          mergedInValidQueryKeys.push(...wardValidateResult?.invalidQueryKeys);
        } else {
          props?.form?.setFieldValue(
            'departmentTransferWardTable',
            departmentTransferWards,
          );
        }
      }

      if (mergedValidFlag === false) {
        showNotification('请检查转科信息表格是否都已填写');

        let firstErrorElement = null;
        if (mergedInValidQueryKeys?.length > 0) {
          firstErrorElement = mergedInValidQueryKeys?.at(0);
        }

        if (!isEmptyValues(firstErrorElement)) {
          firstErrorElement?.focus();
          firstErrorElement?.click();
        }

        borderMissingItem(mergedInValidQueryKeys);
        return;
      }

      // dataSource
      props?.form?.setFieldValue(
        'departmentTransferTable',
        departmentTransfers,
      );

      Emitter.emit(EventConstant.DMR_DEPARTMENT_TRANSFER_CLOSE);

      event['target']['id'] = 'department-transfer-container';
      Emitter.emit(EventConstant.DMR_TABLE_NEXT_KEY, {
        event: event,
        indexOffset: 1,
      });
    });

    let resizeObserver = null;
    if (props?.transferTile === true) {
      if (itemRef.current) {
        resizeObserver = new ResizeObserver(() => {
          // Do what you want to do when the size of the element changes
          resizeContentById('TransDept');
        });
        resizeObserver.observe(itemRef.current);
      }
    }

    return () => {
      if (props?.transferTile === true) {
        resizeObserver?.disconnect();
      }

      Emitter.off(EventConstant.DMR_DEPARTMENT_TRANSFER_CLOSE);
      Emitter.off(EventConstant.DMR_DEPARTMENT_TRANSFER_OK);
    };
  }, []);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const renderItem = (item: any) => {
    return <Tag>{item.Name}</Tag>;
  };

  const renderRest = (items: any[]) => {
    return (
      <Tooltip
        title={<Space split=",">{items.map((item) => item.Name)}</Space>}
        getPopupContainer={(triggerNode) =>
          document.getElementById('dmr-content-grid-layout')
        }
      >
        <Tag>+{items.length}...</Tag>
      </Tooltip>
    );
  };

  const mergeTransferDataWithIndex = () => {
    const result: T[] = [];
    const maxLength = Math.max(
      departmentTransferDataSource.length,
      departmentTransferWardDataSource.length,
    );

    for (let i = 0; i < maxLength; i++) {
      if (i < departmentTransferDataSource.length)
        result.push(departmentTransferDataSource[i]);
      if (i < departmentTransferWardDataSource.length)
        result.push(departmentTransferWardDataSource[i]);
    }

    return result;
  };

  const overFlowDataProcessor = () => {
    let selectedData =
      // mergeTransferDataWithIndex()
      departmentTransferDataSource
        .concat(departmentTransferWardDataSource)
        ?.filter(
          (item) =>
            !isEmptyValues(item?.InCliDept) || !isEmptyValues(item?.InWard),
        )
        ?.map((item) => {
          return item.InCliDept ?? item?.InWard;
        }) ?? [];

    const modelData = globalState?.dictData?.['CliDepts']?.concat(
      globalState?.dictData?.['Wards'],
    );
    let cliDeptNames = [];
    if (!isEmptyValues(modelData) && !isEmptyValues(selectedData)) {
      selectedData?.forEach((selectedItem) => {
        let cliDeptItem = modelData?.find(
          (item) => item?.Code === selectedItem,
        );
        if (cliDeptItem) {
          cliDeptNames.push(cliDeptItem);
        }
      });
    }

    return cliDeptNames;
  };

  if (context?.underConfiguration !== true && props?.transferTile === true) {
    return (
      <div
        ref={itemRef}
        className={
          'department-transfer-container department-transfer-modal-container'
        }
      >
        {departmentTransferWard === false ? (
          <DepartmentTransferTable
            tableType={'CliDept'}
            {...props}
            onForceUpdate={forceUpdate}
          />
        ) : (
          <div className={'department-transfer-container-with-ward'}>
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <DepartmentTransferTable
                tableType={'Ward'}
                {...props}
                columns={props?.wardColumns}
                onForceUpdate={forceUpdate}
              />
            </div>

            <div className={'department-transfer-tile-separator'} />

            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <DepartmentTransferTable
                tableType={'CliDept'}
                {...props}
                columns={props?.columns}
                onForceUpdate={forceUpdate}
              />
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={'department-transfer-container'}>
      <div
        id={'department-transfer-container'}
        className={'tags-container'}
        onClick={() => {
          setDepartmentTransferAdd(true);
        }}
      >
        <Overflow
          data={overFlowDataProcessor()}
          renderItem={renderItem}
          renderRest={renderRest}
          maxCount={'responsive'}
        />
      </div>

      <Modal
        title="转科信息"
        open={departmentTransferAdd}
        width={1000}
        okText={'保存'}
        maskClosable={false}
        cancelButtonProps={{
          style: {
            display: 'none',
          },
        }}
        onOk={(event) => {
          Emitter.emit(EventConstant.DMR_DEPARTMENT_TRANSFER_OK, event);
        }}
        onCancel={(event) => {
          setDepartmentTransferAdd(false);
          event['target']['id'] = 'department-transfer-container';
          Emitter.emit(EventConstant.DMR_TABLE_NEXT_KEY, {
            event: event,
            indexOffset: 1,
          });
        }}
        className={`department-transfer-modal-container ${
          departmentTransferWard === true
            ? 'department-transfer-modal-container-ward'
            : ''
        }`}
        destroyOnClose={true}
        getContainer={document.getElementById('dmr-content-grid-layout')}
      >
        {departmentTransferWard === false ? (
          <DepartmentTransferTable
            tableType={'CliDept'}
            {...props}
            open={departmentTransferAdd}
          />
        ) : (
          <div className={'department-transfer-container-with-ward'}>
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <label className={'label'}>病区转移记录</label>
              <DepartmentTransferTable
                tableType={'Ward'}
                {...props}
                columns={props?.wardColumns}
                scrollY={270}
                open={departmentTransferAdd}
              />
            </div>

            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                marginTop: 10,
              }}
            >
              <label className={'label'}>科室转移记录</label>
              <DepartmentTransferTable
                tableType={'CliDept'}
                {...props}
                columns={props?.columns}
                scrollY={270}
                open={departmentTransferAdd}
              />
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};
