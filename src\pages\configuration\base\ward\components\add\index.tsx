import { Form, Input, InputNumber, Switch } from 'antd';
import React from 'react';
import { UniSelect } from '@uni/components/src';
import { icdeTypes } from '@/pages/configuration/base/icde/constants';
import { cliDeptTypes } from '@/pages/configuration/base/cliDepts/constants';
import './index.less';
import { useModel } from '@@/plugin-model/useModel';
import Datepicker from '@uni/components/src/picker/datepicker';
import dayjs from 'dayjs';

interface CliDeptAddItemProps {
  form: any;

  editIndex?: number;
}

const ItemAdd = (props: CliDeptAddItemProps) => {
  const { globalState } = useModel('@@qiankunStateForSlave');

  const dictData = globalState?.dictData;

  return (
    <Form
      className={'cli-depts-add-container'}
      form={props?.form}
      preserve={false}
    >
      <Form.Item
        label="院区"
        name="HospCode"
        rules={[
          {
            required: true,
            message: '请选择院区',
          },
        ]}
      >
        <UniSelect
          dataSource={dictData?.Hospital || []}
          placeholder={'请选择'}
          optionValueKey={'Code'}
          optionNameKey={'Name'}
        />
      </Form.Item>

      <Form.Item
        label="病区编码"
        name="Code"
        rules={[
          {
            required: true,
            message: '请填写编码',
          },
        ]}
      >
        <Input disabled={props?.editIndex !== undefined} />
      </Form.Item>

      <Form.Item
        label="病区名称"
        name="Name"
        rules={[
          {
            required: true,
            message: '请填写名称',
          },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item label="学科" name="MajorPerfDept">
        <UniSelect
          dataSource={dictData?.MajorPerfDepts || []}
          placeholder={'请选择'}
          optionValueKey={'Code'}
          optionNameKey={'Name'}
        />
      </Form.Item>

      <Form.Item label="有效日期" name="dates">
        <Datepicker.RangePicker picker="date" />
      </Form.Item>

      <Form.Item label="是否有效" name="IsValid">
        <Switch defaultChecked={props?.form?.getFieldValue('IsValid')} />
      </Form.Item>
    </Form>
  );
};

export default ItemAdd;
