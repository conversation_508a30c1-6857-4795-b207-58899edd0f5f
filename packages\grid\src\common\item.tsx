import { IGridItemData } from './interfaces';
import React, { useContext } from 'react';
import { commonProps } from './field-common';
import { RequireMark } from '../components/require-mark';
import { Dropdown, Form, MenuProps, Tooltip } from 'antd';
import './index.less';
import GridItemContext from '@uni/commons/src/grid-context';
import { componentPropsProcessor } from './processor';
import { isEmptyValues } from '@uni/utils/src/utils';
import EmptyWrapper from '../components/empty-wrapper';
import { PlusSquareTwoTone } from '@ant-design/icons';
import { commentAddProps, noDropdownComponents } from './misc';

const FormItemTooltip = (props: any) => {
  const formKey = isEmptyValues(props?.formKeyPrefix)
    ? props?.componentId
    : [...props?.formKeyPrefix, props?.componentId];

  const formItemValue = Form.useWatch(formKey, props?.form);

  return (
    <Tooltip
      className={'form-item-tooltip-container'}
      overlayClassName={'form-item-tooltip-popup-container'}
      placement={'topLeft'}
      title={formItemValue}
      getPopupContainer={(triggerNode) => {
        return document.getElementById('dmr-content-grid-layout');
      }}
    >
      {props?.children}
    </Tooltip>
  );
};

interface GridItemProps {
  underConfiguration?: boolean;

  containerStyle?: any;
  // containerStyleWidth?: number;
  containerClassName?: any;

  form?: any;
  index: number;
  componentId: string;
  data: IGridItemData;
  value?: any;

  onSizeChange?: (width: number, height: number) => void;

  enableGridItemComment?: boolean;
}

const specialNoFormItemContainerComponents = [
  'OutType',
  'ProvinceSeparateSelector',
  'TimeRangeStats',
  'FeeItem',
  'ApgarInput',
  'GestationInput',
];

const specialNoFormItemContainerDataKeys = [
  'MedicineAllergy',
  'PatientReturnPlan',
];
const hightlightTableIds = ['diagnosisTable', 'operationTable'];

export const GridItem = (props: GridItemProps) => {
  const context = useContext(GridItemContext);

  const requiredLabelHighlight =
    context?.externalConfig?.requiredLabelHighlight ?? {};

  const tableHighlight = context?.externalConfig?.tableHighlight ?? {};

  const enableGridItemComment = context?.extra?.enableGridItemComment ?? false;

  const DynamicComponent = props.data?.component
    ? (context?.dynamicComponentsMap[`Uni${props.data?.component}`] as React.FC)
    : undefined;

  let componentProps = {
    ...props?.data?.props,
    ...(commonProps[props.data?.component] || {}),
  };

  componentPropsProcessor(componentProps, props?.data, context?.extra);

  // console.log(
  //   'DynamicComponent',
  //   DynamicComponent,
  //   context?.dynamicComponentsMap,
  //   props.data?.component,
  // );

  const formItemContainerClass =
    specialNoFormItemContainerComponents?.includes(props.data?.component) ||
    specialNoFormItemContainerDataKeys.includes(props?.data?.key)
      ? ''
      : 'form-content-item-container';

  const tableHighlightStyle = hightlightTableIds?.includes(props?.componentId)
    ? tableHighlight
    : {};

  if (
    props?.data?.component === 'SectionHeader' ||
    props?.data?.component === 'SectionBottom'
  ) {
    return (
      <DynamicComponent
        form={props?.form}
        id={`${props.data?.component}#${props?.componentId}`}
        className={`grid-item-base ${componentProps?.className}`}
        underConfiguration={props?.underConfiguration ?? false}
        {...componentProps}
        itemIndex={props?.index}
      />
    );
  }

  let ItemWrapper = EmptyWrapper;
  if (
    enableGridItemComment === true &&
    !noDropdownComponents?.includes(props.data?.component)
  ) {
    ItemWrapper = Dropdown;
  }

  let TooltipWrapper = EmptyWrapper;
  if (
    props?.underConfiguration !== true &&
    props?.data?.props?.tooltip === true
  ) {
    TooltipWrapper = FormItemTooltip;
  }

  // 表示存在 依赖项
  let itemDisabled = true;
  if (props?.data?.props?.hasValueDependency === true) {
    if (!isEmptyValues(props?.data?.props?.valueDependencies)) {
      let formEdited = props?.form?.getFieldValue('formEdited');
      props?.data?.props?.valueDependencies?.forEach((item) => {
        let currentDependencyKeyToValue = Form.useWatch(
          item?.dependencyKey,
          props?.form,
        ) as any;
        itemDisabled =
          currentDependencyKeyToValue?.toString() !==
          item?.dependencyValue?.toString();
      });

      if (itemDisabled === true && formEdited === true) {
        if (props?.data?.props?.valueDependencyClear === true) {
          props?.form?.setFieldValue(props?.data?.props?.formKey, null);
        }
      }
    } else {
      itemDisabled = false;
    }
  } else {
    itemDisabled = false;
  }

  const transferDeptIsTile = (data: any) => {
    if (props?.underConfiguration === true) {
      return true;
    }

    if (data?.key === 'TransDept') {
      if (data?.props?.transferTile === true) {
        return false;
      }
    }

    return true;
  };

  return (
    <ItemWrapper {...commentAddProps(props, context?.extra)}>
      {DynamicComponent ? (
        <div
          style={props?.data?.itemStyle || {}}
          className={`grid-stack-item-content grid-item-container grid-stack-item-container ${
            props?.data?.itemClassName || ''
          }  ${
            props?.componentId?.toLowerCase()?.includes('table')
              ? 'grid-stack-item-container-table'
              : ''
          }`}
        >
          <div className={'grid-stack-item-info-container'}>
            {!props?.componentId?.toLowerCase()?.includes('table') &&
              props.data?.prefix &&
              transferDeptIsTile(props?.data) && (
                <span
                  id={`grid-item-prefix-${props?.componentId}`}
                  style={
                    props?.data?.required &&
                    !isEmptyValues(requiredLabelHighlight)
                      ? requiredLabelHighlight
                      : {}
                  }
                  className={'prefix'}
                >
                  {isEmptyValues(requiredLabelHighlight) &&
                    props?.data?.required && <RequireMark />}
                  {props.data?.prefix?.toString()}
                </span>
              )}
            <TooltipWrapper
              form={props?.form}
              formKeyPrefix={props?.data?.formKeyPrefix}
              componentId={props?.componentId}
            >
              <div
                id={`formItem#${props?.componentId}`}
                className={`input border-bottom-light ${formItemContainerClass}`}
                style={{
                  ...(componentProps?.style || {}),
                  // width: valueContainerWidthStyle,
                  ...{ flex: 1 },
                  ...tableHighlightStyle,
                }}
              >
                {props?.componentId?.toLowerCase()?.includes('table') ? (
                  <DynamicComponent
                    form={props?.form}
                    id={`${props.data?.component}#${props?.componentId}`}
                    className={`grid-item-base ${componentProps?.className}`}
                    underConfiguration={props?.underConfiguration ?? false}
                    {...componentProps}
                    prefix={props?.data?.prefix}
                    componentId={props?.componentId}
                  />
                ) : (
                  <Form.Item
                    htmlFor={`formItem#${props?.componentId}`}
                    name={
                      isEmptyValues(props?.data?.formKeyPrefix)
                        ? props?.componentId
                        : [...props?.data?.formKeyPrefix, props?.componentId]
                    }
                  >
                    {
                      <DynamicComponent
                        form={props?.form}
                        id={`${props.data?.component}#${props?.componentId}`}
                        className={`grid-item-base ${componentProps?.className}`}
                        {...componentProps}
                        componentId={props?.componentId}
                        disabled={itemDisabled || props?.data?.props?.disabled}
                      />
                    }
                  </Form.Item>
                )}
              </div>
            </TooltipWrapper>
            {props.data?.suffix && (
              <span className={'suffix'}>{props.data?.suffix}</span>
            )}
          </div>
        </div>
      ) : (
        <></>
      )}
    </ItemWrapper>
  );
};
