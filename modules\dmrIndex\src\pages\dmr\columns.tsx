import { Sorta<PERSON><PERSON>andle } from 'react-sortable-hoc';
import React, { useEffect, useState } from 'react';
import {
  PlusOutlined,
  DeleteOutlined,
  AlertOutlined,
  MenuOutlined,
  PlusCircleTwoTone,
  InfoCircleTwoTone,
} from '@ant-design/icons';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { Checkbox, Input, Popconfirm, Tag, Tooltip, Button } from 'antd';
import IcdeSelect from '@/pages/dmr/components/icde-select';
import OperationSelect from '@/pages/dmr/components/oper-select';
import dayjs from 'dayjs';
import {
  IcdeOperationInputSelector,
  IcdeFieldInput,
  OperationFieldInput,
  IcdeOperationReadonlyItem,
  IcdeExtraTagsItem,
  OperationExtraTagsItem,
  icdeExtraMap,
  OperIcdeExtraMapItem,
  operationExtraMap,
  hqmsDegreeMap,
  drgsDegreeMap,
  PathologyIcdeFieldInput,
  IcuDurationFieldInput,
  IcdeOperCheckbox,
} from '@uni/grid/src/components/icde-oper-input/input';
import { RowSelectionCheckbox } from '@uni/grid/src/components/row-selection';
import { RowSelectionHeader } from '@uni/grid/src/components/row-selection-header';
import { BatchDeleteButton } from '@/pages/dmr/components/batch-delete-button';
import DateSelect from '@uni/grid/src/components/date-select';
import IconBtn from '@uni/components/src/iconBtn';
import RestrictInputNumber from '@uni/grid/src/components/restrict-number';
import { employeeDataSourceProcessor } from '@/pages/dmr/utils';
import { getArrowUpDownEventKey } from '@uni/grid/src/utils';

const tableOnlyAddIconTrigger =
  (window as any).externalConfig?.['dmr']?.tableOnlyAddIconTrigger ?? false;

const enableTableDropdownNG =
  (window as any).externalConfig?.['dmr']?.enableTableDropdownNG ?? false;

const icdeOperRowSelection =
  (window as any).externalConfig?.['dmr']?.icdeOperRowSelection ?? false;

const nonAddCell = (record, index) => {
  if (record?.id === 'ADD') {
    return {
      colSpan: 0,
    };
  }

  return {};
};

const DragHandler = (node) => {
  return true
    ? SortableHandle(() => <div className={'grab-handle'}>{node}</div>)
    : () => node;
};

interface ExtraTitlePromptItem {
  [key: string]: any;
}

const extraTitle = (
  extraMap?: { [key: string]: OperIcdeExtraMapItem },
  prompts?: ExtraTitlePromptItem,
) => {
  return (
    <Tooltip title={extraTitlePrompt(prompts, extraMap)}>
      <span>
        注<InfoCircleTwoTone style={{ marginLeft: 3 }} />
      </span>
    </Tooltip>
  );
};

const extraTitlePrompt = (
  prompts: ExtraTitlePromptItem,
  extraMap: { [key: string]: OperIcdeExtraMapItem },
) => {
  return (
    <>
      {Object.keys(extraMap)?.map((key, index) => {
        if (key === 'HqmsDegree') {
          return (
            <>
              {Object.keys(hqmsDegreeMap)
                ?.slice(-1)
                ?.map((hqmsKey, index) => {
                  return (
                    <PromptItem
                      color={hqmsDegreeMap?.[hqmsKey]?.color}
                      display={hqmsDegreeMap?.[hqmsKey]?.display}
                      prompt={
                        prompts?.[key]?.prompt ??
                        hqmsDegreeMap?.[hqmsKey]?.label
                      }
                      extraLine={true}
                    />
                  );
                })}
            </>
          );
        }

        if (key === 'DrgsDegree') {
          return (
            <>
              {Object.keys(drgsDegreeMap)
                ?.slice(-1)
                ?.map((drgsKey, index) => {
                  return (
                    <PromptItem
                      color={drgsDegreeMap?.[drgsKey]?.color}
                      display={drgsDegreeMap?.[drgsKey]?.display}
                      prompt={
                        prompts?.[key]?.prompt ??
                        drgsDegreeMap?.[drgsKey]?.label
                      }
                      extraLine={
                        index !== Object.keys(drgsDegreeMap)?.length - 1
                      }
                    />
                  );
                })}
            </>
          );
        }
        return (
          <PromptItem
            color={extraMap?.[key]?.color}
            display={extraMap?.[key]?.display}
            prompt={prompts?.[key]?.prompt ?? extraMap?.[key]?.label}
            extraLine={index !== Object.keys(extraMap)?.length - 1}
          />
        );
      })}
    </>
  );
};

const PromptItem = ({ color, display, prompt, extraLine }) => {
  return (
    <>
      <Tag
        style={{ margin: '5px 5px', border: 'none', borderRadius: 4 }}
        color={color}
      >
        {display}
      </Tag>
      <span>{prompt}</span>
      {extraLine && <br />}
    </>
  );
};

export const icdeColumns = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'IcdeExtra',
    title: extraTitle(icdeExtraMap, {
      InsurIsObsolete: {
        prompt: '医保置灰',
      },
    }),
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 50,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'icde-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.DMR_ICDE_ADD);
                  }
                : undefined
            }
          >
            <div
              itemid={`Table-diagnosisTable-Missing`}
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true
                  ? { cursor: 'pointer', padding: '4px 8px' }
                  : { padding: '4px 8px' }
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_ICDE_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      } else {
        if (record?.id !== 'ADD') {
          return (
            <IcdeExtraTagsItem
              value={record?.['IcdeExtra']}
              nameKey={'IcdeExtra'}
            />
          );
        }
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 14,
        };
      }

      return {};
    },
  },
  {
    key: 'rowSelection',
    dataIndex: 'RowSelection',
    title: (
      <RowSelectionHeader
        tableId="diagnosisTable"
        onSelectAll={(checked) => {
          console.log('全选/反选:', checked);
        }}
      />
    ),
    visible: icdeOperRowSelection || false,
    align: 'center',
    width: 44,
    fixed: 'left',
    readonly: false,
    renderColumnFormItem: (node, record, index, dataIndex) => {
      return (
        <RowSelectionCheckbox
          id={`formItem#RowSelection#${index}#IcdeTable`}
          recordId={record?.id}
          dataIndex={dataIndex}
          onChangeExtra={(checked) => {
            console.log('asddsadadasdacheckbox');
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    key: 'sort',
    dataIndex: 'IcdeSort',
    title: '序',
    visible: true,
    align: 'center',
    width: 44,
    fixed: 'left',
    readonly: true,
    disableComment: true,
    render: (node, record, index, action) => {
      let labelNode = null;
      if (index === 0) {
        labelNode = <span>主</span>;
      } else {
        // labelNode = <span>{`次要诊断${index}`}</span>;
        labelNode = <span style={{ whiteSpace: 'nowrap' }}>{`${index}`}</span>;
      }
      if (record?.id !== 'ADD') {
        const SortDragHandler = DragHandler(labelNode);
        return <SortDragHandler />;
      }
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeCode',
    title: '出院诊断编码',
    align: 'center',
    fixed: 'left',
    visible: true,
    width: 140,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeSelect
          tableId={'diagnosisTable'}
          componentId={`IcdeCode#${index}`}
          paramKey={'OutHospital'}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          icdeSelectType={'IsDscg'}
          instantSelect={true}
          // selectFormKey={`IcdeCode#${index}`}
          formKeys={{
            IcdeName: 'Name',
            IcdeCode: 'Code',
            InsurName: 'InsurName',
            InsurCode: 'InsurCode',
            HqmsName: 'HqmsName',
            HqmsCode: 'HqmsCode',
            IcdeExtra: 'IcdeExtra',
          }}
          listHeight={200}
          // dropdownAlign={{ offset: [140, -30] }}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG ? { offset: [-120, 4] } : undefined
          }
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeName',
    title: '出院诊断名称',
    visible: true,
    fixed: 'left',
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeCond',
    title: '入院病情',
    visible: true,
    align: 'center',
    width: 100,
    conditionDictionaryKey: 'RYBQ',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'diagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeCond'}
          index={index}
          conditionDictionaryKey={'RYBQ'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeNote',
    title: '诊断描述',
    visible: true,
    width: 160,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeFieldInput
          dataIndex={'IcdeNote'}
          index={index}
          tableId={'diagnosisTable'}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'Remark',
    title: '诊断后描述',
    visible: false,
    width: 160,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeFieldInput
          dataIndex={'Remark'}
          index={index}
          tableId={'diagnosisTable'}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeOutcome',
    title: '治疗情况',
    visible: true,
    align: 'center',
    width: 90,
    conditionDictionaryKey: 'IcdeOutcome',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'diagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeOutcome'}
          index={index}
          listHeight={130}
          conditionDictionaryKey={'IcdeOutcome'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IsMain',
    title: '医保主诊',
    visible: true,
    width: 50,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form) => {
      return (
        <IcdeOperCheckbox
          id={`formItem#IsMain#${index}#IcdeTable`}
          recordId={record?.id}
          dataIndex={dataIndex}
          form={form}
          onChangeExtra={(checked) => {
            Emitter.emit(EventConstant.DMR_ICDE_INSURE_MAIN, {
              id: record?.id,
              values: {
                IsMain: checked,
              },
              index: index,
            });
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IsReported',
    title: '医保上报',
    visible: true,
    width: 50,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form) => {
      return (
        <IcdeOperCheckbox
          id={`formItem#IsReported#${index}#IcdeTable`}
          recordId={record['id']}
          dataIndex={dataIndex}
          dependencyKey={'IsMain'}
          dependencyValue={true}
          form={form}
          minimumChecked={1}
          onChangeExtra={(checked) => {
            Emitter.emit(EventConstant.DMR_ICDE_REPORT, checked);
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'InsurCode',
    title: '医保编码',
    visible: true,
    readonly: true,
    width: 140,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeSelect
          tableId={'diagnosisTable'}
          componentId={`InsurCode#${index}`}
          interfaceUrl={'Api/Insur/InsurSearch/Icde'}
          paramKey={'OutHospital'}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          icdeSelectType={'IsDscg'}
          // disabled={!entity['IcdeExtra']?.includes('IsObsolete')}
          // disabled={entity['IsObsolete']}
          // instantSelect={true}
          // selectFormKey={`IcdeCode#${index}`}
          formKeys={{
            InsurName: 'Name',
            InsurCode: 'Code',
            IcdeExtra: 'IcdeExtra',
          }}
          listHeight={200}
          // dropdownAlign={{ offset: [140, -30] }}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG ? { offset: [-144, 4] } : undefined
          }
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'InsurName',
    title: '医保名称',
    visible: true,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'HqmsCode',
    title: '国家临床版编码',
    visible: true,
    readonly: true,
    width: 140,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'HqmsName',
    title: '国家临床版名称',
    visible: true,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index, dataIndex) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IsInfects',
    title: '院感',
    visible: false,
    align: 'center',
    width: 90,
    conditionDictionaryKey: 'IsInfects',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'diagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IsInfects'}
          index={index}
          listHeight={130}
          conditionDictionaryKey={'IsInfects'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IsOperComplication',
    title: '术后并发症',
    visible: false,
    align: 'center',
    width: 90,
    conditionDictionaryKey: 'IsOperComplication',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'diagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IsOperComplication'}
          index={index}
          listHeight={130}
          conditionDictionaryKey={'IsOperComplication'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'DiagAccord',
    title: '符合标识',
    visible: false,
    align: 'center',
    width: 90,
    conditionDictionaryKey: 'DiagAccord',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'diagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'DiagAccord'}
          index={index}
          listHeight={130}
          conditionDictionaryKey={'DiagAccord'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'operation',
    title: icdeOperRowSelection ? (
      <BatchDeleteButton tableId="diagnosisTable" />
    ) : (
      ''
    ),
    visible: true,
    align: 'center',
    width: 100,
    readonly: true,
    disableComment: true,
    render: (node, record, index, action) => {
      return (
        <div className={'operation-item'}>
          <IconBtn
            type="copy"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_ICDE_COPY, {
                id: record?.['id'],
                index: index,
              });
            }}
          />
          <IconBtn
            type="delete"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_ICDE_DELETE, index);
            }}
          />
        </div>
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

const OperationDragHandle = SortableHandle(() => (
  <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />
));
export const operationColumns = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'OperExtra',
    title: extraTitle(operationExtraMap, {
      InsurIsObsolete: {
        prompt: '医保置灰',
      },
      IsMicro: {
        prompt: '微创手术',
      },
      HqmsDegree: {
        prompt: '国考手术等级',
      },
      DrgsDegree: {
        prompt: 'DRG手术等级',
      },
    }),
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 60,
    readonly: true,
    renderColumnFormItem: (node, record, index, dataIndex, form) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'operation-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.DMR_OPER_ADD);
                  }
                : undefined
            }
          >
            <div
              itemid={`Table-operationTable-Missing`}
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true
                  ? { cursor: 'pointer', padding: '4px 8px' }
                  : { padding: '4px 8px' }
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_OPER_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      } else {
        if (record?.id !== 'ADD') {
          return (
            <OperationExtraTagsItem
              record={record}
              uniqueId={record?.UniqueId}
              eventName={`${EventConstant.DMR_OPER_SELECT_ADD}#${record?.id}`}
              nameKey={'OperExtra'}
              conditionDictionaryKey={'SSJB'}
              conditionDictionaryGroup={'Dmr'}
              form={form}
            />
          );
        }
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          // colSpan: 19,
          colSpan: 18,
        };
      }

      return {};
    },
  },
  {
    key: 'rowSelection',
    dataIndex: 'RowSelection',
    title: (
      <RowSelectionHeader
        tableId="operationTable"
        onSelectAll={(checked) => {
          console.log('手术表全选/反选:', checked);
        }}
      />
    ),
    visible: icdeOperRowSelection || false,
    align: 'center',
    width: 44,
    fixed: 'left',
    readonly: false,
    renderColumnFormItem: (node, record, index, dataIndex) => {
      return (
        <RowSelectionCheckbox
          id={`formItem#RowSelection#${index}#OperTable`}
          recordId={record?.id}
          dataIndex={dataIndex}
          onChangeExtra={(checked) => {
            console.log('手术行选择:', checked);
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    key: 'sort',
    dataIndex: 'OperSort',
    title: '序',
    visible: true,
    align: 'center',
    fixed: 'left',
    readonly: true,
    disableComment: true,
    width: 70,
    render: (node, record, index, action) => {
      let labelNode = (
        <span
          style={{ whiteSpace: 'nowrap' }}
          className={'operation-index'}
        >{`${index + 1}`}</span>
      );
      const SortDragHandler = DragHandler(labelNode);
      return <SortDragHandler />;
    },
  },
  {
    dataIndex: 'HqmsDegree',
    title: '国考手术级别',
    visible: false,
    readonly: true,
  },
  {
    dataIndex: 'DegreeRemark',
    title: '国考手术级别条件',
    visible: false,
    readonly: true,
  },
  {
    dataIndex: 'DrgsDegree',
    title: 'DRG手术级别',
    visible: false,
    readonly: true,
  },
  {
    dataIndex: 'OperGroupNo',
    title: '台次',
    visible: true,
    align: 'center',
    width: 100,
    disableComment: true,
    renderColumnFormItem: (node, record, index) => {
      return <OperationFieldInput dataIndex={'OperGroupNo'} index={index} />;
    },
  },
  {
    dataIndex: 'OperType',
    title: '手术类型',
    visible: true,
    align: 'center',
    width: 100,
    fixed: 'left',
    readonly: true,
    conditionDictionaryKey: 'OperType',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      console.log('OperType render');
      return (
        <IcdeOperationReadonlyItem
          className={'dmr-oper-type'}
          conditionDictionaryKey={'OperType'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'OperCode',
    title: '手术及操作编码',
    visible: true,
    fixed: 'left',
    width: 140,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <OperationSelect
          columnType={'Dmr'}
          tableId={'operationTable'}
          componentId={`OperCode#${index}`}
          rowDataKey={record['id']}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          parentId={'operation-table-content'}
          instantSelect={true}
          formKeys={{
            OperName: 'Name',
            OperCode: 'Code',
            InsurName: 'InsurName',
            InsurCode: 'InsurCode',
            HqmsName: 'HqmsName',
            HqmsCode: 'HqmsCode',
            OperRate: 'Degree',
            OperType: 'OperType',
            OperExtra: 'OperExtra',
            HqmsDegree: 'HqmsDegree',
            DegreeRemark: 'DegreeRemark',
            DrgsDegree: 'DrgsDegree',
            RowClassName: 'RowClassName',
          }}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          listHeight={200}
          // dropdownAlign={{ offset: [140, -30] }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG ? { offset: [-144, 4] } : undefined
          }
        />
      );
    },
  },
  {
    dataIndex: 'OperName',
    title: '手术及操作名称',
    visible: true,
    fixed: 'left',
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem className={'dmr-oper-name'} />;
    },
  },
  {
    dataIndex: 'OprnOprtBegntime',
    title: '手术及操作日期',
    visible: true,
    align: 'center',
    width: 166,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          className={'operation-item-container'}
          type={'compact'}
          formItemId={`formItem#OprnOprtBegntime#${index}#CompactInput`}
          formKey={'OprnOprtBegntime'}
          dataTableIndex={index}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
  },
  {
    dataIndex: 'OprnConTime',
    title: '手术持续时间（小时）',
    visible: true,
    align: 'center',
    width: 100,
    renderColumnFormItem: (node, record, index) => {
      return (
        <RestrictInputNumber
          itemId={`formItem#OprnConTime#${index}#RestrictInputNumber`}
          min={0}
          max={1000}
          precious={2}
          step={0.01}
          formKey={'OprnConTime'}
        />
      );
    },
  },
  {
    dataIndex: 'OperRate',
    title: '手术级别',
    visible: true,
    align: 'center',
    width: 50,
    readonly: true,
    conditionDictionaryKey: 'SSJB',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationReadonlyItem
          conditionDictionaryKey={'SSJB'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
  },
  // {
  //   dataIndex: 'Operators',
  //   title: '手术及操作医师',
  //   visible: true,
  //   align: 'center',
  //   children: [
  //
  //   ],
  // },
  {
    dataIndex: 'Operator',
    title: '手术者',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'Employee',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      // return (
      //   <OperationFieldInput
      //     record={entity}
      //     dataIndex={'Operator'}
      //     index={index}
      //   />
      // )
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'Operator'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
          numberSelectItem={enableTableDropdownNG}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'Firstasst',
    title: 'Ⅰ助',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'Employee',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'Firstasst'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
          numberSelectItem={enableTableDropdownNG}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'Secondasst',
    title: 'Ⅱ助',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'Employee',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'Secondasst'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
          numberSelectItem={enableTableDropdownNG}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'WoundHealingRateClass',
    title: '手术切口愈合等级',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'QKYHLB',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'WoundHealingRateClass'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'QKYHLB'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'AnaType',
    title: '麻醉方式',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'MZFS',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'AnaType'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'MZFS'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'AnstLvCode',
    title: '麻醉分级',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'AnstLv',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'AnstLvCode'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'AnstLv'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'AnaDoc',
    title: '麻醉医师',
    visible: true,
    align: 'center',
    width: 80,
    conditionDictionaryKey: 'Employee',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'AnaDoc'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
          leftOneAutoSelect={true}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'OprnPatnTypeCode',
    title: '手术类别',
    visible: true,
    width: 100,
    align: 'center',
    conditionDictionaryKey: 'OprnPatnType',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'OprnPatnTypeCode'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'OprnPatnType'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'OperNote',
    title: '手术操作描述',
    visible: true,
    width: 160,
    align: 'center',
    renderColumnFormItem: (node, record, index) => {
      return <OperationFieldInput dataIndex={'OperNote'} index={index} />;
    },
  },
  {
    dataIndex: 'OprnOprtEndtime',
    title: '手术截止日期',
    visible: true,
    width: 160,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          className={'operation-item-container'}
          type={'compact'}
          formItemId={`formItem#OprnOprtEndtime#${index}#CompactInput`}
          formKey={'OprnOprtEndtime'}
          dataTableIndex={index}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
  },
  {
    dataIndex: 'AnstBegntime',
    title: '手术麻醉时间',
    visible: true,
    width: 160,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          className={'operation-item-container'}
          type={'compact'}
          formItemId={`formItem#AnstBegntime#${index}#CompactInput`}
          formKey={'AnstBegntime'}
          dataTableIndex={index}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
  },
  {
    dataIndex: 'AnstEndtime',
    title: '麻醉结束时间',
    visible: true,
    width: 160,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          className={'operation-item-container'}
          type={'compact'}
          formItemId={`formItem#AnstEndtime#${index}#CompactInput`}
          formKey={'AnstEndtime'}
          dataTableIndex={index}
          value={record['AnstEndtime']}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
  },
  {
    dataIndex: 'OperDept',
    title: '手术科室',
    visible: true,
    width: 160,
    align: 'center',
    conditionDictionaryKey: 'CliDepts',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'OperDept'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'CliDepts'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'InsurCode',
    title: '医保手术编码',
    visible: true,
    readonly: true,
    width: 140,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        // <IcdeOperationReadonlyItem
        //   eventName={`${EventConstant.DMR_OPER_INSUR_SELECT_ADD}#${entity?.id}`}
        //   nameKey={'InsurCode'}
        // />
        <OperationSelect
          columnType={'Insur'}
          tableId={'operationTable'}
          componentId={`InsurCode#${index}`}
          interfaceUrl={'Api/Insur/InsurSearch/Oper'}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          // disabled={!entity['IsObsolete']}
          // disabled={!entity['OperExtra']?.includes('IsObsolete')}
          formKeys={{
            InsurName: 'Name',
            InsurCode: 'Code',
            OperExtra: 'OperExtra',
          }}
          // instantSelect={true}
          value={{
            value: record['InsurCode'],
          }}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          listHeight={200}
          // dropdownAlign={{ offset: [140, -30] }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG ? { offset: [-144, 4] } : undefined
          }
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'InsurName',
    title: '医保手术名称',
    visible: true,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem className={'dmr-oper-name'} />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IsMain',
    title: '医保主手术',
    visible: true,
    width: 70,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form) => {
      return (
        <IcdeOperCheckbox
          id={`IsMain#${index}#OperTable`}
          recordId={record?.id}
          dataIndex={dataIndex}
          form={form}
          onChangeExtra={(checked) => {
            Emitter.emit(EventConstant.DMR_OPER_INSURE_MAIN, {
              id: record?.id,
              values: {
                IsMain: checked,
              },
              index: index,
            });
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IsReported',
    title: '医保上报',
    visible: true,
    width: 50,
    align: 'center',
    dependencyFormKey: 'IsMain',
    propsKey: 'disabled',
    dependencyFormValue: true,
    renderColumnFormItem: (node, record, index, dataIndex, form) => {
      return (
        <IcdeOperCheckbox
          id={`IsReported#${index}#OperTable`}
          recordId={record['id']}
          dataIndex={dataIndex}
          dependencyKey={'IsMain'}
          dependencyValue={true}
          form={form}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'HqmsCode',
    title: '国家临床版编码',
    visible: true,
    readonly: true,
    width: 140,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'HqmsName',
    title: '国家临床版名称',
    visible: true,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'OperAccord',
    title: '符合标志',
    visible: false,
    width: 100,
    align: 'center',
    conditionDictionaryKey: 'OperAccord',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'OperAccord'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'OperAccord'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'operation',
    title: icdeOperRowSelection ? (
      <BatchDeleteButton tableId="operationTable" />
    ) : (
      ''
    ),
    visible: true,
    align: 'center',
    width: 100,
    disableComment: true,
    render: (node, record, index) => {
      return (
        <div className={'operation-item'}>
          <IconBtn
            type="copy"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_OPER_COPY, {
                id: record?.['id'],
                index: index,
              });
            }}
          />
          <IconBtn
            type="delete"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_OPER_DELETE, index);
            }}
          />
        </div>
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

export const dmrSearchColumns = [
  {
    dataIndex: 'status',
    visible: true,
    width: 30,
    align: 'center',
    title: '',
    orderable: false,
    order: 1,
    render: (node, record, index) => {
      if (
        record?.RegisterStatusName === null ||
        record?.RegisterStatusName === undefined
      )
        return record?.RegisterStatusName;
      switch (record?.RegisterStatusName) {
        case '未登记':
          return (
            <Tooltip title={'未登记'}>
              <span className="font-warning">
                <AlertOutlined />
              </span>
            </Tooltip>
          );
        case '已登记':
          return (
            <Tooltip title={record?.RegisterStatusName}>
              <span className="font-success">
                <AlertOutlined />
              </span>
            </Tooltip>
          );
        default:
          return node;
      }
    },
  },
];
export const icuColumns = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'IcuSort',
    title: '',
    visible: true,
    align: 'center',
    width: 40,
    readonly: true,
    renderColumnFormItem: (node, record, index, dataIndex, form) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'operation-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.DMR_ICU_ADD);
                  }
                : undefined
            }
          >
            <div
              itemid={`Table-icuTable-Missing`}
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true
                  ? { cursor: 'pointer', padding: '4px 8px' }
                  : { padding: '4px 8px' }
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_ICU_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      } else {
        if (record?.id !== 'ADD') {
          return <OperationDragHandle />;
        }
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 7,
        };
      }

      return {};
    },
  },
  {
    dataIndex: 'InpoolIcuTime',
    title: '进重症监护室时间',
    visible: true,
    width: 250,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          formItemId={`formItem#InpoolIcuTime#${index}#CompactInput`}
          type={'compact'}
          formKey={'InpoolIcuTime'}
          dataTableIndex={index}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'OutIcuTime',
    title: '出重症监护室时间',
    visible: true,
    width: 250,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          formItemId={`formItem#OutIcuTime#${index}#CompactInput`}
          type={'compact'}
          formKey={'OutIcuTime'}
          dataTableIndex={index}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcuCategory',
    title: '监护类型',
    visible: true,
    align: 'center',
    width: 200,
    conditionDictionaryKey: 'IcuCategory',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'icuTable'}
          className={'in-hospital-diagnosis'}
          dataIndex={'IcuCategory'}
          index={index}
          conditionDictionaryKey={'IcuCategory'}
          conditionDictionaryGroup={'Dmr'}
          listHeight={130}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcuCode',
    title: '重症监护病房类型',
    visible: true,
    align: 'center',
    width: 180,
    conditionDictionaryKey: 'IcuType',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'icuTable'}
          className={'in-hospital-diagnosis'}
          dataIndex={'IcuCode'}
          index={index}
          conditionDictionaryKey={'IcuType'}
          conditionDictionaryGroup={'Dmr'}
          listHeight={130}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcuDuration',
    title: '重症监护使用时长（小时）',
    visible: true,
    width: 150,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcuDurationFieldInput
          className={'in-hospital-diagnosis'}
          dataIndex={'IcuDuration'}
          index={index}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'operation',
    title: '',
    visible: true,
    align: 'center',
    width: 40,
    readonly: true,
    disableComment: true,
    render: (node, record, index) => {
      return (
        <IconBtn
          type="delete"
          onClick={() => {
            Emitter.emit(EventConstant.DMR_ICU_DELETE, index);
          }}
        />
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

export const pathologyIcdeColumns = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'PathologyIcdeSort',
    title: '序',
    visible: true,
    align: 'center',
    width: '5%',
    readonly: true,
    renderColumnFormItem: (node, record, index, dataIndex, form) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'icde-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.DMR_PATHOLOGY_ICDE_ADD);
                  }
                : undefined
            }
          >
            <div
              itemid={`Table-pathologicalDiagnosisTable-Missing`}
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true
                  ? { cursor: 'pointer', padding: '4px 8px' }
                  : { padding: '4px 8px' }
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_PATHOLOGY_ICDE_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      } else {
        let labelNode = <span>{`${index + 1}`}</span>;
        if (record?.id !== 'ADD') {
          const SortDragHandler = DragHandler(labelNode);
          return <SortDragHandler />;
        }
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 6,
        };
      }

      return {};
    },
  },
  {
    dataIndex: 'PathoType',
    title: '诊断输入类别',
    align: 'center',
    visible: false,
    width: 120,
    conditionDictionaryKey: 'PathoType',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'pathologicalDiagnosisTable'}
          dataIndex={'PathoType'}
          index={index}
          listHeight={130}
          conditionDictionaryKey={'PathoType'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'PathologyIcdeCode',
    title: '病理诊断编码',
    align: 'center',
    visible: true,
    width: '20%',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeSelect
          tableId={'pathologicalDiagnosisTable'}
          componentId={`PathologyIcdeCode#${index}`}
          paramKey={'pathologicalDiagnosis'}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          icdeSelectType={'IsPathoAndMor'}
          formKeys={{
            PathologyIcdeName: 'Name',
            PathologyIcdeCode: 'Code',
          }}
          listHeight={200}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG ? { offset: [-144, 4] } : undefined
          }
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'PathologyIcdeName',
    title: '病理诊断名称',
    visible: true,
    readonly: true,
    width: '30%',
    renderColumnFormItem: (node, record, index) => {
      return (
        <PathologyIcdeFieldInput
          recordId={record['id']}
          disabled={true}
          dataIndex={'PathologyIcdeName'}
          index={index}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'PalgNo',
    title: '病理号',
    visible: true,
    width: '15%',
    renderColumnFormItem: (node, record, index) => {
      return (
        <PathologyIcdeFieldInput
          recordId={record['id']}
          dataIndex={'PalgNo'}
          index={index}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeNote',
    title: '病理结果',
    visible: true,
    width: '15%',
    renderColumnFormItem: (node, record, index) => {
      return (
        <PathologyIcdeFieldInput
          recordId={record['id']}
          dataIndex={'IcdeNote'}
          index={index}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'Remark',
    title: '诊断后描述',
    visible: false,
    width: 160,
    renderColumnFormItem: (node, record, index) => {
      return (
        <PathologyIcdeFieldInput
          recordId={record['id']}
          dataIndex={'Remark'}
          index={index}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'TumorDiagEvid',
    title: '病理诊断',
    visible: false,
    width: '15%',
    conditionDictionaryKey: 'TumorDiagEvid',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'pathologicalDiagnosisTable'}
          dataIndex={'TumorDiagEvid'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'TumorDiagEvid'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'TumorStaging_T',
    title: '肿瘤分期 T',
    visible: false,
    width: '15%',
    conditionDictionaryKey: 'TumorStaging_T',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'pathologicalDiagnosisTable'}
          dataIndex={'TumorStaging_T'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'TumorStaging_T'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'TumorStaging_N',
    title: '肿瘤分期 N',
    visible: false,
    width: '15%',
    conditionDictionaryKey: 'TumorStaging_N',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'pathologicalDiagnosisTable'}
          dataIndex={'TumorStaging_N'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'TumorStaging_N'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'TumorStaging_M',
    title: '肿瘤分期 M',
    visible: false,
    width: '15%',
    conditionDictionaryKey: 'TumorStaging_M',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'pathologicalDiagnosisTable'}
          dataIndex={'TumorStaging_M'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'TumorStaging_M'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'DiagAccord',
    title: '符合标识',
    visible: false,
    align: 'center',
    width: '10%',
    conditionDictionaryKey: 'DiagAccord',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'pathologicalDiagnosisTable'}
          dataIndex={'DiagAccord'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'DiagAccord'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'TechType',
    title: '医技方法名称',
    visible: false,
    align: 'center',
    width: '10%',
    conditionDictionaryKey: 'TechType',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'pathologicalDiagnosisTable'}
          dataIndex={'TechType'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'TechType'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'TechDiagResult',
    title: '医技诊断结果',
    visible: false,
    align: 'center',
    width: '15%',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <PathologyIcdeFieldInput
          recordId={record['id']}
          dataIndex={'TechDiagResult'}
          index={index}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'operation',
    title: '',
    visible: true,
    align: 'center',
    width: '5%',
    readonly: true,
    disableComment: true,
    render: (node, record, index) => {
      return (
        <Tooltip title="删除">
          <Popconfirm
            title="确定删除？"
            onConfirm={() => {
              Emitter.emit(EventConstant.DMR_PATHOLOGY_ICDE_DELETE, index);
            }}
            getPopupContainer={(triggerNode) =>
              document.getElementById('dmr-content-container')
            }
          >
            <a className={'operation'}>
              <DeleteOutlined />
            </a>
          </Popconfirm>
        </Tooltip>
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

export const tcmIcdeColumns = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'IcdeExtra',
    title: extraTitle(icdeExtraMap, {
      InsurIsObsolete: {
        prompt: '医保置灰',
      },
    }),
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 50,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'icde-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.DMR_ICDE_ADD);
                  }
                : undefined
            }
          >
            <div
              itemid={`Table-tcmDiagnosisTable-Missing`}
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true
                  ? { cursor: 'pointer', padding: '4px 8px' }
                  : { padding: '4px 8px' }
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_TCM_ICDE_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      } else {
        if (record?.id !== 'ADD') {
          return (
            <IcdeExtraTagsItem
              value={record?.['IcdeExtra']}
              nameKey={'IcdeExtra'}
            />
          );
        }
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 14,
        };
      }

      return {};
    },
  },
  {
    key: 'sort',
    dataIndex: 'IcdeSort',
    title: '序',
    visible: true,
    align: 'center',
    width: 44,
    fixed: 'left',
    readonly: true,
    disableComment: true,
    render: (node, record, index, action) => {
      let labelNode = null;
      if (index === 0) {
        labelNode = <span>主病</span>;
      } else {
        // labelNode = <span>{`次要诊断${index}`}</span>;
        labelNode = <span>主证</span>;
      }

      if (index === 0) {
        return labelNode;
      }

      if (record?.id !== 'ADD') {
        const SortDragHandler = DragHandler(labelNode);
        return <SortDragHandler />;
      }
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'UniqueIcdeCode',
    title: 'UniqueIcdeCode',
    visible: false,
    readonly: true,
  },
  {
    dataIndex: 'IcdeCode',
    title: '中医诊断编码',
    align: 'center',
    fixed: 'left',
    visible: true,
    width: 140,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeSelect
          tableId={'tcmDiagnosisTable'}
          componentId={`TcmIcdeCode#${index}`}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          icdeSelectType={'IsTcm'}
          icdeTcmCategory={index === 0 ? 'A' : 'B'}
          instantSelect={true}
          // selectFormKey={`IcdeCode#${index}`}
          formKeys={{
            IcdeName: 'Name',
            IcdeCode: 'TcmIcdeCode',
            UniqueIcdeCode: 'Code',
            InsurName: 'InsurName',
            InsurCode: 'InsurCode',
            HqmsName: 'HqmsName',
            HqmsCode: 'HqmsCode',
            IcdeExtra: 'IcdeExtra',
          }}
          listHeight={200}
          // dropdownAlign={{ offset: [140, -30] }}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG ? { offset: [-144, 4] } : undefined
          }
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeName',
    title: '中医诊断名称',
    visible: true,
    fixed: 'left',
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeCond',
    title: '入院病情',
    visible: true,
    align: 'center',
    width: 100,
    conditionDictionaryKey: 'RYBQ',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'tcmDiagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeCond'}
          index={index}
          conditionDictionaryKey={'RYBQ'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeOutcome',
    title: '出院情况',
    visible: true,
    align: 'center',
    width: 90,
    conditionDictionaryKey: 'IcdeOutcome',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'tcmDiagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeOutcome'}
          index={index}
          listHeight={130}
          conditionDictionaryKey={'IcdeOutcome'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'UniqueTreatMethodCode',
    title: 'UniqueTreatMethodCode',
    visible: false,
    readonly: true,
  },
  {
    dataIndex: 'TreatMethodCode',
    title: '治则治法编码',
    align: 'center',
    fixed: 'left',
    visible: true,
    width: 140,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeSelect
          tableId={'tcmDiagnosisTable'}
          componentId={`TcmTreatMethodCode#${index}`}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          icdeSelectType={'IsTcm'}
          icdeTcmCategory={'C'}
          instantSelect={true}
          // selectFormKey={`IcdeCode#${index}`}
          formKeys={{
            TreatMethodName: 'Name',
            TreatMethodCode: 'TcmIcdeCode',
            UniqueTreatMethodCode: 'Code',
          }}
          listHeight={200}
          // dropdownAlign={{ offset: [140, -30] }}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG ? { offset: [-144, 4] } : undefined
          }
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'TreatMethodName',
    title: '治则治法名称',
    visible: true,
    fixed: 'left',
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'InsurCode',
    title: '医保编码',
    visible: true,
    readonly: true,
    width: 140,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeSelect
          tableId={'tcmDiagnosisTable'}
          componentId={`InsurCode#${index}`}
          interfaceUrl={'Api/Insur/InsurSearch/Icde'}
          paramKey={'OutHospital'}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          icdeSelectType={'IsTcm'}
          // disabled={!entity['IcdeExtra']?.includes('IsObsolete')}
          // disabled={entity['IsObsolete']}
          // instantSelect={true}
          // selectFormKey={`IcdeCode#${index}`}
          formKeys={{
            InsurName: 'Name',
            InsurCode: 'Code',
            IcdeExtra: 'IcdeExtra',
          }}
          listHeight={200}
          // dropdownAlign={{ offset: [140, -30] }}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG ? { offset: [-144, 4] } : undefined
          }
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'InsurName',
    title: '医保名称',
    visible: true,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'HqmsCode',
    title: '国家临床版编码',
    visible: true,
    readonly: true,
    width: 140,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'HqmsName',
    title: '国家临床版名称',
    visible: true,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index, dataIndex) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'operation',
    title: '',
    visible: true,
    align: 'center',
    width: 70,
    readonly: true,
    disableComment: true,
    render: (node, record, index, action) => {
      return (
        <div className={'operation-item'}>
          <IconBtn
            type="copy"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_TCM_ICDE_COPY, {
                id: record?.['id'],
                index: index,
              });
            }}
          />
          {index !== 0 && (
            <IconBtn
              type="delete"
              onClick={() => {
                Emitter.emit(EventConstant.DMR_TCM_ICDE_DELETE, index);
              }}
            />
          )}
        </div>
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

export const babyIcdeColumns = (containerRef: any) => {
  return [
    {
      key: 'CONTAINS_ADD',
      dataIndex: 'IcdeSort',
      title: '序',
      visible: true,
      align: 'center',
      width: 44,
      fixed: 'left',
      readonly: true,
      disableComment: true,
      render: (node, record, index, action) => {
        if (record?.id === 'ADD') {
          return (
            <div
              className={'icde-add-container'}
              style={
                tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
              }
              onClick={
                tableOnlyAddIconTrigger === false
                  ? () => {
                      containerRef?.current?.babyIcdeAdd();
                    }
                  : undefined
              }
            >
              <div
                className={'flex-row-center'}
                style={
                  tableOnlyAddIconTrigger === true ? { cursor: 'pointer' } : {}
                }
                onClick={
                  tableOnlyAddIconTrigger === true
                    ? () => {
                        containerRef?.current?.babyIcdeAdd();
                      }
                    : undefined
                }
              >
                <PlusCircleTwoTone />
                <span>新增</span>
              </div>
            </div>
          );
        } else {
          if (record?.id !== 'ADD') {
            let labelNode = <span>{`${index + 1}`}</span>;
            if (record?.id !== 'ADD') {
              const SortDragHandler = DragHandler(labelNode);
              return <SortDragHandler />;
            }
          }
        }
      },
      onCell: (record, index) => {
        if (record?.id === 'ADD') {
          return {
            colSpan: 5,
          };
        }

        return {};
      },
    },
    {
      dataIndex: 'IcdeCode',
      title: '诊断编码',
      align: 'center',
      fixed: 'left',
      visible: true,
      width: 200,
      renderColumnFormItem: (
        node,
        record,
        index,
        dataIndex,
        form,
        extraItem,
      ) => {
        return (
          <IcdeSelect
            tableId={'babyDiagnosisTable'}
            componentId={`IcdeCode#${index}`}
            paramKey={'OutHospital'}
            recordId={record['id']}
            dataIndex={dataIndex}
            form={form}
            icdeSelectType={'IsDscg'}
            instantSelect={true}
            // selectFormKey={`IcdeCode#${index}`}
            formKeys={{
              IcdeName: 'Name',
              IcdeCode: 'Code',
            }}
            listHeight={200}
            // dropdownAlign={{ offset: [140, -30] }}
            // getPopupContainer={(trigger) => {
            //   return trigger?.closest('table');
            // }}
            onChangeValueProcessor={(fieldsValue) => {
              return fieldsValue[dataIndex];
            }}
            codeColumnWidth={extraItem?.width}
            numberSelectItem={enableTableDropdownNG}
            dropdownAlign={
              enableTableDropdownNG ? { offset: [-144, 4] } : undefined
            }
            getPopupContainer={(trigger) =>
              document.getElementById('baby-form-container')
            }
          />
        );
      },
      onCell: nonAddCell,
    },
    {
      dataIndex: 'IcdeName',
      title: '诊断名称',
      visible: true,
      fixed: 'left',
      readonly: true,
      width: 400,
      renderColumnFormItem: (node, record, index) => {
        return <IcdeOperationReadonlyItem />;
      },
      onCell: nonAddCell,
    },
    {
      dataIndex: 'IcdeOutcome',
      title: '治疗情况',
      visible: true,
      align: 'center',
      width: 90,
      renderColumnFormItem: (
        node,
        record,
        index,
        dataIndex,
        form,
        extraItem,
      ) => {
        return (
          <IcdeOperationInputSelector
            tableId={'babyDiagnosisTable'}
            className={'in-hospital-diagnosis icde-table-item'}
            dataIndex={'IcdeOutcome'}
            index={index}
            listHeight={130}
            conditionDictionaryKey={'IcdeOutcome'}
            conditionDictionaryGroup={'Dmr'}
            extraItem={extraItem}
            parentNodeId={'baby-form-container'}
          />
        );
      },
      onCell: nonAddCell,
    },
    {
      dataIndex: 'operation',
      title: '',
      visible: true,
      align: 'center',
      width: 70,
      readonly: true,
      disableComment: true,
      render: (node, record, index, action) => {
        return (
          <div className={'operation-item'}>
            <IconBtn
              type="copy"
              onClick={() => {
                containerRef?.current?.babyIcdeCopy({
                  id: record?.['id'],
                  index: index,
                });
              }}
            />
            <IconBtn
              type="delete"
              onClick={() => {
                containerRef?.current?.babyIcdeDelete(index);
              }}
            />
          </div>
        );
      },
      onCell: nonAddCell,
      fixed: 'right',
    },
  ];
};

export const admsIcdeColumns = (tableOperationRef?: any) => [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'IcdeSort',
    title: '',
    visible: true,
    align: 'center',
    width: 60,
    readonly: true,
    disableComment: true,
    render: (node, record, index, action) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'icde-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    tableOperationRef?.current?.onItemAdd();
                  }
                : undefined
            }
          >
            <div
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true ? { cursor: 'pointer' } : {}
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      tableOperationRef?.current?.onItemAdd();
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      } else {
        let labelNode = <span>{`${index + 1}`}</span>;
        if (record?.id !== 'ADD') {
          const SortDragHandler = DragHandler(labelNode);
          return <SortDragHandler />;
        }
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 6,
        };
      }

      return {};
    },
  },
  {
    dataIndex: 'IcdeCode',
    title: '入院诊断编码',
    align: 'center',
    visible: true,
    width: 180,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeSelect
          tableId={'admsDiagnosisTable'}
          componentId={`AdmsIcdeCode#${index}`}
          paramKey={'admsDiagnosis'}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          icdeSelectType={'IsAdms'}
          formKeys={{
            IcdeName: 'Name',
            IcdeCode: 'Code',
          }}
          listHeight={200}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG ? { offset: [-144, 4] } : undefined
          }
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeName',
    title: '入院诊断名称',
    visible: true,
    readonly: true,
    width: 240,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeNote',
    title: '入院诊断描述',
    visible: false,
    width: 160,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeFieldInput
          dataIndex={'IcdeNote'}
          index={index}
          tableId={'admsDiagnosisTable'}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'Remark',
    title: '诊断后描述',
    visible: false,
    width: 160,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeFieldInput
          dataIndex={'Remark'}
          index={index}
          tableId={'admsDiagnosisTable'}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeCond',
    title: '入院病情',
    visible: false,
    align: 'center',
    width: 100,
    conditionDictionaryKey: 'RYBQ',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'admsDiagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeCond'}
          index={index}
          conditionDictionaryKey={'RYBQ'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeOutcome',
    title: '治疗情况',
    visible: false,
    align: 'center',
    width: 90,
    conditionDictionaryKey: 'IcdeOutcome',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'admsDiagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeOutcome'}
          index={index}
          listHeight={130}
          conditionDictionaryKey={'IcdeOutcome'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeDate',
    title: '入院时间',
    visible: false,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          formItemId={`formItem#AdmsIcdeDate#${index}#CompactInput`}
          type={'compact'}
          formKey={'IcdeDate'}
          dataTableIndex={index}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'operation',
    title: '',
    visible: true,
    align: 'center',
    width: 80,
    readonly: true,
    disableComment: true,
    render: (node, record, index) => {
      return (
        <Tooltip title="删除">
          <Popconfirm
            title="确定删除？"
            onConfirm={() => {
              tableOperationRef?.current?.onItemDelete(index);
            }}
            getPopupContainer={(triggerNode) =>
              document.getElementById('dmr-content-container')
            }
          >
            <a className={'operation'}>
              <DeleteOutlined />
            </a>
          </Popconfirm>
        </Tooltip>
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

export const otpsIcdeColumns = (tableOperationRef?: any) => [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'IcdeSort',
    title: '',
    visible: true,
    align: 'center',
    width: 60,
    readonly: true,
    disableComment: true,
    render: (node, record, index, action) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'icde-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    tableOperationRef?.current?.onItemAdd();
                  }
                : undefined
            }
          >
            <div
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true ? { cursor: 'pointer' } : {}
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      tableOperationRef?.current?.onItemAdd();
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      } else {
        let labelNode = <span>{`${index + 1}`}</span>;
        if (record?.id !== 'ADD') {
          const SortDragHandler = DragHandler(labelNode);
          return <SortDragHandler />;
        }
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 6,
        };
      }

      return {};
    },
  },
  {
    dataIndex: 'IcdeCode',
    title: '门(急)诊断编码',
    align: 'center',
    visible: true,
    width: 180,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeSelect
          tableId={'otpsDiagnosisTable'}
          componentId={`OtpsIcdeCode#${index}`}
          paramKey={'otpsDiagnosis'}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          icdeSelectType={'IsOtps'}
          formKeys={{
            IcdeName: 'Name',
            IcdeCode: 'Code',
          }}
          listHeight={200}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG ? { offset: [-144, 4] } : undefined
          }
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeName',
    title: '门(急)诊断名称',
    visible: true,
    readonly: true,
    width: 240,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeNote',
    title: '门急诊断描述',
    visible: false,
    width: 160,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeFieldInput
          dataIndex={'IcdeNote'}
          index={index}
          tableId={'otpsDiagnosisTable'}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'Remark',
    title: '诊断后描述',
    visible: false,
    width: 160,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeFieldInput
          dataIndex={'Remark'}
          index={index}
          tableId={'otpsDiagnosisTable'}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeCond',
    title: '入院病情',
    visible: false,
    align: 'center',
    width: 100,
    conditionDictionaryKey: 'RYBQ',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'otpsDiagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeCond'}
          index={index}
          conditionDictionaryKey={'RYBQ'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeOutcome',
    title: '治疗情况',
    visible: false,
    align: 'center',
    width: 90,
    conditionDictionaryKey: 'IcdeOutcome',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'otpsDiagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeOutcome'}
          index={index}
          listHeight={130}
          conditionDictionaryKey={'IcdeOutcome'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeDate',
    title: '门急诊断时间',
    visible: false,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          formItemId={`formItem#OtpsIcdeDate#${index}#CompactInput`}
          type={'compact'}
          formKey={'IcdeDate'}
          dataTableIndex={index}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'operation',
    title: '',
    visible: true,
    align: 'center',
    width: 80,
    readonly: true,
    disableComment: true,
    render: (node, record, index) => {
      return (
        <Tooltip title="删除">
          <Popconfirm
            title="确定删除？"
            onConfirm={() => {
              tableOperationRef?.current?.onItemDelete(index);
            }}
            getPopupContainer={(triggerNode) =>
              document.getElementById('dmr-content-container')
            }
          >
            <a className={'operation'}>
              <DeleteOutlined />
            </a>
          </Popconfirm>
        </Tooltip>
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];
