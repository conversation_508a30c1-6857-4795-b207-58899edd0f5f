import { Reducer, useEffect, useReducer, useRef } from 'react';
import { <PERSON><PERSON>, Card, message } from 'antd';
import { useSafeState } from 'ahooks';
import {
  IReducer,
  ITableState,
  IEditableState,
} from '@uni/reducers/src/interface';
import _ from 'lodash';
import {
  InitTableState,
  TableAction,
  tableReducer,
  tableEditPropsReducer,
  EditableTableAction,
} from '@uni/reducers/src';
import { ReqActionType } from '../constants';
import { useRequest, useModel } from 'umi';
import { SwagMajorPerfDeptItem } from './interface';
import UniEditableTable from '@uni/components/src/table/edittable';
import { ModalForm } from '@uni/components/src/pro-form';
import ProFormContainer from '@uni/components/src/pro-form-container';
import { MajorPerfDeptItems } from './formItems';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { majorPerfDeptColumns } from './columns';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';

const MajorPerfDept = () => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateForSlave');

  const [TableState, TableDispatch] = useReducer<
    Reducer<ITableState<SwagMajorPerfDeptItem>, IReducer>
  >(tableReducer, InitTableState);

  const [EditableState, EditableStateDispatch] = useReducer<
    Reducer<
      IEditableState<SwagMajorPerfDeptItem>,
      IReducer<IEditableState<SwagMajorPerfDeptItem>>
    >
  >(tableEditPropsReducer, {
    value: [],
    editableKeys: [],
  });

  const [formItems, setFormItems] = useSafeState([]);

  const { data: originalColumns, run: employeeConfigurationColumnsReq } =
    useRequest(
      () => {
        return uniCommonService(ReqActionType.GetMajorPerfDepts, {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        });
      },
      {
        formatResult: (response: RespVO<TableColumns>) => {
          if (response.code === 0) {
            console.log(
              response?.data?.Columns,
              tableColumnBaseProcessor(
                majorPerfDeptColumns,
                response?.data?.Columns,
              ),
            );
            TableDispatch({
              type: TableAction.columnsChange,
              payload: {
                columns: tableColumnBaseProcessor(
                  majorPerfDeptColumns,
                  response?.data?.Columns,
                ),
              },
            });
          }
        },
      },
    );

  const { loading: dataLoading, run: dataReq } = useRequest(
    () => {
      return uniCommonService(ReqActionType.GetMajorPerfDepts, {
        method: 'POST',
        data: {},
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<SwagMajorPerfDeptItem[]>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          TableDispatch({
            type: TableAction.dataChange,
            payload: {
              data: response?.data,
            },
          });
        } else {
          TableDispatch({
            type: TableAction.dataChange,
            payload: {
              data: [],
            },
          });
        }
      },
    },
  );

  // action req
  const { loading: upsertLoading, run: upsertReq } = useRequest(
    (values) => {
      let data = {};

      data = {
        ...values,
      };

      return uniCommonService(ReqActionType.UpsertMajorPerfDept, {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.statusCode === 200) {
          message.success('操作成功');
          dataReq();
          return 'done';
        }
        return 'failed';
      },
    },
  );

  useEffect(() => {
    dataReq();
  }, []);

  useEffect(() => {
    if (TableState.data) {
      setFormItems(MajorPerfDeptItems(TableState.data));
    }
  }, [TableState.data]);

  const { run: deleteReq } = useRequest(
    (values) => {
      let data = {};
      console.log(values);
      data = {
        majorPerfDeptId: values?.MajorPerfDeptId,
      };
      return uniCommonService(ReqActionType.DeleteMajorPerfDept, {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('删除成功');
        }
      },
      onSuccess: (response, params) => {
        dataReq();
      },
    },
  );

  useEffect(() => {
    Emitter.on(ConfigurationEvents.MAJOR_PERFDEPT_DELETE, (data) => {
      if (data?.index > -1) {
        deleteReq(data.record);
      }
    });

    return () => {
      Emitter.off(ConfigurationEvents.MAJOR_PERFDEPT_DELETE);
    };
  }, [TableState.data]);

  return (
    <Card
      title="学科列表"
      extra={
        <ModalForm<{
          name: string;
          company: string;
        }>
          title="新增学科"
          trigger={<Button>新增学科</Button>}
          // form={form}
          grid
          layout="horizontal"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          key={'add'}
          autoFocusFirstInput
          modalProps={{
            width: 500,
            destroyOnClose: true,
            onCancel: () => console.log('run'),
          }}
          onFinish={async (values) => {
            const result = await upsertReq(values);
            if (result === 'done') return true;
            return false;
          }}
        >
          <ProFormContainer isModalForm searchOpts={formItems} />
        </ModalForm>
      }
    >
      <UniEditableTable
        id="major_perf_dept"
        rowKey="MajorPerfDeptId"
        loading={dataLoading || false}
        recordCreatorProps={false}
        columns={TableState.columns}
        dataSource={TableState.data}
        editable={{
          type: 'multiple',
          editableKeys: EditableState.editableKeys,
          onSave: async (rowKey, data, row) => {
            let upsertResponse = await upsertReq(data);
            if (upsertResponse === 'done') {
              return true;
            }
            return false;
          },
          actionRender: (row, config, defaultDoms) => {
            return [defaultDoms.save, defaultDoms.cancel];
          },
          onChange: (keys, rows) => {
            console.log(keys);
            EditableStateDispatch({
              type: EditableTableAction.editableKeysChange,
              payload: {
                editableKeys: keys,
              },
            });
          },
        }}
      />
    </Card>
  );
};

export default MajorPerfDept;
