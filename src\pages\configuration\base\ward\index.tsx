import { UniTable } from '@uni/components/src';
import { wardsDictColumns } from '@/pages/configuration/base/columns';
import { Button, Card, Form, Modal, TableProps, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useRequest } from 'umi';
import {
  DictionaryItem,
  RespVO,
  TableColumns,
} from '@uni/commons/src/interfaces';
import { columnsHandler } from '@/utils/widgets';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import './index.less';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { DictionaryItemCompare } from '@/pages/configuration/base/interfaces';
import { CliDeptItem } from '@/pages/configuration/base/interfaces';
import ItemAdd from './components/add';
import { useModel } from '@@/plugin-model/useModel';
import dayjs from 'dayjs';
import cloneDeep from 'lodash/cloneDeep';
import _ from 'lodash';
import { useUpdateEffect } from 'ahooks';
import './index.less';
import UniEditableTable from '@uni/components/src/table/edittable';
import { v4 as uuidv4 } from 'uuid';
import HierarchyBedIndex from '@/pages/configuration/base/cliDepts/components/hierarchy-bed';

interface CliDeptsDictionaryProps {}

const CliDeptsDictionary = (props?: CliDeptsDictionaryProps) => {
  const moduleGroup = 'Dmr';

  const [form] = Form.useForm();

  const [cliDeptDictionaryEditItemIndex, setCliDeptDictionaryEditItemIndex] =
    useState(undefined);

  const [addItem, setAddItem] = useState(false);

  // dict data
  const [wardsDataSource, setWardsDataSource] = useState([]);

  // dict compare data
  const [wardsCompareDataSource, setCliDeptsCompareDataSource] = useState([]);

  const [wardsTableDataSource, setWardsTableDataSource] = useState([]);

  const [wardsColumns, setWardsColumns] = useState([]);

  const { globalState } = useModel('@@qiankunStateForSlave');

  const hospitalData = globalState?.dictData?.Hospital || [];

  const [frontPagination, setFrontPagination] = useState({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
  });
  // 前端分页OnChange
  const frontTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setFrontPagination({
      ...frontPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });
  };

  useEffect(() => {
    wardsDictionaryColumnsReq();
    wardsDictionaryReq();
  }, []);

  useEffect(() => {
    // Emitter.on(
    //   ConfigurationEvents.CLI_DEPTS_CONFIGURATION_DICTIONARY_EDIT,
    //   (index) => {
    //     let currentEditItem = wardsTableDataSource?.at(index);
    //     console.log(currentEditItem);

    //     setCliDeptDictionaryEditItemIndex(index);
    //     if (currentEditItem) {
    //       setAddItem(true);
    //       form.setFieldsValue({
    //         ...currentEditItem,
    //         dates: [
    //           currentEditItem?.Sdate
    //             ? dayjs(currentEditItem?.Sdate)
    //             : undefined,
    //           currentEditItem?.Edate
    //             ? dayjs(currentEditItem?.Edate)
    //             : undefined,
    //         ],
    //       });
    //     }
    //   },
    // );

    return () => {
      // Emitter.off(ConfigurationEvents.CLI_DEPTS_CONFIGURATION_ITEM_DELETE);
      // Emitter.off(ConfigurationEvents.CLI_DEPTS_CONFIGURATION_DICTIONARY_EDIT);
    };
  }, [wardsTableDataSource]);

  useEffect(() => {
    setWardsTableDataSource(
      wardsDataSource.map((item, index) => {
        return {
          ...item,
          actualIndex: index,
          id: uuidv4(),
        };
      }),
    );
    // processCompares(wardsDataSource, wardsCompareDataSource);
  }, [wardsDataSource, wardsCompareDataSource]);

  const processCompares = (
    dictionaries: DictionaryItem[],
    compares: DictionaryItemCompare[],
  ) => {
    let dataSource = [];
    let codeToCompares = {};

    dictionaries.forEach((dictItem) => {
      let codeCompares = compares
        .filter((item) => item.Code === dictItem.Code)
        .map((item) => {
          let codeCompareItem = {
            ...item,
            ...dictItem,
          };

          delete codeCompareItem['span'];
          delete codeCompareItem['index'];

          return codeCompareItem;
        });

      if (codeCompares?.length > 0) {
        codeToCompares[dictItem?.Code] = codeCompares;
      } else {
        codeToCompares[dictItem?.Code] = [dictItem];
      }
    });

    Object.keys(codeToCompares).forEach((key, index) => {
      let codeData = codeToCompares[key];
      if (codeData?.length > 0) {
        codeData[0]['span'] = codeData?.length;
        codeData[0]['index'] = index + 1;
      }
      dataSource.push(...codeData.slice());
    });

    setWardsTableDataSource(
      dataSource.map((item, index) => {
        return {
          ...item,
          actualIndex: index,
          id: uuidv4(),
        };
      }),
    );
  };

  const { loading: wardsDictionaryLoading, run: wardsDictionaryReq } =
    useRequest(
      () => {
        let data = {
          ModuleGroup: moduleGroup,
        };
        return uniCommonService('Api/Sys/HospHierarchySys/GetHierarchyWards', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<CliDeptItem[]>) => {
          if (response.code === 0 && response?.statusCode === 200) {
            let dataSource = response?.data?.slice();
            setWardsDataSource(
              dataSource.map((item) => {
                item['HospName'] = hospitalData?.find(
                  (hospitalItem) => hospitalItem?.Code === item?.HospCode,
                )?.Name;

                return item;
              }),
            );
          } else {
            setWardsDataSource([]);
          }
        },
      },
    );

  const { data: wardsDictionaryColumns, run: wardsDictionaryColumnsReq } =
    useRequest(
      () => {
        return uniCommonService('Api/Sys/HospHierarchySys/GetHierarchyWards', {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<TableColumns>) => {
          if (response.code === 0) {
            return response?.data?.Columns;
          } else {
            return [];
          }
        },
      },
    );

  const { loading: wardsUpsertLoading, run: wardsUpsertReq } = useRequest(
    (values) => {
      let data = {};
      if (cliDeptDictionaryEditItemIndex !== undefined) {
        let currentEditItem = wardsTableDataSource?.at(
          cliDeptDictionaryEditItemIndex,
        );
        if (currentEditItem) {
          data = {
            ...currentEditItem,
          };
        }
      }

      data = {
        ...data,
        ...values,
        ModuleGroup: moduleGroup,
      };

      return uniCommonService('Api/Sys/HospHierarchySys/UpsertHierarchyWard', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.statusCode === 200) {
          setAddItem(false);
          setCliDeptDictionaryEditItemIndex(undefined);
          wardsDictionaryReq();
        }
      },
    },
  );

  const onCliDeptsDictionaryItemAdd = (values: any) => {
    wardsUpsertReq(values);
  };

  const ref = useRef<any>();
  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);

  const { run: deleteReq } = useRequest(
    (values) => {
      let data = {};
      data = {
        hierarchyId: values.HierarchyId,
      };
      return uniCommonService(`Api/Sys/HospHierarchySys/DeleteHierarchy`, {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('删除成功');
        }
      },
      onSuccess: (response, params) => {
        wardsDictionaryReq();
      },
    },
  );

  // columns 处理
  useEffect(() => {
    if (
      wardsDictionaryColumns?.length > 0 &&
      globalState?.dictData?.Hospital?.length > 0
    ) {
      setWardsColumns(
        tableColumnBaseProcessor(
          wardsDictColumns(globalState?.dictData),
          wardsDictionaryColumns,
        ),
      );
    }
  }, [wardsDictionaryColumns, globalState?.dictData]);

  useEffect(() => {
    if (!addItem) {
      form.resetFields();
    }
  }, [addItem]);

  useEffect(() => {
    Emitter.on(
      ConfigurationEvents.CLI_DEPTS_CONFIGURATION_ITEM_DELETE,
      (data) => {
        if (data?.index > -1) {
          deleteReq(data.record);
        }
      },
    );

    Emitter.on(ConfigurationEvents.HIERARCHY_BED_EDIT, (data) => {
      let currentEditItem = wardsTableDataSource?.at(data.index);
      if (currentEditItem) {
        setHierarchyBedIndex(true);
        setHierarchyBedItem(currentEditItem);
      }
    });

    return () => {
      Emitter.off(ConfigurationEvents.CLI_DEPTS_CONFIGURATION_ITEM_DELETE);
      Emitter.off(ConfigurationEvents.HIERARCHY_BED_EDIT);
    };
  }, [wardsTableDataSource]);

  const [hierarchyBedIndex, setHierarchyBedIndex] = useState(false);
  const [hierarchyBedItem, setHierarchyBedItem] = useState<any>({});

  return (
    <>
      <Card
        className={'cli-depts-dictionary-container'}
        title="病区列表"
        extra={
          <>
            <Button
              key="add"
              loading={false}
              onClick={(e) => {
                setAddItem(true);
                form.setFieldValue('IsValid', true);
              }}
            >
              新增病区
            </Button>
          </>
        }
      >
        <UniEditableTable
          actionRef={ref}
          id={`cli-depts-dictionary-table`}
          className={'cli-depts-dictionary-table'}
          rowKey={'id'}
          scroll={{ y: 540, x: 'max-content' }}
          bordered={true}
          loading={wardsDictionaryLoading || wardsUpsertLoading}
          columns={wardsColumns}
          value={wardsTableDataSource || []}
          clickable={false}
          // pagination={frontPagination}
          // onChange={frontTableOnChange}
          dictionaryData={globalState?.dictData}
          widthDetectAfterDictionary
          toolBarRender={null}
          recordCreatorProps={false}
          onChange={(value) => {
            console.log('value', value);
          }}
          editable={{
            form: form,
            type: 'multiple',
            editableKeys: editableColumnKeys,
            onSave: async (rowKey, data, row) => {
              wardsUpsertReq({
                ...data,
                MajorPerfDeptName: _.isObject(data?.MajorPerfDeptName)
                  ? data?.MajorPerfDeptName?.label
                  : data?.MajorPerfDeptName,
                MajorPerfDept: _.isObject(data?.MajorPerfDeptName)
                  ? data?.MajorPerfDeptName?.value
                  : data?.MajorPerfDept,
                HospName: _.isObject(data?.HospName)
                  ? data?.HospName?.label
                  : data?.HospName,
                HospCode: _.isObject(data?.HospName)
                  ? data?.HospName?.value
                  : data?.HospCode,
              });
            },
            actionRender: (row, config, defaultDoms) => {
              return [defaultDoms.save, defaultDoms.cancel];
            },
            onChange: setEditableColumnKeys,
          }}
        />
      </Card>

      <Modal
        title={`新增病区`}
        open={addItem}
        onOk={async () => {
          form.validateFields().then((values) => {
            onCliDeptsDictionaryItemAdd({
              ...values,
              Sdate:
                values?.dates &&
                dayjs(values?.dates?.at(0))?.format('YYYY-MM-DD'),
              Edate:
                values?.dates &&
                dayjs(values?.dates?.at(1))?.format('YYYY-MM-DD'),
            });
          });
        }}
        onCancel={() => {
          setAddItem(false);
          setCliDeptDictionaryEditItemIndex(undefined);
        }}
        destroyOnClose={true}
        getContainer={document.getElementById('cli-depts-dictionary-table')}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <ItemAdd form={form} editIndex={cliDeptDictionaryEditItemIndex} />
      </Modal>

      <Modal
        title={`${hierarchyBedItem?.Name} 床位数列表`}
        open={hierarchyBedIndex}
        width={800}
        footer={[
          <Button
            type="primary"
            loading={false}
            onClick={() => setHierarchyBedIndex(false)}
          >
            确认
          </Button>,
        ]}
        onOk={async () => {
          setHierarchyBedIndex(false);
        }}
        onCancel={() => {
          setHierarchyBedIndex(false);
        }}
        destroyOnClose={true}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <HierarchyBedIndex data={hierarchyBedItem} />
      </Modal>
    </>
  );
};

export default CliDeptsDictionary;
