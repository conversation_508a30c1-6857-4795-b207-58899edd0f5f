@import '~@uni/commons/src/style/variables.less';

.dmr-container-common .uni-drag-edit-table-container {
  width: 100%;
  .ant-table,
  .ant-table-cell,
  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right,
  .ant-input {
    //background-color: @dmr-index-bg-color;
  }

  .ant-select-selector {
    padding: 0 !important;
  }

  .icde-input {
    border: 1px solid transparent !important;
  }

  .in-hospital-diagnosis {
    //border: 1px solid @border-color !important;
  }

  .icde-add-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    padding: 8px 8px;

    span {
      margin-right: 10px;
    }
  }

  .operation-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-evenly;
    a {
      margin: 0px 5px;
    }
  }
}
