import merge from 'lodash/merge';
import cloneDeep from 'lodash/cloneDeep';
import lodash, { isEmpty } from 'lodash';
import mergeWith from 'lodash/mergeWith';
import assignInWith from 'lodash/assignInWith';
import { isEmptyValues } from '@uni/utils/src/utils';
import pickBy from 'lodash/pickBy';
import { TableColumnFullProperties } from './properties/column';

import {
  addressFormKey,
  addressItemsPreSaveProcessor,
  autoSelectPreSaveProcessor,
} from '@/pages/configuration/mapping/common';

const valuesKeyItemGetter = (values: any, item: any) => {
  // 单个input
  // 修改：使用props.formKey来获取
  if (addressFormKey?.includes(item?.data?.props?.formKey)) {
    return values?.[`${item?.data?.props?.formKey}SeparateSelector`];
  }

  if (
    addressFormKey?.find(
      (formKeyItem) => `${formKeyItem}SeparateSelector` === item?.data?.key,
    )
  ) {
    return values?.[item?.data?.key?.replace('SeparateSelector', '')];
  }

  return values?.[item?.data?.key];
};

const moveLockProperties = {
  resizeHandles: [],
  static: true,
  isDraggable: false,
  isResizable: false,
};
export const layoutSaveProcessor = (
  values: any,
  type: 'CONTENT' | 'HEADER',
) => {
  let savedLayout = {};
  let contentData = [];

  let layouts = type === 'HEADER' ? values?.headerLayouts : values?.layouts;
  let originContentData =
    type === 'HEADER' ? values?.headerContentData : values?.contentData;

  let columnEditDataIndexes = TableColumnFullProperties?.map(
    (item) => item?.key,
  ).concat('sort');

  // 先行处理contentData
  contentData = originContentData?.map((item: any) => {
    let valueItem = valuesKeyItemGetter(values, item);
    if (valueItem) {
      let mergedItem = mergeWith(
        {},
        item,
        valueItem,
        (objValue, srcValue, key, object, source, stack) => {
          console.log(
            'mergeWith',
            objValue,
            srcValue,
            key,
            object,
            source,
            stack,
          );

          // 表格列的话 做合并
          if (key?.toLowerCase()?.includes('columns')) {
            if (!isEmptyValues(srcValue) && !isEmptyValues(objValue)) {
              let mergedValue = [];
              mergedValue = srcValue?.map((srcItem) => {
                let originColumnItem = objValue?.find(
                  (objItem) => objItem?.dataIndex === srcItem?.dataIndex,
                );
                if (originColumnItem) {
                  Object.keys(originColumnItem)?.forEach((key) => {
                    if (!columnEditDataIndexes?.includes(key)) {
                      srcItem[key] = originColumnItem?.[key];
                    }
                  });
                }

                return pickBy(srcItem, (value, key) => {
                  return !isEmptyValues(value);
                });
              });

              return mergedValue;
            }
          }

          if (lodash.isArray(srcValue) && lodash.isArray(objValue)) {
            // array的情况下直接替换掉
            return srcValue;
          }

          return undefined;
        },
      );

      return mergedItem;
    }

    return item;
  });

  Object.keys(layouts)?.forEach((key) => {
    let currentKeyLayout = [];
    layouts[key]?.forEach((item) => {
      let contentDataItem = contentData?.find(
        (contentItem) => contentItem?.i === item?.i,
      );
      if (contentDataItem) {
        let mergedItemWithLayoutItem = {
          ...item,
          data: contentDataItem?.data,
          ...moveLockProperties,
        };

        // 地址相关
        addressItemsPreSaveProcessor(mergedItemWithLayoutItem);
        autoSelectPreSaveProcessor(mergedItemWithLayoutItem);

        currentKeyLayout.push(mergedItemWithLayoutItem);
      }
    });

    savedLayout[key] = currentKeyLayout?.sort((a, b) => {
      if (a.y === b.y) {
        // Price is only important when cities are the same
        return a.x - b.x;
      }
      return a.y - b.y;
    });
  });

  console.log('savedLayout', JSON.stringify(savedLayout));

  return savedLayout;
};

export const layoutAddOtherProperties = {
  isDraggable: true,
  isResizable: true,
  minH: 1,
  moved: false,
  resizeHandles: ['se'],
  static: false,
};

export const layoutItemAddProcessor = (data) => {
  let layoutItemData = cloneDeep(data);
  layoutItemData['i'] = data?.data?.key;

  if (layoutItemData?.['data']) {
    delete layoutItemData?.['data'];
  }

  return layoutItemData;
};

export const contentItemAddProcessor = (data) => {
  let contentItemData = cloneDeep(data);

  return contentItemData;
};
