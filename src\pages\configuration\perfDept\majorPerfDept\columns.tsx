import { Switch, Space } from 'antd';
import IconBtn from '@uni/components/src/iconBtn';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';

export const majorPerfDeptColumns = [
  {
    dataIndex: 'Code',
    title: '学科代码',
    readonly: true,
  },
  {
    dataIndex: 'Name',
    title: '学科名称',
    valueType: 'text',
  },
  {
    dataIndex: 'IsValid',
    align: 'center',
    width: 80,
    readonly: true,
    render: (node, record, index) => {
      return <span>{record['IsValid'] ? '是' : '否'}</span>;
    },
    renderFormItem: ({ index, entity }, props) => {
      return <Switch {...props?.fieldProps} defaultChecked={entity?.IsValid} />;
    },
  },
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    fixed: 'right',
    order: Number.MAX_VALUE,
    width: 90,
    render: (node, record, index, action) => (
      <Space size={'middle'}>
        <IconBtn
          key="edit"
          type="edit"
          onClick={() => {
            action?.startEditable?.(record.MajorPerfDeptId);
          }}
        />
        <IconBtn
          key="delete"
          type="delete"
          openPop={true}
          popOnConfirm={() => {
            Emitter.emit(ConfigurationEvents.MAJOR_PERFDEPT_DELETE, {
              index,
              record,
            });
          }}
        />
      </Space>
    ),
  },
];
