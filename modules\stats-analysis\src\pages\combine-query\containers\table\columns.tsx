import IconBtn from '@uni/components/src/iconBtn';
import React from 'react';
import { Input, InputNumber, message, Popconfirm, Tooltip } from 'antd';
import { SortableHandle } from 'react-sortable-hoc';
import {
  CloseOutlined,
  MenuOutlined,
  VerticalAlignBottomOutlined,
  VerticalAlignTopOutlined,
} from '@ant-design/icons';
import { Emitter } from '@uni/utils/src/emitter';
import { StatsAnalysisEventConstant } from '@/constants';
import { UniSelect } from '@uni/components/src';

const DragHandler = (node) => {
  return SortableHandle(() => <div className={'grab-handle'}>{node}</div>);
};

export const columns = (
  currentHisIds: string[],
  onExtraSearchedHisIdLastOne: () => void,
  onRowClick: (id: string) => void,
  onExtraSearchedHisIdChange?: (hisId: string) => void,
) => {
  return [
    {
      dataIndex: 'operation',
      visible: true,
      width: 40,
      align: 'center',
      title: '',
      fixed: 'left',
      enableResizing: false,
      render: (node, record, index) => {
        return (
          <IconBtn
            title="查看病案首页"
            type="checkInfo"
            className="operation-btn"
            onClick={(e) => {
              if (record?.HisId) {
                (global?.window as any)?.eventEmitter?.emit('DMR_AOD_STATUS', {
                  status: true,
                  hisId: record.HisId,
                  operateNextPreviousExtraData: true,
                  extraSearchedHisIds: currentHisIds,
                  onExtraSearchedHisIdLastOne: onExtraSearchedHisIdLastOne,
                  onExtraSearchedHisIdChange: onExtraSearchedHisIdChange,
                });
                onRowClick?.(record?.id);
              } else {
                message.warn('HisId不存在');
              }
            }}
          />
        );
      },
    },
  ];
};

export const columnSettingColumns = [
  {
    key: 'sort',
    dataIndex: '',
    title: '',
    visible: true,
    align: 'center',
    width: '10%',
    readonly: true,
    renderFormItem: ({ index, entity }) => {
      const SortDragHandler = DragHandler(<MenuOutlined />);
      return <SortDragHandler />;
    },
  },
  {
    dataIndex: 'originTitle',
    title: '项目名',
    visible: true,
    align: 'center',
    width: '25%',
    readonly: true,
    renderFormItem: ({ index, entity }) => {
      return <span>{entity['originTitle']}</span>;
    },
  },
  {
    dataIndex: 'customTitle',
    title: '重命名',
    visible: true,
    align: 'center',
    width: '25%',
    renderFormItem: ({ index, entity }) => {
      return (
        <Input.TextArea
          autoSize={{ minRows: 1 }}
          defaultValue={entity['title']}
          placeholder={entity['title']}
        />
      );
    },
  },
  {
    dataIndex: 'width',
    title: '宽度',
    visible: false,
    align: 'center',
    width: '20%',
    renderFormItem: ({ index, entity }) => {
      return (
        <InputNumber
          className={'combo-input-no-control'}
          min={70}
          controls={false}
          defaultValue={entity['width']}
          placeholder={'请填写宽度'}
        />
      );
    },
  },
  {
    dataIndex: 'textOverflowType',
    title: '截断方式',
    visible: true,
    align: 'center',
    width: '25%',
    renderFormItem: ({ index, entity }) => {
      return (
        <UniSelect
          dataSource={[
            {
              value: 'ellipse',
              label: '省略显示',
            },
            {
              value: 'clip',
              label: '截断显示',
            },
            {
              value: 'none',
              label: '换行显示',
            },
          ]}
          allowClear={false}
          defaultValue={entity['textOverflowType'] ?? 'none'}
          placeholder={'请选择截断方式'}
        />
      );
    },
  },
  {
    dataIndex: 'operation',
    title: '',
    visible: true,
    align: 'center',
    width: '15%',
    readonly: true,
    renderFormItem: ({ index, entity }) => {
      return (
        <div className={'selected-table-operation-container'}>
          <Tooltip title={'置顶'}>
            <VerticalAlignTopOutlined
              className={'item'}
              onClick={() => {
                Emitter.emit(
                  StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT_TOP,
                  {
                    index: index,
                    entity: entity,
                  },
                );
              }}
            />
          </Tooltip>
          <Tooltip title={'置底'}>
            <VerticalAlignBottomOutlined
              className={'item'}
              onClick={() => {
                Emitter.emit(
                  StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT_BOTTOM,
                  {
                    index: index,
                    entity: entity,
                  },
                );
              }}
            />
          </Tooltip>
          <Tooltip title={'删除'}>
            <CloseOutlined
              className={'item'}
              onClick={() => {
                Emitter.emit(
                  StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT_DELETE,
                  {
                    index: index,
                    entity: entity,
                  },
                );
              }}
            />
          </Tooltip>
        </div>
      );
    },
  },
];
