import React, { useEffect } from 'react';
import { useDispatch, useSelector } from '@@/plugin-dva/exports';
import { Dispatch } from '@@/plugin-dva/connect';
import { useAccess } from '@@/plugin-access/access';
import { useAsyncEffect } from 'ahooks';
import { dictModuleInitialize } from '@/utils/initialize';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import merge from 'lodash/merge';
import { useModel } from 'umi';
import { v4 as uuidv4 } from 'uuid';
import { useLocation } from 'umi';
import { history } from '@@/core/history';
import { notification } from 'antd';
import dayjs from 'dayjs';
import useTabTracker from '@uni/utils/src/tab-tracker';
import { doLog } from '@uni/services/src';
import { LogLevel } from '@uni/services/src/commonService';

// 当前标签页的ID
global['clientId'] = uuidv4();

const clearTokenOnClose =
  (window as any).externalConfig?.['common']?.clearTokenOnClose ?? false;
const clearTokenOnNextDay =
  (window as any).externalConfig?.['common']?.clearTokenOnNextDay ?? false;

global['wisdomThirdPartyCheckSocket'] = null;

global['wisdomThirdPartyCheckSocketId'] = 'Union-Dmr-Web';

export default function InitLayout(props: any) {
  const { initialState, setInitialState, refresh }: any =
    useModel('@@initialState');

  const dvaDictData = useSelector((state: any) => state.uniDict.dictData);

  const dispatch: Dispatch = useDispatch();

  const access = useAccess();

  const location = useLocation();

  let tabAwareWorker;

  // 用户信息给到 子应用
  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateForSlave',
  );

  if (clearTokenOnClose) {
    useTabTracker(global['clientId']);
  }

  useAsyncEffect(async () => {
    await dictModuleInitialize(dispatch);

    initializeTabAwareWorker();

    initWebSocketForThirdPartyCheck();

    if (clearTokenOnNextDay === true) {
      // 这边是处理：当某些不可控的特殊情况 导致在没关闭浏览器时直接关机 这种时候可能会无法触发 beforeunload
      // 当前时间的年月日 !== 登陆时记录时间的年月日 则清除localstorage *强制机制
      // 当然 这个最好的方式是后端处理 但是他们不处理
      let connectTimestamp = localStorage.getItem('uni-connect-timestamp');
      if (
        connectTimestamp &&
        !dayjs().isSame(dayjs(+connectTimestamp), 'day')
      ) {
        localStorage.clear();
        console.log('hhhh');
        doLog(LogLevel.Trace, { title: '第二天强制登出' });
        global?.window.location.replace('/login');
      }
    }

    // DMRDEV-965 温附2定制 当 socket没有时候重连
    (global?.window as any)?.eventEmitter?.on(
      'CONNECT_WISDOM_SOCKET',
      (callback: () => void) => {
        initWebSocketForThirdPartyCheck(callback);
      },
    );

    return () => {
      (global?.window as any)?.eventEmitter.off('CONNECT_WISDOM_SOCKET');
    };
  }, []);

  useEffect(() => {
    if (global['tabAwareWorker']) {
      global['tabAwareWorker'].port.postMessage({
        type: 'ROUTE_CHANGE',
        clientId: global['clientId'],
        location: location?.pathname,
      });
    }
  }, [location]);

  const initializeTabAwareWorker = () => {
    tabAwareWorker = new SharedWorker('/tabAwareWorker.js', 'tab-aware-worker');
    tabAwareWorker.port.postMessage({
      type: 'TAB_CONNECT',
      clientId: global['clientId'],
      location: location?.pathname,
    });
    tabAwareWorker.port.onmessage = (event) => {
      if (event?.data?.type === 'CLEAR_TOKEN_ON_CLOSE') {
        global['CLEAR_TOKEN_ON_CLOSE'] = event?.data?.payload;
      }

      if (event?.data?.type === 'ROUTE_REDIRECT') {
        if (event?.data?.url) {
          history.replace(event?.data?.url);
        }
      }

      if (event?.data?.type === 'NEW_WINDOW') {
        if (event?.data?.url) {
          // window.open(event?.data?.url, "", `popup=1,height=${window.screen.height},width=${window.screen.width}`)
          window.open(event?.data?.url);
        }
      }

      if (event?.data?.type === 'EXIST_WINDOW') {
        // 通知
        // TODO 通知的文案
        notification.info({
          ...event?.data?.payload,
          getContainer: () => document.getElementById('root-container'),
        });
      }
    };

    global['tabAwareWorker'] = tabAwareWorker;
  };

  useEffect(() => {
    Emitter.emit(EventConstant.DICT_DATA_CHANGE, dvaDictData);
  }, [dvaDictData]);

  useEffect(() => {
    Emitter.on(EventConstant.DICT_DATA_CHANGE, (dvaDictData) => {
      let dictData = merge({}, globalState?.dictData, dvaDictData);

      setQiankunGlobalState({
        ...globalState,
        // 若因为dict change 而set的globalState searchParams的 triggerSource 变成 other,保证不会污染通过btnClick判断要不要调接口的useEffect
        searchParams: { ...globalState?.searchParams, triggerSource: 'other' },
        dictData: dictData,
        // 放一份 access 和 userInfo 和 chsConfigurationInfo
        userInfo: initialState?.userInfo,
        access: access,
        chsConfigurationInfo: initialState?.configurationInfo,
      });
    });

    return () => {
      Emitter.off(EventConstant.DICT_DATA_CHANGE);
    };
  }, [globalState, access, initialState]);

  // DMRDEV-965 温附2定制 websocket 连接到小程序
  const initWebSocketForThirdPartyCheck = (callback?: any) => {
    if (global['wisdomThirdPartyCheckSocket'] !== null) {
      return;
    }

    const wsUrl =
      'ws://127.0.0.1:9449/' + global['wisdomThirdPartyCheckSocketId'];

    const wisdomThirdPartyCheckSocket = new WebSocket(wsUrl);
    wisdomThirdPartyCheckSocket.onopen = () => {
      console.log('WebSocket连接成功');
      global['wisdomThirdPartyCheckSocket'] = wisdomThirdPartyCheckSocket;

      callback && callback();
    };

    wisdomThirdPartyCheckSocket.onmessage = (event: any) => {
      console.log('WebSocket Data', event);
      const thirdPartyData = JSON.parse(event.data);
      if (thirdPartyData.status === 'success') {
        //成功
        console.log('websocket服务器成功收到数据');
      } else if (thirdPartyData.status === 'fail') {
        //失败
        console.error('websocket服务器接收的参数存在问题', thirdPartyData);
        notification.error({
          message: '第三方质控错误',
          description: thirdPartyData?.message,
          duration: 3,
          key: 'QUALITY_CONTROL_ERROR',
        });
        doLog(LogLevel.Trace, JSON.stringify(thirdPartyData));
      }
    };

    wisdomThirdPartyCheckSocket.onclose = (event: any) => {
      console.log('websocket服务器已关闭');
      setTimeout(() => {
        global['wisdomThirdPartyCheckSocket'] = null;
      }, 3000);
    };
    wisdomThirdPartyCheckSocket.onerror = (event: any) => {
      console.log('websocket服务发生错误');
      global['wisdomThirdPartyCheckSocket'] = null;

      notification.error({
        message: '第三方质控socket错误',
        description: '第三方质控客户端无法连接， 请检查客户端是否开启',
        duration: 3,
        key: 'SOCKET_ERROR',
      });
    };
  };

  return <>{props.children}</>;
}
