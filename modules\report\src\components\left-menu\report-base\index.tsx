import React, { useEffect, useState } from 'react';
import './index.less';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { ReportItem, ReportMasterItem } from '@/interfaces';
import {
  Empty,
  Form,
  message,
  Modal,
  Popconfirm,
  Radio,
  Spin,
  Tooltip,
} from 'antd';
import { Emitter } from '@uni/utils/src/emitter';
import { PeriodicPickerModes, ReportEventConstant } from '@/constants';
import {
  reportAddArgFormValueProcessor,
  reportTitleParamProcessor,
} from '@/processors';
import ReportItemAdd from '@/components/report-add';
import { DeleteOutlined, PlusCircleTwoTone } from '@ant-design/icons';
import dayjs from 'dayjs';
import Search from 'antd/es/input/Search';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';

interface LeftMenuReportBaseProps {
  masterItem: ReportMasterItem;
}

const LeftMenuReportBase = (props: LeftMenuReportBaseProps) => {
  const [form] = Form.useForm();

  const [reportBaseData, setReportBaseData] = React.useState<ReportItem[]>([]);

  const [selectedReportBaseItem, setSelectedReportBaseItem] =
    React.useState<ReportItem>();

  const [reportItemAdd, setReportItemAdd] = useState(false);

  const [searchKeyword, setSearchKeyword] = useState('');

  useEffect(() => {
    if (props?.masterItem) {
      reportBasesReq(props?.masterItem?.Id);
    }
  }, [props?.masterItem]);

  const { loading: reportBasesLoading, run: reportBasesReq } = useRequest(
    (id) => {
      return uniCommonService('Api/Report/Report/GetReportBases', {
        method: 'POST',
        data: {
          ReportSettingMasterId: id,
        },
        requestType: 'json',
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<ReportItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setReportBaseData(response?.data);
        } else {
          setReportBaseData([]);
        }
      },
    },
  );

  const onReportBaseCLick = (reportBaseItem: ReportItem) => {
    setSelectedReportBaseItem(reportBaseItem);
    Emitter.emit(ReportEventConstant.REPORT_ITEM_CLICK, {
      masterItem: props?.masterItem,
      reportItem: reportBaseItem,
    });
  };

  const { loading: reportBaseItemAddLoading, run: reportBaseItemAddReq } =
    useRequest(
      (title, reportArgs) => {
        return uniCommonService('Api/Report/Report/Create', {
          method: 'POST',
          data: {
            Title: title,
            ReportArgs: reportArgs,
            ReportSettingMasterId: props?.masterItem?.Id,
          },
          requestType: 'json',
        });
      },
      {
        manual: true,
        formatResult: async (response: RespVO<ReportItem>) => {
          // 创建之后新增到列表里面
          if (response?.code === 0 && response?.statusCode === 200) {
            setReportItemAdd(false);
            // Emitter.emit(StatsEventConstant.REPORT_ITEM_CREATED, response?.data);
            setReportBaseData([...reportBaseData, response?.data]);
          }
        },
      },
    );

  const { loading: reportBaseItemDeleteLoading, run: reportBaseItemDeleteReq } =
    useRequest(
      (reportBaseId) => {
        return uniCommonService('Api/Report/Report/Delete', {
          method: 'POST',
          data: {
            ReportSettingMasterId: props?.masterItem?.Id,
            ReportBaseId: reportBaseId,
          },
          requestType: 'json',
        });
      },
      {
        manual: true,
        formatResult: async (response: RespVO<ReportItem>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            message.success('删除成功');
            reportBasesReq(props?.masterItem?.Id);
          } else {
            message.error('删除失败');
          }
        },
      },
    );

  const onSearch = (event: any) => {
    setSearchKeyword(event?.target?.value);
  };

  return (
    <div className={'report-base-container'}>
      {/*<div className={'report-base-query-container'}>*/}
      {/*  <Radio.Group*/}
      {/*    className={`fast-date-select-container`}*/}
      {/*    value={periodicValue}*/}
      {/*    buttonStyle="solid"*/}
      {/*    onChange={(event) => {*/}
      {/*    }}*/}
      {/*  >*/}
      {/*    {PeriodicPickerModes?.map((item) => {*/}
      {/*      return (*/}
      {/*        <Radio.Button*/}
      {/*          style={{*/}
      {/*            width: `calc(100% / ${*/}
      {/*              props?.fastSelectDataSource?.length || 0*/}
      {/*            })`,*/}
      {/*            maxWidth: 100,*/}
      {/*          }}*/}
      {/*          value={item.value}*/}
      {/*        >*/}
      {/*          {item.name}*/}
      {/*        </Radio.Button>*/}
      {/*      );*/}
      {/*    })}*/}
      {/*  </Radio.Group>*/}
      {/*</div>*/}

      <Spin
        spinning={
          reportBasesLoading ||
          reportBaseItemAddLoading ||
          reportBaseItemDeleteLoading
        }
      >
        <div className={'report-base-items-container'}>
          {reportBaseData?.length === 0 ? (
            props?.masterItem?.ReportMode?.toLowerCase()?.indexOf(
              'readonly',
            ) === -1 ? null : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )
          ) : (
            <>
              <Search
                className={'search-container'}
                placeholder="输入报表标题以搜索"
                onChange={onSearch}
                allowClear={true}
                enterButton
              />

              {reportBaseData
                ?.sort((a, b) => {
                  return (
                    (b?.CreationTime ? dayjs(b?.CreationTime)?.unix() : 0) -
                    (a?.CreationTime ? dayjs(a?.CreationTime)?.unix() : 0)
                  );
                })
                ?.filter((item: ReportItem) => {
                  return (
                    item?.Title?.includes(searchKeyword) ||
                    pinyinInitialSearch(item?.Title, searchKeyword)
                  );
                })
                ?.map((item: ReportItem) => {
                  return (
                    <div
                      className={`report-base-item ${
                        item?.ReportBaseId ===
                        selectedReportBaseItem?.ReportBaseId
                          ? 'item-container-select'
                          : ''
                      }`}
                    >
                      <span
                        style={{ flex: 1 }}
                        onClick={() => {
                          onReportBaseCLick(item);
                        }}
                      >
                        {item?.Title}
                      </span>
                      {props?.masterItem?.ReportMode?.toLowerCase()?.indexOf(
                        'readonly',
                      ) === -1 && (
                        <div className={'flex-row'}>
                          <Tooltip title="删除">
                            <Popconfirm
                              title={`确定删除“${item.Title}报表”？`}
                              onConfirm={() => {
                                reportBaseItemDeleteReq(item?.ReportBaseId);
                              }}
                            >
                              <span key="delete" className="font-danger">
                                <DeleteOutlined />
                              </span>
                            </Popconfirm>
                          </Tooltip>
                        </div>
                      )}
                    </div>
                  );
                })}
            </>
          )}
          {props?.masterItem?.ReportMode?.toLowerCase()?.indexOf('readonly') ===
            -1 && (
            <div
              className={`report-base-item flex-row-center`}
              onClick={() => {
                setReportItemAdd(true);
              }}
            >
              <PlusCircleTwoTone />
              <span style={{ marginLeft: '10px' }}>新增</span>
            </div>
          )}
        </div>
      </Spin>

      <Modal
        title="新增报表"
        open={reportItemAdd}
        onOk={async () => {
          let validateResponse = await form.validateFields();
          if (validateResponse?.errorFields?.length !== 0) {
            let reportArgParams = reportAddArgFormValueProcessor(
              form.getFieldValue('args'),
              form.getFieldsValue(),
              props?.masterItem,
            );

            reportBaseItemAddReq(
              reportTitleParamProcessor(
                !!props?.masterItem?.CustomTitle,
                form?.getFieldValue('title'),
              ),
              reportArgParams,
            );
          }
        }}
        onCancel={() => {
          setReportItemAdd(false);
        }}
        okText={'新增'}
        cancelText={'取消'}
        destroyOnClose={true}
        getContainer={document.getElementById('report-container')}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <ReportItemAdd form={form} masterItem={props?.masterItem} />
      </Modal>
    </div>
  );
};

export default LeftMenuReportBase;
