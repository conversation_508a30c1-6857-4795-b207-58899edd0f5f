import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ProDescriptions } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  Tag,
  Card,
  Badge,
  Row,
  Col,
  Tooltip,
  Tabs,
  Modal,
  Divider,
  Alert,
  Space,
} from 'antd';
import { useModel } from '@@/plugin-model/useModel';
import { v4 as uuidv4 } from 'uuid';
import UniEcharts from '@uni/components/src/echarts';
import { UniTable } from '@uni/components/src';
import './index.less';
import { FeeTypesBarOption } from '../../chart.opts';

const FeeTypesContent = (props) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const [filterData, setFilterData] = useState<any>([]);
  console.log('globalState', globalState);
  useEffect(() => {
    if (props?.data?.length) {
      setFilterData(
        _.orderBy(
          props?.data?.filter((i) => i.ChargeTypeMode === props?.type),
          'TotalFee',
          'desc',
        ).map((fee) => {
          let label = globalState?.dictData?.[
            props?.type === 'Med' ? 'MedChargeType' : 'StatsChargeType'
          ]?.find((v) => v?.Code === fee?.FeeType)?.Name;
          return {
            ...fee,
            FeeTypeName: label,
          };
        }),
      );
    }
  }, [props?.data, props?.type]);

  return (
    <>
      <Card title="费用分类信息" loading={props?.loading} size="small">
        <Row gutter={[0, 0]}>
          <Col span={16}>
            <UniEcharts
              elementId={'radar'}
              height={250}
              loading={false}
              options={
                (filterData?.length &&
                  FeeTypesBarOption(filterData, 'FeeTypeName')) ||
                {}
              }
            />
          </Col>
          <Col span={8}>
            <UniTable
              id="fee_type_table"
              rowKey="Code"
              columns={props?.columns}
              dataSource={filterData}
              pagination={false}
              size="small"
              scroll={{ y: 220 }}
            />
          </Col>
        </Row>
      </Card>
    </>
  );
};

export default FeeTypesContent;
