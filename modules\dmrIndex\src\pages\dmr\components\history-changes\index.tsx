import './index.less';
import React, { useEffect, useRef, useState } from 'react';
import {
  DndContext,
  PointerSensor,
  useDraggable,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { Coordinates, CSS } from '@dnd-kit/utilities';
import { CloseOutlined, HolderOutlined } from '@ant-design/icons';
import { ExportIconBtn, UniTable } from '@uni/components/src';
import { icdePortalColumns } from '@/pages/dmr/components/icde-oper-portal/columns';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import {
  historyChangesPortalColumns,
  historyChangesPortalBaseColumns,
} from '@/pages/dmr/components/history-changes/columns';
import { isEmptyValues } from '@uni/utils/src/utils';
import ReactDOM from 'react-dom';
import ChargePortal from '@/pages/dmr/components/charge-portal';
import DmrChangeHistoryDetailPortal from '@/pages/dmr/components/history-changes/change-detail';
import { v4 as uuidv4 } from 'uuid';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { codeSdateEdateProcessorViaSearchedValue } from '@/utils/utils';

export const defaultCoordinates = {
  x: 240,
  y: 0,
};

export interface DmrChangeHistoryPortalProps {
  containerRef: any;
  hisId: string;
  dictData: any;
}

const DmrChangeHistoryPortal = (props: DmrChangeHistoryPortalProps) => {
  const [coordinates, setCoordinates] =
    useState<Coordinates>(defaultCoordinates);

  const [changeHistoryPortalColumns, setChangeHistoryPortalColumns] = useState(
    [],
  );

  const detailInfoContainerRef = useRef(null);

  const [selectedRowId, setSelectedRowId] = useState<any>(null);

  const { attributes, isDragging, listeners, setNodeRef, transform } =
    useDraggable({
      id: 'change-history-container',
    });
  const style = {
    transform: CSS.Translate.toString(transform),
  };

  const [changeHistoryPortalShow, setChangeHistoryPortalShow] =
    useState<boolean>(false);

  const [dmrChangeHistoryData, setDmrChangeHistoryData] = useState<any[]>([]);

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      showStatus: (data: any) => {
        setSelectedRowId(null);
        setChangeHistoryPortalShow(data?.status);
        if (data?.status === true && !isEmptyValues(props?.hisId)) {
          dmrChangeHistoryColumnsReq();
          dmrChangeHistoryReq();
        }

        if (data?.status === false) {
          detailInfoContainerRef?.current?.showStatus({
            status: false,
          });
        }
      },
      updateCoordinates: (data: any) => {
        if (data?.id === 'change-history-container') {
          let currentCoordinates: any = {};
          currentCoordinates['x'] =
            (coordinates['x'] ?? defaultCoordinates['x']) + data?.delta?.['x'];
          currentCoordinates['y'] =
            (coordinates['y'] ?? defaultCoordinates['y']) + data?.delta?.['y'];
          setCoordinates(currentCoordinates);
        }

        if (data?.id === 'change-history-detail-container') {
          detailInfoContainerRef?.current?.updateCoordinates(data?.delta);
        }
      },
    };
  });

  const {
    loading: dmrChangeHistoryColumnsLoading,
    run: dmrChangeHistoryColumnsReq,
  } = useRequest(
    () => {
      return uniCommonService(
        `Api/Dmr/DmrCardHistory/GetDmrHistoriesWithChanges`,
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        if (response.code === 0) {
          setChangeHistoryPortalColumns(
            tableColumnBaseProcessor(
              historyChangesPortalColumns,
              response?.data?.Columns,
            ),
          );
        } else {
          setChangeHistoryPortalColumns([]);
        }
      },
    },
  );

  const { loading: dmrChangeHistoryLoading, run: dmrChangeHistoryReq } =
    useRequest(
      () => {
        return uniCommonService(
          `Api/Dmr/DmrCardHistory/GetDmrHistoriesWithChanges`,
          {
            method: 'POST',
            data: {
              hisId: props?.hisId,
              actions: ['Update'],
            },
          },
        );
      },
      {
        manual: true,
        formatResult: async (response: RespVO<any>) => {
          if (response.code === 0 && response?.statusCode === 200) {
            setDmrChangeHistoryData(
              (response?.data ?? [])?.map((item) => {
                item['rowId'] = uuidv4();
                return item;
              }),
            );

            requestAnimationFrame(() => {
              document
                .querySelector('#change-history-portal-table .ant-table-body')
                .scrollTo({
                  top: 0,
                  behavior: 'smooth',
                });
            });
          } else {
            setDmrChangeHistoryData([]);
          }
        },
      },
    );

  return (
    <div
      className={
        'change-history-portal-container change-history-draggable-container'
      }
      ref={setNodeRef}
      style={
        {
          display: `${changeHistoryPortalShow ? 'flex' : 'none'}`,
          ...style,
          top: `${coordinates?.y}px`,
          left: `${coordinates?.x}px`,
          '--translate-x': `${transform?.x ?? 0}px`,
          '--translate-y': `${transform?.y ?? 0}px`,
        } as React.CSSProperties
      }
    >
      <div className={'change-history-data-container'}>
        <div className={`change-history-header-container`} {...listeners}>
          <div className={`title-container ${isDragging ? 'grabbing' : ''}`}>
            <HolderOutlined className={'handle'} size={30} />
            <span className={'title'}>病案修改痕迹</span>
            <TableColumnEditButton
              columnInterfaceUrl={
                'Api/Dmr/DmrCardHistory/GetDmrHistoriesWithChanges'
              }
              onTableRowSaveSuccess={(columns) => {
                setChangeHistoryPortalColumns(
                  tableColumnBaseProcessor(
                    historyChangesPortalColumns,
                    columns,
                  ),
                );
              }}
            />
            <ExportIconBtn
              isBackend={true}
              backendObj={{
                url: 'Api/Dmr/DmrCardHistory/ExportGetDmrHistoriesWithChanges',
                method: 'POST',
                data: {
                  hisId: props?.hisId,
                  actions: ['Update'],
                },
                fileName: '历史痕迹',
              }}
              btnDisabled={dmrChangeHistoryData?.length < 1}
            />
          </div>
          <CloseOutlined
            onClick={() => {
              setSelectedRowId(null);
              setChangeHistoryPortalShow(false);
              detailInfoContainerRef?.current?.showStatus({
                status: false,
              });
            }}
          />
        </div>

        <UniTable
          id={'change-history-portal-table'}
          rowKey={'rowId'}
          scroll={{ x: 'max-content', y: 300 }}
          loading={dmrChangeHistoryLoading}
          columns={[
            ...historyChangesPortalBaseColumns,
            ...changeHistoryPortalColumns,
          ]}
          dataSource={dmrChangeHistoryData}
          rowClassName={(record, index) => {
            return record?.rowId === selectedRowId
              ? 'history-change-row-selected'
              : '';
          }}
          pagination={false}
          clickable={true}
          onRow={(record) => {
            return {
              onClick: (event) => {
                setSelectedRowId(record?.rowId);
                detailInfoContainerRef?.current?.showStatus({
                  status: true,
                  data: record?.ChangeModels,
                });
              },
            };
          }}
        />
      </div>

      {ReactDOM.createPortal(
        <DmrChangeHistoryDetailPortal
          containerRef={detailInfoContainerRef}
          dictData={props?.dictData}
        />,
        document.getElementById('root-container'),
      )}
    </div>
  );
};

export default DmrChangeHistoryPortal;
