import {
  Button,
  InputNumber,
  Skeleton,
  Space,
  Switch,
  TablePaginationConfig,
} from 'antd';
import React, {
  CSSProperties,
  ReactNode,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { ParamsType, ProProvider } from '@ant-design/pro-provider';
import { ColumnType, FilterValue } from 'antd/lib/table/interface';
import './index.less';
import {
  ActionType,
  EditableProTable,
  ProTable,
  ProTableProps,
} from '@ant-design/pro-table';
import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
} from '@uni/utils/src/utils';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import {
  processSearchFilter,
  SearchItemProcessor,
} from '../processor/column/search-item';
import SorterItemProcessor, {
  processDefaultSortedInfo,
} from '../processor/column/sorter-item';
import { v4 as uuidv4 } from 'uuid';
import {
  CheckOutlined,
  CloseOutlined,
  FileExcelOutlined,
} from '@ant-design/icons';
import isEmpty from 'lodash/isEmpty';
import Toolbar from '@ant-design/pro-table/es/components/ToolBar';
import { processSummaryData } from '../processor/data/summary-data';
import { exportExcel } from '@uni/utils/src/excel-export';
import cloneDeep from 'lodash/cloneDeep';
import {
  EditableFormInstance,
  EditableProTableProps,
} from '@ant-design/pro-table/es/components/EditableTable';
import {
  ValueTypeProcessor,
  valueTypeMap,
} from '../processor/column/value-type';
import { DictionaryProcessor } from '../processor/column/dictionary-processor';
import { FilterItemProcessor } from '../processor/column/filter-item';
import { exportExcelDictionaryModuleProcessor } from '../processor/data/export';
import isNil from 'lodash/isNil';
import { useHotkeys } from 'react-hotkeys-hook';
import { editableTableHotKeys } from '../constants';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import EditFormItemIdProcessor from '../processor/column/form-item-id';
// import { VList } from "../../virtualize";
import { useVirtualizer } from '@tanstack/react-virtual';
import isEqual from 'lodash/isEqual';
import ColumnItemMinWidthProcessor from '../processor/column/column-min-width';
import { externalPaginationProcessor } from '../pagination';
import { ColumnWidthDetectProcessor } from '../processor/column/columns-width-detector';

const defaultOptions = {
  density: false,
  reload: false,
  setting: false,
  fullScreen: false,
};

const defaultPagination: TablePaginationConfig = {
  size: 'default',
  defaultPageSize: 10,
  pageSizeOptions: ['10', '20'],
  hideOnSinglePage: true,
} as any;

interface ColumnItem<RecordType> extends ColumnType<RecordType> {
  data?: string;
  draggable?: boolean;
}

export interface UniEditableTableProps<T, U = ParamsType>
  extends EditableProTableProps<T, U> {
  id: string;
  tableHeader?: React.ReactNode;
  tableHeaderTitle?: string;
  columns?: any[];
  dataItemType?: string;
  columnDefinitions?: object;
  dataSource?: any[];
  rowKey: string;
  scroll?: object;
  height?: number;
  // 请求方法
  columnsFetch?: () => Promise<any[]>;
  dataFetch?: () => Promise<any[]>;
  // dataProcessor
  dataItemProcessor?: (dataItem: any, index: number) => any;
  // columnProcessor
  columnItemProcessor?: (columnItem: any, index: number) => any;
  // 样式
  wrapperClassName?: string;
  className?: string;
  style?: any;
  // drag
  onTableDataSourceOrderChange?: (tableData: any[]) => void;
  // toolbar
  extraToolBar?: ReactNode[];
  onCustomToolbarExportClick?: () => void;
  needExport?: boolean;
  // 是否需要总计行
  needSummary?: boolean;
  summaryDataIndex?: string;
  summaryExcludeKeys?: string[];
  summaryData?: any;
  // 导出
  exportName?: string;
  exportExcludeKeys?: string[];
  exportTableDataSourceProcessor?: (dataSource: any[]) => any[];
  // 是否能点击
  clickable?: boolean;
  // 用于高度计算
  parentId?: string;
  dictionaryData?: any;

  // 目前此字段 为非必填，若需要用于其他项目中请改为必填
  backendPagination?: boolean;
  // 是否强制columns更新...对于那些title文字，visible,renderFormItem等blahblah更新的，绕过tableColumns检查
  forceColumnsUpdate?: boolean;
  // 开启快捷键
  enableShortcuts?: boolean;
  enableHotKeys?: boolean;
  // 自定义快捷键
  customShortcuts?: any;

  // 是否计算列宽度
  widthCalculate?: boolean;
  // 是否在字典翻译完后计算宽度
  widthDetectAfterDictionary?: boolean;

  // 保持 columns 不更新
  columnsPersist?: boolean;
}

const UniEditableTable = ({
  id,
  tableHeader,
  tableHeaderTitle,
  columns,
  dataSource,
  value, // editabletable 应该看这个
  dataItemType,
  columnDefinitions,
  columnsFetch,
  columnItemProcessor,
  dataFetch,
  dataItemProcessor,
  onChange,
  onTableChange,
  wrapperClassName,
  className,
  style,
  scroll,
  height,
  needSummary,
  summaryDataIndex,
  summaryExcludeKeys,
  summaryData,
  exportName,
  exportExcludeKeys,
  pagination,
  clickable,
  parentId,
  dictionaryData,
  backendPagination,
  forceColumnsUpdate,
  enableShortcuts = false,
  customShortcuts,
  widthCalculate = false,
  widthDetectAfterDictionary = false,
  columnsPersist = false,
  enableHotKeys = false,
  ...restProps
}: UniEditableTableProps<any>) => {
  const itemRef = React.useRef<any>();

  const providerValues = useContext(ProProvider);
  const [tableColumns, setTableColumns] = useState([]);
  const [tableRenderColumns, setTableRenderColumns] = useState([]);
  const [tableDataSource, setTableDataSource] = useState([]);
  // Loading
  const [tableLoading, setTableLoading] = useState(false);

  // filters & sorters
  const [searchFilter, setSearchFilter] = useState<
    Array<{ searchText: string; searchedColumn: string }>
  >([]);

  // controlled FilterValue && sortedInfo
  const [filteredInfo, setFilteredInfo] = useState<
    Record<string, FilterValue | null>
  >({});
  const [sortedInfo, setSortedInfo] = useState(undefined);

  useEffect(() => {
    Emitter.on(EventConstant.TABLE_COLUMN_SORTER_EDIT, (sorterInfo) => {
      setSortedInfo(sorterInfo);
    });

    return () => {
      Emitter.off(EventConstant.TABLE_COLUMN_SORTER_EDIT);
    };
  }, []);

  // 快捷键
  const hotKeyRefs = editableTableHotKeys?.map((item) => {
    return useHotkeys(
      item.key,
      (e: any) => {
        onShortcutKeyPress(item?.type);
      },
      {
        preventDefault: true,
        enabled: enableShortcuts || enableHotKeys,
        enableOnFormTags: true,
      },
    );
  });

  // 初始化Event Listener
  // 部分依赖state的请监听state
  useEffect(() => {
    Emitter.on(
      EventConstant.TABLE_COLUMN_SEARCH_CONFIRM,
      onTableColumnSearchConfirm,
    );
    Emitter.on(
      EventConstant.TABLE_COLUMN_SEARCH_RESET,
      onTableColumnSearchReset,
    );

    return () => {
      Emitter.off(EventConstant.TABLE_COLUMN_SEARCH_CONFIRM);
      Emitter.off(EventConstant.TABLE_COLUMN_SEARCH_RESET);
    };
  }, [searchFilter]);

  useEffect(() => {
    if (columnsPersist && tableColumns?.length > 0) {
      // 跳过
      return;
    }
    let processors = [
      // ColumnDefinitionProcessor,
      commonColumnProcessor,
      ColumnItemMinWidthProcessor,
      ValueTypeProcessor,
      SorterItemProcessor,
      FilterItemProcessor,
      SearchItemProcessor,
      EditFormItemIdProcessor,
    ];
    processTableColumns(true, processors);
  }, [columns, columnsFetch]);

  useEffect(() => {
    processTableDataSource();
  }, [dataSource, value, dataFetch]);

  // 快捷键 实现
  const onShortcutKeyPress = (type) => {
    console.log('onShortcutKeyPress', type);
    switch (type) {
      case 'SAVE':
        Emitter.emit(EventConstant.EDITABLE_SAVE_SHORTCUT);
        break;
      case 'CANCEL':
        Emitter.emit(EventConstant.EDITABLE_CANCEL_SHORTCUT);
        break;
      case 'DELETE':
        onDeletePress();
        break;
      case 'LEFT':
        Emitter.emit(EventConstant.EDITABLE_LEFT_KEYDOWN_SHORTCUT);
        onArrowKeyPress(type);
        break;
      case 'RIGHT':
        Emitter.emit(EventConstant.EDITABLE_RIGHT_KEYDOWN_SHORTCUT);
        onArrowKeyPress(type);
        break;
      case 'UP':
        Emitter.emit(EventConstant.EDITABLE_UP_KEYDOWN_SHORTCUT);
        onArrowKeyPress(type);
        break;
      case 'DOWN':
        Emitter.emit(EventConstant.EDITABLE_DOWN_KEYDOWN_SHORTCUT);
        onArrowKeyPress(type);
        break;
      case 'SCROLL_LEFT':
      case 'SCROLL_RIGHT':
      case 'SCROLL_UP':
      case 'SCROLL_DOWN':
        onTableScroll(type);
        break;
      default:
        break;
    }
  };

  const onTableScroll = (type) => {
    let baseLeft = 100;
    let baseTop = 50;

    let scrollParam = undefined;

    let tableBody = document.querySelector(
      `div[id='${id}'] div[class='ant-table-body']`,
    );
    switch (type) {
      case 'SCROLL_LEFT':
        scrollParam = {
          left: -baseLeft,
        };
        break;
      case 'SCROLL_RIGHT':
        scrollParam = {
          left: baseLeft,
        };
        break;
      case 'SCROLL_UP':
        scrollParam = {
          top: -baseTop,
        };
        break;
      case 'SCROLL_DOWN':
        scrollParam = {
          top: baseTop,
        };
        break;
    }

    if (scrollParam) {
      tableBody?.scrollBy({
        ...scrollParam,
        behavior: 'smooth',
      });
    }
  };

  const onArrowKeyPress = (type) => {
    let currentInputItem = document?.activeElement;
    let currentTableInputItems = [].slice.call(
      document?.querySelectorAll("div[class*='editable-form-item'] input"),
    );

    let currentRow = document?.activeElement.closest('tr');
    let currentRowInputItems = Array.from(
      currentRow.querySelectorAll("div[class*='editable-form-item'] input"),
    );
    let currentInputIndexInRow = currentRowInputItems.indexOf(
      document.activeElement,
    );

    let currentRowIndex = parseInt(currentRow.getAttribute('data-index'));

    let currentFocusIndexInRow = currentRowInputItems?.indexOf(
      document.activeElement,
    );

    if (currentInputItem) {
      switch (type) {
        case 'LEFT':
        case 'RIGHT':
          if (currentFocusIndexInRow > -1) {
            let nextFocusIndex =
              currentFocusIndexInRow + (type === 'LEFT' ? -1 : 1);
            // 如果不在这个区间内  需要计算到下一行了
            if (
              nextFocusIndex > -1 &&
              currentFocusIndexInRow < currentRowInputItems?.length
            ) {
              (currentRowInputItems?.at(nextFocusIndex) as any)?.focus();
            }
          }
          break;
        case 'UP':
        case 'DOWN':
          if (currentFocusIndexInRow > -1 && currentRowIndex > -1) {
            let nextRowIndex = currentRowIndex + (type === 'UP' ? -1 : 1);
            let nextRowInputItems = [].slice.call(
              document.querySelectorAll(
                `tr[data-index='${nextRowIndex}'] div[class*='editable-form-item'] input`,
              ),
            );
            // (nextRowInputItems?.at(currentInputIndexInRow) as any)?.focus();

            // 获取当前输入框的位置信息
            let currentInputRect = currentInputItem.getBoundingClientRect();
            let currentInputCenterX =
              currentInputRect.left + currentInputRect.width / 2;

            // 找到下一行中在水平位置上最接近当前输入框的输入框
            let closestInput = null;
            let minDistance = Infinity;

            for (let input of nextRowInputItems) {
              let rect = input.getBoundingClientRect();
              let centerX = rect.left + rect.width / 2;
              let distance = Math.abs(centerX - currentInputCenterX);

              if (distance < minDistance) {
                minDistance = distance;
                closestInput = input;
              }
            }

            // 聚焦到找到的最近输入框
            if (closestInput) {
              closestInput.focus();
            }
          }
          break;
        default:
          break;
      }
    }
  };

  const onDeletePress = () => {
    let currentRowItem = document?.activeElement?.closest(
      "div[class='editable-form-item']",
    );
    if (currentRowItem) {
      let currentRowKey = currentRowItem?.id?.split('#')?.at(0);
      let currentFormKey = currentRowItem?.id?.split('#')?.at(1);

      let currentTableItem = tableDataSource?.find(
        (item) => item?.rowId === currentRowKey,
      );
      if (currentTableItem) {
        currentTableItem[currentFormKey] = '';
      }

      let currentRowData =
        restProps?.editable?.form?.getFieldValue(currentRowKey);
      currentRowData[currentFormKey] = '';

      let formValue = restProps?.editable?.form.getFieldsValue();
      formValue[currentRowKey] = currentRowData;

      restProps?.editable?.form?.setFieldsValue(formValue);
    }
  };

  const onTableColumnSearchConfirm = (data) => {
    setSearchFilter(
      searchFilter.map((item) => {
        return item.searchedColumn === data.dataIndex
          ? {
              searchText: data.selectedKeys[0],
              searchedColumn: item.searchedColumn,
            }
          : item;
      }),
    );
  };

  const onTableColumnSearchReset = (data) => {
    setSearchFilter(
      searchFilter.map((item) => {
        return item.searchedColumn === data.dataIndex
          ? {
              searchText: '',
              searchedColumn: item.searchedColumn,
            }
          : item;
      }),
    );
  };

  // default column processor
  const commonColumnProcessor = ({ columnItem }) => {
    columnItem.hideInTable = !columnItem?.visible;
    if (
      !columnItem.render &&
      (!columnItem.valueType || columnItem?.valueType === 'text')
    ) {
      columnItem['normalRenderer'] = true;
      columnItem.render = (node, record, index, action) => {
        // module & moduleGroup
        let dataItem = record[columnItem.dataIndex];
        if (columnItem?.isExtraProperty === true) {
          dataItem = record?.['ExtraProperties']?.[columnItem.dataIndex];
        }
        if (columnItem?.dictionaryModule) {
          dataItem = dataItemWithColumnModuleProcessor(
            columnItem?.dictionaryModule,
            dataItem,
          );
        }

        return (
          <span>
            {valueNullOrUndefinedReturnDash(
              dataItem,
              columnItem['dataType'],
              columnItem['scale'],
            )}
          </span>
        );
      };
    }

    // renderFormItem 默认值
    if (
      !columnItem?.renderFormItem &&
      columnItem?.isReadOnly &&
      (!columnItem.valueType || columnItem?.valueType === 'text')
    ) {
      columnItem.renderFormItem = ({ index, entity }) => {
        let dataItem = entity[columnItem.dataIndex];
        if (columnItem?.dictionaryModule) {
          dataItem = dataItemWithColumnModuleProcessor(
            columnItem?.dictionaryModule,
            dataItem,
          );
        }

        return (
          <span>
            {valueNullOrUndefinedReturnDash(
              dataItem,
              columnItem['dataType'],
              columnItem['scale'],
            )}
          </span>
        );
      };
    }

    return {
      ...columnItem,
    };
  };

  const commonDataSourceProcessor = (dataSourceItem) => {
    if (restProps?.rowKey === 'id' && !isEmptyValues(dataSourceItem?.id)) {
      return {
        id: uuidv4(),
        ...dataSourceItem,
      };
    }

    return dataSourceItem;
  };

  const dataItemWithColumnModuleProcessor = (module, dataItem) => {
    if (dictionaryData?.[module]) {
      let dictionaryItem = dictionaryData?.[module]?.find(
        (item) => item.Code === dataItem,
      );
      if (dictionaryItem) {
        return dictionaryItem?.Name || dataItem;
      }
    }

    return dataItem;
  };

  const processTableColumns = async (
    fetchColumn: boolean = false,
    processors: Function[],
  ) => {
    let processColumns = columns;
    // ? columns.slice()?.sort((a, b) => (a?.order ?? 0) - (b?.order ?? 0))
    // : [];
    if (fetchColumn && columnsFetch) {
      processColumns = await columnsFetch();
    }

    // 当处理过columns 保持处理同一份columns
    if (
      !forceColumnsUpdate &&
      tableColumns &&
      tableColumns?.length > 0 &&
      tableColumns?.length === columns?.length &&
      // 严格意义上相等 包括顺序也要相等
      isEqual(tableColumns, columns)
    ) {
      processColumns = tableColumns?.slice();
    }

    setSearchFilter(processSearchFilter(processColumns));

    let defaultSortedInfo = processDefaultSortedInfo(processColumns);

    if (isEmptyValues(sortedInfo) && !isEmptyValues(defaultSortedInfo)) {
      if (isEmptyValues(global['tableParameters'])) {
        global['tableParameters'] = {};
      }
      global['tableParameters']['sorter'] = defaultSortedInfo;
    }

    console.log(
      'defaultSortedInfo',
      defaultSortedInfo,
      global['tableParameters'],
    );

    // TODO 处理columns
    // tableColumns = tableColumns.slice().map((item, index) => {
    for (let index = 0; index < processColumns.length; index++) {
      let item = processColumns?.at(index);

      // 存在children的时候是不用过process的
      if (item.children && item.children?.length > 0) {
        item.children = childItemProcessor(item, processors, defaultSortedInfo);
        continue;
      }

      item = itemProcess(item, processors, defaultSortedInfo);
      if (columnItemProcessor) {
        item = Object.assign({}, columnItemProcessor(item, index));
      }
      processColumns[index] = item;
    }

    // dictionary columns
    // processColumns = DictionaryProcessor(
    //   processColumns,
    //   dictionaryData,
    // )?.slice();
    setTableColumns(processColumns);
  };

  const itemProcess = (
    item,
    processors: any[],
    defaultSortedInfo: any = {},
  ) => {
    for (let processor of processors) {
      item = Object.assign(
        {},
        processor({
          // definitionProcessor
          columnItem: item,
          ...item,
          // backendPagination: false,
          backendPagination:
            backendPagination ?? !isNil((pagination as any)?.total),
          // search props
          filteredInfo: filteredInfo,
          searchFilter: searchFilter,
          // sorter props
          sortedInfo: isEmptyValues(sortedInfo)
            ? defaultSortedInfo
            : sortedInfo,
          // datasource 用于filters
          dataSource: tableDataSource,
          // edit form item id
          enableShortcuts: enableShortcuts,
          // 是否计算宽度
          widthCalculate: widthCalculate,
        }),
      );
    }

    return item;
  };

  const childItemProcessor = (
    item: any,
    processors: any[],
    defaultSortedInfo: any = {},
  ) => {
    return item.children
      ?.map((childItem) => {
        if (childItem?.children && childItem.children?.length > 0) {
          childItem['children'] = childItemProcessor(
            childItem,
            processors,
            defaultSortedInfo,
          );
        } else {
          childItem = itemProcess(childItem, processors, defaultSortedInfo);
        }

        return childItem;
      })
      .slice();
  };

  const processTableDataSource = async () => {
    let tableDataSource = dataSource || value?.slice() || [];
    if (dataFetch) {
      tableDataSource = await dataFetch();
    }

    // 处理 dataSource
    tableDataSource = tableDataSource.slice().map((item, index) => {
      if (dataItemProcessor) {
        item = Object.assign({}, dataItemProcessor(item, index));
      }
      return commonDataSourceProcessor(item);
    });

    if (needSummary && summaryDataIndex) {
      tableDataSource = processSummaryData(
        tableDataSource,
        summaryDataIndex,
        summaryData,
        summaryExcludeKeys,
      ).slice();
    }

    setTableDataSource(tableDataSource);
  };

  useEffect(() => {
    let columns = tableColumns?.slice();

    if (columns && columns?.length > 0) {
      if (!isEmpty(sortedInfo) && columns.length > 0) {
        columns = columns
          .map((item) => {
            return SorterItemProcessor({
              columnItem: item,
              sortedInfo: sortedInfo,
              // backendPagination: false,
              backendPagination:
                backendPagination ?? !isNil((pagination as any)?.total),
            });
          })
          ?.slice();
      }

      if (!isEmpty(filteredInfo) && columns.length > 0) {
        let filterProcessors = [FilterItemProcessor, SearchItemProcessor];

        columns = columns.map((item) => {
          let processedItem = Object.assign({}, item);

          if (processedItem?.children && processedItem?.children?.length > 0) {
            processedItem.children = childItemProcessor(
              processedItem,
              filterProcessors,
            );
          } else {
            processedItem = itemProcess(processedItem, filterProcessors);
          }

          return processedItem;
        });
      }
      console.log('filteredInfo, sortedInfo', columns);

      setTableColumns(columns?.slice());
    }
  }, [filteredInfo, sortedInfo]);

  // 处理 翻译 && filterType = filter
  // filterType 用于 生成filters ，且 如果遇到有key为sort的则直接略过
  // 这边的处理是最后一步
  useEffect(() => {
    setTimeout(() => {
      let columns = tableColumns.slice();
      // 这边先翻译 翻译是必做的
      if (
        dictionaryData &&
        tableColumns?.length > 0 &&
        tableDataSource?.length > 0
      ) {
        let dictionaryProceedColumns = DictionaryProcessor(
          tableColumns,
          dictionaryData,
          widthDetectAfterDictionary,
        )?.slice();
        // 这边判断要不要做widthDetect
        if (widthDetectAfterDictionary) {
          dictionaryProceedColumns = ColumnWidthDetectProcessor(
            dictionaryProceedColumns,
            tableDataSource,
            dictionaryData,
            false,
          )?.slice();
        }
        columns = dictionaryProceedColumns?.slice();
      }

      // 然后再filterType = filter
      if (
        columns &&
        columns?.length > 0 &&
        columns?.findIndex((d) => d.key === 'sort') === -1 &&
        tableDataSource?.length > 0
      ) {
        let filterSortColumns = columns.map((item) => {
          let columnItem = Object.assign(
            {},
            FilterItemProcessor({
              columnItem: item,
              ...item,
              // backendPagination: false,
              backendPagination:
                backendPagination ?? !isNil((pagination as any)?.total),
              // search props
              filteredInfo: filteredInfo,
              dataSource: tableDataSource,
              dictData: dictionaryData,
            }),
          );

          if (columnItem.children && columnItem.children?.length > 0) {
            columnItem.children = columnItem.children
              ?.map((childItem) => {
                return FilterItemProcessor({
                  columnItem: childItem,
                  ...childItem,
                  // backendPagination: false,
                  backendPagination:
                    backendPagination ?? !isNil((pagination as any)?.total),
                  // search props
                  filteredInfo: filteredInfo,
                  dataSource: tableDataSource,
                  dictData: dictionaryData,
                });
              })
              .slice();
          }

          return columnItem;
        });

        if (!isEqual(columns, filterSortColumns)) {
          columns = filterSortColumns?.slice();
        }
      }

      setTableRenderColumns(columns);
    }, 200);
  }, [
    dictionaryData,
    tableDataSource,
    tableColumns,
    widthDetectAfterDictionary,
  ]);

  const onToolbarExportClick = () => {
    const canExportColumns = tableColumns?.filter(
      (columnItem) =>
        columnItem.className?.indexOf('exportable') !== -1 &&
        columnItem.valueType !== 'option',
    );
    if (!isEmpty(canExportColumns)) {
      exportExcel(
        canExportColumns.slice() as any[],
        exportExcelDictionaryModuleProcessor(
          canExportColumns,
          restProps.exportTableDataSourceProcessor
            ? restProps.exportTableDataSourceProcessor(
                cloneDeep(tableDataSource.slice()),
              )
            : cloneDeep(tableDataSource.slice()),
          dictionaryData,
        ),
        exportName,
        exportExcludeKeys,
      );
    }
  };

  const defaultToolbar = () => (
    <>
      {restProps?.needExport || true ? (
        <Button
          key="export"
          icon={<FileExcelOutlined />}
          onClick={() => {
            restProps?.onCustomToolbarExportClick
              ? restProps.onCustomToolbarExportClick()
              : onToolbarExportClick();
          }}
        >
          导出Excel
        </Button>
      ) : (
        <></>
      )}
    </>
  );

  const renderToolbar: React.ReactNode[] = [
    defaultToolbar(),
    ...(restProps?.extraToolBar || []),
  ];

  const parentHeight = parentId
    ? document.getElementById(parentId)?.offsetHeight
    : undefined;

  console.log('dictionaryProceedColumns', tableRenderColumns);

  return (
    <div ref={mergeRefs([itemRef, ...hotKeyRefs])} className={wrapperClassName}>
      <Skeleton active loading={tableLoading}>
        {/*table toolbar*/}
        {(restProps?.needExport || restProps.extraToolBar?.length > 0) && (
          <div className={'toolbar-container'}>
            <div className={'tools-left'} />
            <Space
              className={`tools-right`}
              direction={'horizontal'}
              size={16}
              align={'center'}
            >
              {renderToolbar.map((item) => {
                return item;
              })}
            </Space>
          </div>
        )}

        {/*table header*/}
        {(tableHeader || tableHeaderTitle) && (
          <div className={'header-container'}>
            {tableHeader ? (
              tableHeader
            ) : (
              <span className={'label'}>{tableHeaderTitle}</span>
            )}
          </div>
        )}
        <ProProvider.Provider
          value={{
            ...providerValues,
            valueTypeMap,
          }}
        >
          <EditableProTable
            id={id}
            loading={tableLoading}
            className={`uni-table ${className}`}
            style={{
              ...(style || {}),
              ...(parentHeight
                ? { height: parentHeight, minHeight: parentHeight }
                : {}),
            }}
            columns={tableRenderColumns}
            value={tableDataSource}
            onChange={(values: any[]) => {
              onChange && onChange(values);
            }}
            onTableChange={(pagination, filters, sorter, extra) => {
              setFilteredInfo(filters);
              setSortedInfo(sorter);
              // 把sorter和filter挂在global上 用于下一次带有DtParam的请求 如果下一次不包含DtParam即丢弃
              if (backendPagination ?? !isNil((pagination as any)?.total)) {
                global['tableParameters'] = {
                  filters: filters,
                  sorter: sorter,
                };
              }
              onTableChange &&
                onTableChange(pagination, filters, sorter, extra);
            }}
            scroll={scroll}
            pagination={
              pagination !== false
                ? {
                    ...defaultPagination,
                    ...(pagination || {}),
                    ...externalPaginationProcessor(),
                  }
                : false
            }
            toolBarRender={false}
            {...restProps}
            search={false}
            options={{ ...defaultOptions, ...(restProps?.options || {}) }}
          />
        </ProProvider.Provider>
      </Skeleton>
    </div>
  );
};

export default UniEditableTable;

export { EditableFormInstance };
