const { FixedThreadPool, PoolEvents, DynamicThreadPool } = require('poolifier');
const os = require('node:os');
const fs = require('fs');
const path = require('path');
const argv = require('minimist')(process.argv.slice(2));

const interval = 10 * 1000;

const pool = new FixedThreadPool(3, './scripts/packWorker.js', {
  errorHandler: (e) => console.error('error', e),
  onlineHandler: () => console.log('worker is online'),
});

pool.emitter?.on(PoolEvents.full, () => console.info('Pool is full'));
pool.emitter?.on(PoolEvents.ready, () => console.info('Pool is ready'));
pool.emitter?.on(PoolEvents.busy, () => console.info('Pool is busy'));

function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

const buildList = [];

const buildProject = async () => {
  // 从umirc中捞取 所有的 注册了的子应用
  let registerModules =
    require(path.resolve(__dirname, '../apps.js'))?.apps?.filter(
      (item) => item?.bundle === true,
    ) ?? [];
  console.log(
    '已注册的模块：',
    registerModules?.map((item) => item.name)?.toString(),
  );

  const argModules = argv?.modules?.split(' ');
  let modules = [];
  let masterPath = path.resolve(__dirname, '..');
  if (argModules?.length > 0) {
    argModules?.forEach((module) => {
      let modulePath = undefined;
      if (module !== 'master') {
        if (registerModules?.map((item) => item.name)?.includes(module)) {
          modulePath = path.resolve(`modules/${module}`);
        }
      } else {
        modulePath = masterPath;
      }

      if (modulePath && fs.existsSync(modulePath)) {
        modules.push(modulePath);
      }
    });
  } else {
    modules = fs
      .readdirSync('modules')
      ?.filter((item) =>
        registerModules?.map((item) => item.name)?.includes(item),
      )
      .map((item) => {
        return path.resolve(`modules/${item}`);
      });
    // modules 追加当前master目录 作为主目录
    modules.push(masterPath);
  }

  if (modules.length > 0) {
    console.log('打包的module路径：\n', modules?.join('\n '));

    // 如果存在mater先打包master 并等待master打包结束
    let masterPathIndex = modules?.findIndex((item) => item === masterPath);
    if (masterPathIndex > -1) {
      // 删除masterPath
      modules?.splice(masterPathIndex, 1);
      console.log('master 打包开始...');
      await pool.execute(masterPath).catch((error) => {
        console.error('出现错误：', error);
      });
    }

    // 维护模块 单独打包 以防止可能出现的CI 机器卡住问题
    const reportSysPath = modules?.find((item) => item?.includes('report-sys'));
    if (reportSysPath !== undefined) {
      console.log('维护模块 打包开始...');
      await pool.execute(reportSysPath).catch((error) => {
        console.error('出现错误：', error);
      });
    }

    for (let modulePath of modules?.filter(
      (item) => !item?.includes('report-sys'),
    )) {
      buildList.push(
        pool.execute(modulePath).catch((error) => {
          console.error('出现错误：', error);
        }),
      );

      console.log(`等待${interval}秒再推下一个`);
      await delay(interval);
    }
  }

  await Promise.allSettled(buildList);
  console.log('打包结束，关闭线程池');
  console.log('打包结束：', new Date().toLocaleString());
  pool.destroy();
};

console.log('打包开始：', new Date().toLocaleString());
buildProject();
