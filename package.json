{"private": true, "scripts": {"preinstall": "npx only-allow pnpm", "start": "npm-run-all --parallel start:*", "start:master": "umi dev", "start:slave": "pnpm -r --filter ./modules/** --parallel run start", "build": "umi build", "build:local": "cross-env NODE_ENV=production BUILD_ENV=local node scripts/build.js", "build:alpha": "cross-env NODE_ENV=production BUILD_ENV=alpha node scripts/build.js", "build:beta": "cross-env NODE_ENV=production BUILD_ENV=beta node scripts/build.js", "build:prod": "cross-env NODE_ENV=production BUILD_ENV=prod node scripts/build.js", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage", "prepare": "husky install", "lint-staged": "lint-staged"}, "browserslist": [">0.2%", "not dead", "not op_mini all"], "husky": {"hooks": {"pre-commit": "npm run lint-staged"}}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/icons": "4.7.0", "@uiw/react-markdown-preview": "4.0.24", "@uni/commons": "workspace:^", "@uni/components": "workspace:^", "@uni/reducers": "workspace:^", "@uni/services": "workspace:^", "@uni/utils": "workspace:^", "ahooks": "3.7.1", "antd": "4.24.2", "clsx": "^1.2.1", "core-js": "3.27.2", "crypto-js": "^4.1.1", "dayjs": "^1.9.4", "deepdash": "^5.3.9", "error-stack-parser": "2.1.4", "immutability-helper": "^3.1.1", "jsonwebtoken": "^8.5.1", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "path": "^0.12.7", "poolifier": "2.4.10", "prop-types": "^15.8.1", "qs": "^6.11.0", "react": "18.x", "react-dom": "18.x", "react-error-boundary": "^3.1.4", "react-grid-layout": "1.4.3", "react-highlight-words": "^0.18.0", "react-json-view": "^1.21.3", "react-select": "^5.9.0", "react-sortable-hoc": "2.0.0", "react-virtualized": "^9.22.5", "umi": "3.5.34", "uuid": "^9.0.0"}, "devDependencies": {"@types/node": "^18.14.2", "@types/react": "18.x", "@types/react-dom": "18.x", "@umijs/plugin-qiankun": "^2.42.0", "@umijs/preset-react": "2.1.0", "@umijs/test": "3.5.34", "antd-dayjs-webpack-plugin": "^1.0.6", "babel-loader": "^8.2.5", "babel-plugin-import": "^1.13.5", "child_process": "^1.0.2", "compression-webpack-plugin": "6.1.1", "cross-env": "^7.0.3", "father": "4.1.3", "happypack": "^5.0.1", "husky": "^8.0.0", "less": "^4.1.3", "lint-staged": "13.0.3", "minimist": "^1.2.8", "npm-run-all": "^4.1.5", "prettier": "2.7.1", "prettier-plugin-organize-imports": "2.3.4", "prettier-plugin-packagejson": "2.3.0", "typescript": "^4.1.2", "yorkie": "^2.0.0", "swr": "2.2.5", "@babel/plugin-proposal-optional-chaining": "7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "engines": {"node": ">=14", "pnpm": ">=6"}, "pnpm": {"overrides": {"rc-picker": "git+*******************************************************/masachi.zhang/rc-picker.git#v2.7.66", "rc-tabs": "git+*****************************************************/masachi.zhang/rc-tabs.git#v12.3.2", "minimatch": "5.1.2", "@types/minimatch": "5.1.2"}}}