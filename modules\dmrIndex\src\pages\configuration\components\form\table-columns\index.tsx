import './index.less';
import { But<PERSON>, Form, Modal } from 'antd';
import React, { useEffect } from 'react';
import { UniDragEditTable, UniTable } from '@uni/components/src';
import { menuColumns, tableEditColumns } from '@/pages/configuration/constants';
import {
  tableColumnPropertiesToColumns,
  tableDataSourceProcessor,
} from '@/pages/configuration/components/form/table-columns/processor';
import {
  IcdeTableColumnsBlockProperties,
  OperTableColumnsBlockProperties,
  TableColumnFullProperties,
} from '@/pages/configuration/properties/column';
import { ProForm, ProFormDependency } from '@uni/components/src/pro-form';
import cloneDeep from 'lodash/cloneDeep';
import mergeWith from 'lodash/mergeWith';
import unset from 'lodash/unset';
import pick from 'lodash/pick';
import { isEmptyValues } from '@uni/utils/src/utils';
import {
  TableColumnItemExtraConfigContainer,
  tableExtraColumns,
} from '@/pages/configuration/components/form/table-columns/extra';
import {
  departmentTransferWardColumns,
  departmentTransferColumns,
} from '@uni/grid/src/common/columns';
import { icdeColumns } from '@/pages/dmr/columns';

const departmentTransferWard =
  (window as any).externalConfig?.['dmr']?.departmentTransferWard ?? false;

interface TableColumnsEditTableProps {
  outerForm: any;
  form: any;
  component: string;
  columnDataSource: any[];
}

export const tableComponentToColumnsPropertiesMapping = {
  IcdeDragTable: IcdeTableColumnsBlockProperties,
  OperationDragTable: OperTableColumnsBlockProperties,
  IcuDragTable: [],
  PathologyIcdeDragTable: [],
  // 门急诊 入院
  AdmsIcdeDragTable: [],
  OtpsIcdeDragTable: [],
  TcmIcdeDragTable: IcdeTableColumnsBlockProperties,
};

const TableColumnsEditTable = (props: TableColumnsEditTableProps) => {
  const [tableColumnsDataSource, setTableColumnsDataSource] = React.useState<
    any[]
  >([]);

  const tableItemExtraConfigContainerRef = React.useRef(null);

  useEffect(() => {
    setTableColumnsDataSource(
      tableDataSourceProcessor(
        props?.columnDataSource ?? [],
        tableComponentToColumnsPropertiesMapping[props?.component],
      ),
    );
  }, [props?.columnDataSource]);

  return (
    <>
      <UniDragEditTable
        className={'table-columns-edit-table'}
        bordered={false}
        form={props?.form}
        key={'table-columns-edit-table'}
        id={'table-columns-edit-table'}
        tableId={'table-columns-edit-table'}
        scroll={{
          y: 400,
        }}
        pagination={false}
        forceColumnsUpdate={true}
        columns={[
          ...tableEditColumns(),
          ...tableColumnPropertiesToColumns(TableColumnFullProperties),
          ...tableExtraColumns(tableItemExtraConfigContainerRef),
        ]}
        value={tableColumnsDataSource}
        rowKey={'id'}
        onValuesChange={(recordList: any[]) => {
          console.log('recordList', recordList);
        }}
        onTableDataSourceOrderChange={(tableData) => {
          let formValues = props?.form?.getFieldsValue();
          tableData?.forEach((item, index) => {
            formValues[item?.id]['order'] = index + 1;

            // 设定tableData 与 form Values 合并
            let currentFormItem = formValues[item?.id];
            mergeWith(
              item,
              pick(
                currentFormItem,
                TableColumnFullProperties?.map((item) => item?.key),
              ),
              (objValue, srcValue, key, object) => {
                if (srcValue == null) {
                  unset(object, key);
                }
              },
            );
          });
          props?.form.setFieldsValue({
            ...formValues,
          });

          setTableColumnsDataSource(tableData);
        }}
      />
      <TableColumnItemExtraConfigContainer
        form={props?.form}
        containerRef={tableItemExtraConfigContainerRef}
      />
    </>
  );
};

const TableColumnsConfiguration = (props: any) => {
  const [tableColumnsEditOpen, setTableColumnsEditOpen] =
    React.useState<boolean>(false);

  const tableColumnsItemKey = React.useRef('data.props.columns');

  const [tableForm] = Form.useForm();

  const emrTableEnabled = Form.useWatch('data.props.emrDataTable', props?.form);

  const wardColumnsEditing = React.useRef(false);

  const [tableColumnsDataSource, setTableColumnsDataSource] = React.useState<
    any[]
  >([]);

  const mergeTableColumns = (formColumns: any[], defaultColumns: any[]) => {
    if (isEmptyValues(formColumns)) {
      return defaultColumns;
    }

    let mergedColumns = [...formColumns];

    defaultColumns.forEach((defaultColumnItem, index) => {
      let formColumnItem = formColumns.find((formColumn) => {
        return formColumn.dataIndex === defaultColumnItem.dataIndex;
      });
      if (formColumnItem === undefined) {
        mergedColumns.splice(index, 0, defaultColumnItem);
      }
    });

    return mergedColumns;
  };

  useEffect(() => {
    let columns = mergeTableColumns(
      props?.form.getFieldValue('originData')?.data?.props?.columns,
      props?.fieldProps?.defaultColumns,
    );
    setTableColumnsDataSource(columns);

    props?.form.setFieldValue('data.props.columns', columns);

    if (
      !isEmptyValues(
        props?.form.getFieldValue('originData')?.data?.props?.emrColumns,
      )
    ) {
      let emrColumns = mergeTableColumns(
        props?.form.getFieldValue('originData')?.data?.props?.emrColumns,
        props?.fieldProps?.defaultColumns,
      );
      props?.form.setFieldValue('data.props.emrColumns', emrColumns);
    }
  }, [props?.form.getFieldValue('originData')]);

  console.log('TableColumnsConfiguration', props);

  const itemComponent = props?.form?.getFieldValue('data.component');

  return (
    <>
      {departmentTransferWard === true &&
        itemComponent === 'DepartmentTransfer' && (
          <ProForm.Item hidden={true} name={'data.props.wardColumns'} />
        )}

      {emrTableEnabled === true && (
        <ProForm.Item hidden={true} name={'data.props.emrColumns'} />
      )}

      <ProForm.Item name={'data.props.columns'}>
        {props?.name === 'data.props.columns' && (
          <Button
            type={'primary'}
            onClick={() => {
              setTableColumnsEditOpen(true);
              tableColumnsItemKey.current = 'data.props.columns';
              if (
                departmentTransferWard === true &&
                itemComponent === 'DepartmentTransfer'
              ) {
                wardColumnsEditing.current = false;
                let columns = mergeTableColumns(
                  props?.form.getFieldValue('data.props.columns') ??
                    props?.form.getFieldValue('originData')?.data?.props
                      ?.columns,
                  departmentTransferColumns(false),
                );
                setTableColumnsDataSource(columns);
              }
            }}
          >
            修改表格列属性
          </Button>
        )}

        {props?.name === 'data.props.emrColumns' && emrTableEnabled === true && (
          <Button
            type={'primary'}
            onClick={() => {
              setTableColumnsEditOpen(true);
              tableColumnsItemKey.current = 'data.props.emrColumns';
              let columns = mergeTableColumns(
                props?.form.getFieldValue('data.props.emrColumns') ??
                  props?.form.getFieldValue('originData')?.data?.props
                    ?.emrColumns ??
                  props?.form.getFieldValue('originData')?.data?.props?.columns,
                props?.fieldProps?.defaultColumns,
              );
              setTableColumnsDataSource(columns);
            }}
          >
            修改医生端表格列属性
          </Button>
        )}

        {props?.name === 'data.props.wardColumns' &&
          departmentTransferWard === true &&
          itemComponent === 'DepartmentTransfer' && (
            <Button
              style={{ marginLeft: 10 }}
              type={'primary'}
              onClick={() => {
                setTableColumnsEditOpen(true);
                wardColumnsEditing.current = true;
                let wardColumns = mergeTableColumns(
                  props?.form.getFieldValue('data.props.wardColumns') ??
                    props?.form.getFieldValue('originData')?.data?.props
                      ?.wardColumns ??
                    props?.form.getFieldValue('originData')?.data?.props
                      ?.columns,
                  departmentTransferWardColumns(false),
                );
                setTableColumnsDataSource(wardColumns);
              }}
            >
              修改转科病区表格列属性
            </Button>
          )}

        <Modal
          zIndex={11001}
          title="表格列属性修改"
          open={tableColumnsEditOpen}
          width={1500}
          okText={'确定'}
          cancelButtonProps={{
            style: { display: 'none' },
          }}
          wrapClassName={'table-columns-configuration-container'}
          onOk={async (event) => {
            console.log('form items', tableForm.getFieldsValue());
            // setTableColumnsEditOpen(false);
            let formValues = cloneDeep(tableForm.getFieldsValue());
            let columns = [];
            Object.keys(formValues).forEach((key) => {
              let item = cloneDeep(formValues?.[key]);
              if (item?.['sort']) {
                delete item?.['sort'];
              }

              //先行给一个fixed = 空字符串
              if (item?.['fixed'] === undefined) {
                item['fixed'] = '';
              }

              if (
                tableComponentToColumnsPropertiesMapping[props?.component]?.[
                  item?.dataIndex
                ]
              ) {
                tableComponentToColumnsPropertiesMapping[props?.component]?.[
                  item?.dataIndex
                ]?.forEach((blockKey) => {
                  if (item[blockKey]) {
                    // 不可见同时不可修改
                    delete item?.[blockKey];
                  }
                });
              }

              columns.push(item);
            });
            if (wardColumnsEditing.current === true) {
              props?.form.setFieldValue('data.props.wardColumns', columns);
            } else {
              props?.form.setFieldValue(
                tableColumnsItemKey.current ?? 'data.props.columns',
                columns,
              );
            }
            // 追加修改 当前 dataSource
            setTableColumnsDataSource(columns);
            setTableColumnsEditOpen(false);
          }}
          onCancel={(event) => {
            setTableColumnsEditOpen(false);
          }}
          destroyOnClose={true}
          getContainer={document.getElementById('dmr-configuration-wrapper')}
        >
          <TableColumnsEditTable
            outerForm={props?.form}
            form={tableForm}
            columnDataSource={tableColumnsDataSource}
            component={props?.form?.getFieldValue('data.component')}
          />
        </Modal>
      </ProForm.Item>
    </>
  );
};

export default TableColumnsConfiguration;
