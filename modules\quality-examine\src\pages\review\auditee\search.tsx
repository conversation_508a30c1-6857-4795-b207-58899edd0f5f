import { useEffect } from 'react';
import { useModel } from 'umi';
import { Button, Form, Input } from 'antd';
import { UniSelect } from '@uni/components/src';
import locale from 'antd/es/date-picker/locale/zh_CN';
import Datepicker from '@uni/components/src/picker/datepicker';
import dayjs from 'dayjs';
import { noDisablePermissionAsAdminCoderManagementExtra } from '@/pages/review/utils';
import './index.less';

interface ReviewAuditeeSearchProps {
  searchParams?: any;
  setSearchParams?: (params: any) => void;
  form?: any;
}
const externalSearchConfig = (window as any).externalConfig?.['searchConfig'];
const autoClearSearchValue = externalSearchConfig?.['autoClearSearchValue'];

const ReviewAuditeeSearch = (props: ReviewAuditeeSearchProps) => {
  const { setSearchParams, searchParams, form } = props;
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');

  const onFinish = (values: any) => {
    console.log(values, 'values');
    const date = {
      Sdate: values?.date?.at(0)
        ? dayjs(values?.date?.at(0))?.format('YYYY-MM-DD')
        : undefined,
      Edate: values?.date?.at(1)
        ? dayjs(values?.date?.at(1))?.format('YYYY-MM-DD')
        : undefined,
    };
    setSearchParams({
      ...searchParams,
      ...values,
      // 传参非必要
      ...date,
      batchArgs: { ...date },
    });
  };

  useEffect(() => {
    if (form) {
      const { batchArgs = {} } = searchParams;
      form.setFieldsValue({
        ...searchParams,
        ...batchArgs,
        date: [
          batchArgs?.['Sdate'] ? dayjs(batchArgs?.['Sdate']) : undefined,
          batchArgs?.['Edate'] ? dayjs(batchArgs?.['Edate']) : undefined,
        ],
      });
    }
  }, [searchParams, form]);

  return (
    <>
      <Form
        className={'form'}
        form={form}
        style={{ width: '100%' }}
        layout="inline"
        onFinish={onFinish}
      >
        <Form.Item label="病案标识" name="Keyword">
          <Input placeholder={'请输入病案标识'} />
        </Form.Item>
        <Form.Item label="时间" name="date">
          <Datepicker.RangePicker
            style={{ width: 250 }}
            locale={locale}
            picker={'date'}
            allowClear={false}
            showTime={false}
            format={'YYYY-MM-DD'}
          />
        </Form.Item>
        <Form.Item label="编码员" name="Coder">
          <UniSelect
            allowClear={true}
            placeholder={'请选择编码员'}
            disabled={noDisablePermissionAsAdminCoderManagementExtra(userInfo)}
            dataSource={globalState?.dictData?.Coder || []}
            optionValueKey={'Code'}
            optionNameKey={'Name'}
          />
        </Form.Item>
        <Form.Item label="院区" name="hospCode">
          <UniSelect
            placeholder={'请选择院区'}
            dataSource={globalState?.dictData?.Hospital || []}
            optionValueKey={'Code'}
            optionNameKey={'Name'}
            mode="multiple"
            maxTagCount={'responsive'}
          />
        </Form.Item>
        <Form.Item label="所属学科" name="MajorPerfDepts">
          <UniSelect
            mode={'multiple'}
            maxTagCount={'responsive'}
            placeholder={'请选择学科'}
            dataSource={globalState?.dictData?.MajorPerfDepts || []}
            optionValueKey={'Code'}
            optionNameKey={'Name'}
          />
        </Form.Item>
        <Form.Item label="科室" name="CliDepts">
          <UniSelect
            placeholder={'请选择科室'}
            dataSource={globalState?.dictData?.CliDepts || []}
            optionValueKey={'Code'}
            optionNameKey={'Name'}
            mode="multiple"
            maxTagCount={'responsive'}
            autoClearSearchValue={autoClearSearchValue}
          />
        </Form.Item>
        <Form.Item label="责任医生" name="Doctors">
          <UniSelect
            placeholder={'请选择责任医生'}
            dataSource={
              globalState?.dictData?.Employee?.map((e) => {
                return {
                  ...e,
                  Name: `${e.Name}(${e.ExtraProperties?.Remark ?? '无'})`,
                };
              }) || []
            }
            optionValueKey={'Code'}
            optionNameKey={'Name'}
            mode="multiple"
            maxTagCount={'responsive'}
          />
        </Form.Item>
      </Form>

      <Button
        className={'search-btn'}
        type={'primary'}
        htmlType="submit"
        onClick={() => {
          form?.submit();
        }}
      >
        查询
      </Button>
    </>
  );
};

export default ReviewAuditeeSearch;
