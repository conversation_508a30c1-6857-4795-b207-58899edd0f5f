@import '~@uni/commons/src/style/variables.less';

.dmr-container-common .uni-drag-edit-table-container {
  width: 100%;
  //.ant-table,
  //.ant-table-cell,
  //.ant-table-cell-fix-left,
  //.ant-table-cell-fix-right {
  //  background-color: #ffffff;
  //}

  .ant-table {
    background-color: var(--form-background-color);
  }

  .ant-input {
    background-color: unset;
  }

  tr > td:first-child {
    border-left: 4px solid transparent;
  }

  .ant-table-thead > tr > th {
    background-color: var(--form-background-color);
  }

  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    background-color: var(--form-background-color);
  }

  .ant-table-tbody > tr.ant-table-row:hover > td,
  .ant-table-tbody > tr > td.ant-table-cell-row-hover {
    background-color: var(--form-background-color);
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr:not(:last-child) > td {
    border-bottom-color: var(--separator-color);
  }

  .ant-table-tbody > tr:last-child > td {
    border-bottom: 0px !important;
  }

  .ant-checkbox-disabled .ant-checkbox-inner::after {
    position: absolute;
    display: table;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    transform: rotate(45deg) scale(1) translate(-50%, -50%);
    opacity: 1;
    transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
    content: ' ';
  }

  .ant-select-selector {
    padding: 0 !important;
  }

  .icde-input {
    border: 1px solid transparent !important;
  }

  .in-hospital-diagnosis {
    //border: 1px solid @border-color !important;
  }

  .icde-add-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    padding: 8px 8px;

    span {
      margin-right: 10px;
    }
  }

  .operation-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-evenly;

    a {
      margin: 0px 5px;
    }
  }

  .ant-checkbox-disabled .ant-checkbox-inner {
    background-color: #1890ff !important;
    border-color: #1890ff !important;
  }

  .ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner::after {
    border-color: #ffffff;
  }
}
