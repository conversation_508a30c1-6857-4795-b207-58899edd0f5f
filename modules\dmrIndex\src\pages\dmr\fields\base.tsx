import { IGridItem } from '@/pages/dmr/interfaces';
import { poisons } from '@/pages/dmr/fields/poison';
import { pathologicalDiagnosis } from '@/pages/dmr/fields/pathological-diagnosis';
import { allergy } from '@/pages/dmr/fields/allergy';
import { blood } from '@/pages/dmr/fields/blood';
import { doctors } from '@/pages/dmr/fields/doctors';
import { qualityControl } from '@/pages/dmr/fields/quality-control';
import { outHospital, outHospitalType } from '@/pages/dmr/fields/out-hospital';
import { unconscious } from '@/pages/dmr/fields/unconscious';
import {
  icdeColumns,
  icuColumns,
  operationColumns,
  pathologyIcdeColumns,
} from '@/pages/dmr/columns';
import { fees } from '@/pages/dmr/fields/fees';
import { returnPlan } from '@/pages/dmr/fields/return-plan';
import { tumor } from '@/pages/dmr/fields/tumor';
import { bloodExtra } from '@/pages/dmr/fields/blood-extra';
import { others } from '@/pages/dmr/fields/others';
import {
  inHospital,
  inHospitalIcde,
  inHospitalOthers,
} from '@/pages/dmr/fields/in-hospital';
import { otpDiagnosis } from '@/pages/dmr/fields/otp-diagnosis';

export const diagnosisTable: IGridItem = {
  data: {
    key: 'diagnosisTable',
    component: 'IcdeDragTable',
    props: {
      id: 'diagnosis-table-content',
      parentId: 'diagnosisTable',
      tableId: 'diagnosis-table-content',
      className: '',
      bordered: false,
    },
  },
  h: 10,
  w: 24,
};

export const operationTable: IGridItem = {
  data: {
    key: 'operationTable',
    component: 'OperationDragTable',
    props: {
      id: 'operation-table-content',
      parentId: 'operationTable',
      tableId: 'operation-table-content',
      className: '',
      bordered: false,
    },
  },
  h: 14,
  w: 24,
};

export const icuTable: IGridItem = {
  data: {
    key: 'icuTable',
    component: 'IcuDragTable',
    props: {
      id: 'icu-table-content',
      parentId: 'icuTable',
      tableId: 'icu-table-content',
      className: '',
      bordered: false,
    },
  },
  h: 6,
  w: 24,
};

export const pathologicalDiagnosisTable: IGridItem = {
  data: {
    key: 'pathologicalDiagnosisTable',
    component: 'PathologyIcdeDragTable',
    props: {
      id: 'pathological-diagnosis-table-content',
      parentId: 'pathologicalDiagnosisTable',
      tableId: 'pathological-diagnosis-table-content',
      className: '',
      bordered: false,
    },
  },
  h: 6,
  w: 24,
};

export const admsDiagnosisTable: IGridItem = {
  data: {
    key: 'admsDiagnosisTable',
    component: 'AdmsIcdeDragTable',
    props: {
      id: 'adms-diagnosis-table-content',
      parentId: 'admsDiagnosisTable',
      tableId: 'adms-diagnosis-table-content',
      className: '',
      bordered: false,
    },
  },
  h: 6,
  w: 24,
};

export const otpsDiagnosisTable: IGridItem = {
  data: {
    key: 'otpsDiagnosisTable',
    component: 'OtpsIcdeDragTable',
    props: {
      id: 'otps-diagnosis-table-content',
      parentId: 'otpsDiagnosisTable',
      tableId: 'otps-diagnosis-table-content',
      className: '',
      bordered: false,
    },
  },
  h: 6,
  w: 24,
};

export const base: IGridItem[][] = [
  [
    {
      data: {
        prefix: '姓名',
        key: 'PatName',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
        },
      },
      w: 3,
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '性别',
        key: 'PatSex',
        desc: '',
        suffix: '',
        component: 'AutoSelect',
        props: {
          formKey: 'PatSex',
          modelDataKey: 'XB',
          modelDataGroup: 'Dmr',
          className: '',
          formItemStyle: { width: '100%' },
        },
      },
      w: 3,
      sm: {
        w: 5,
      },
      xs: {
        w: 4,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '出生日期',
        key: 'PatBirth',
        desc: '出生日期',
        suffix: '',
        component: 'DateSelect',
        props: {
          type: 'compact',
          formKey: 'PatBirth',
          style: {
            width: '100%',
          },
          datePicker: false,
          className: 'border-none',
          calculateItems: [
            {
              needCalculateFormKey: 'PatAge',
              calculateFromFormKeys: ['PatBirth', 'InDate'],
              dateCalculateUnits: 'year',
            },
          ],
        },
      },
      offsetX: 2,
      w: 3,
      sm: {
        w: 5,
      },
      xs: {
        w: 5,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '年龄',
        key: 'PatAge',
        desc: '年龄（岁）',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          style: {
            width: '100%',
          },
          min: 0,
          max: 200,
          step: 1,
          formKey: 'PatAge',
          className: 'border-none',
        },
      },
      w: 3,
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '国籍',
        key: 'Country',
        desc: '',
        suffix: '',
        component: 'DmrSelect',
        props: {
          formKey: 'Country',
          modelDataKey: 'GJ',
          modelDataGroup: 'Dmr',
          placeholder: '请选择国籍',
          optionNameKey: 'Name',
        },
      },
      w: 4,
      xxs: {
        w: 10,
      },
    },
  ],
  [
    {
      data: {
        prefix: '（年龄不足1周岁）的年龄',
        key: 'BabyAge',
        desc: '',
        suffix: '天',
        // component: 'BabyAge',
        // props: {
        //   formKey: 'PatAgeMonth',
        // },
        component: 'RestrictInputNumber',
        props: {
          style: {
            width: '100%',
          },
          min: 1,
          max: 365,
          step: 1,
          formKey: 'BabyAge',
          className: 'border-none',
        },
      },
      w: 4,
    },
    {
      data: {
        prefix: '新生儿出生体重',
        key: 'BabyBw',
        desc: '',
        suffix: '克',
        component: 'RestrictInputNumber',
        props: {
          formKey: 'BabyBw',
          min: 100,
          max: 9999,
          precious: 0,
          step: 10,
        },
      },
      w: 4,
      sm: {
        w: 7,
      },
      xs: {
        w: 6,
      },
      xxs: {
        w: 10,
      },
    },
    {
      data: {
        prefix: '新生儿出生体重2',
        key: 'BabyBw2',
        desc: '',
        suffix: '克',
        component: 'RestrictInputNumber',
        props: {
          formKey: 'BabyBw2',
          min: 100,
          max: 9999,
          precious: 0,
          step: 10,
        },
      },
      w: 4,
      sm: {
        w: 7,
      },
      xs: {
        w: 6,
      },
      xxs: {
        w: 10,
      },
    },
  ],
  [
    {
      data: {
        prefix: '新生儿入院体重',
        key: 'BabyIw',
        desc: '',
        suffix: '克',
        component: 'RestrictInputNumber',
        props: {
          formKey: 'BabyIw',
          min: 100,
          max: 9999,
          precious: 0,
          step: 10,
        },
      },
      w: 4,
      sm: {
        w: 6,
      },
      xs: {
        w: 6,
      },
      xxs: {
        w: 10,
      },
    },
    {
      data: {
        prefix: '新生儿入院类型',
        // TODO 字段
        key: 'NwbAdmType',
        desc: '',
        suffix: '',
        component: 'DmrSelect',
        props: {
          formKey: 'NwbAdmType',
          modelDataKey: 'NwbAdmType',
          modelDataGroup: 'Dmr',
          placeholder: '请选择新生儿入院类型',
          optionNameKey: 'Name',
        },
      },
      w: 4,
      sm: {
        w: 6,
      },
      xs: {
        w: 6,
      },
      xxs: {
        w: 10,
      },
    },
  ],
  [
    {
      data: {
        prefix: '出生地',
        key: 'BirthPlaceSeparateSelector',
        desc: '',
        suffix: '',
        component: 'ProvinceSeparateSelector',
        props: {
          formKey: 'BirthPlace',
          itemFormKeys: ['BirthPlaceProv', 'BirthPlaceCity', 'BirthPlaceCoty'],
          showDetailAddress: false,
          fullSelection: true,
        },
      },
      w: 8,
      md: {
        w: 11,
      },
      sm: {
        w: 14,
      },
      xs: {
        w: 12,
        h: 1.5,
        data: {
          itemClassName: 'grid-item-start-container',
        },
      },
      xxs: {
        w: 10,
        h: 1.5,
        data: {
          itemClassName: 'grid-item-start-container',
        },
      },
    },
    {
      data: {
        prefix: '籍贯',
        key: 'NativePlaceSeparateSelector',
        desc: '',
        suffix: '',
        component: 'ProvinceSeparateSelector',
        props: {
          formKey: 'NativePlace',
          itemFormKeys: ['NativePlaceProv', 'NativePlaceCity'],
          showDetailAddress: false,
          hideDistrict: true,
          fullSelection: true,
        },
      },
      w: 6,
      md: {
        w: 8,
      },
      sm: {
        w: 10,
      },
      xs: {
        w: 12,
      },
      xxs: {
        w: 10,
        h: 1.5,
        data: {
          itemClassName: 'grid-item-start-container',
        },
      },
    },
    {
      data: {
        prefix: '民族',
        key: 'PatNation',
        desc: '',
        suffix: '',
        component: 'DmrSelect',
        props: {
          formKey: 'PatNation',
          modelDataKey: 'MZ',
          modelDataGroup: 'Dmr',
          placeholder: '请选择',
          optionNameKey: 'Name',
        },
      },
      w: 4,
    },
  ],
  [
    {
      data: {
        prefix: '证件类别',
        key: 'PatIdKind',
        desc: '',
        suffix: '',
        component: 'AutoSelect',
        props: {
          formKey: 'PatIdKind',
          modelDataKey: 'PatIdKind',
          modelDataGroup: 'Dmr',
          className: '',
          formItemStyle: { width: '100%' },
        },
      },
      w: 5,
      xs: {
        w: 12,
      },
      xxs: {
        w: 10,
      },
    },
    {
      data: {
        prefix: '身份证号',
        key: 'IdCard',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
        },
      },
      w: 7,
      md: {
        w: 9,
      },
      xs: {
        w: 12,
      },
      xxs: {
        w: 10,
      },
    },
    {
      data: {
        prefix: '职业',
        key: 'Job',
        desc: '',
        suffix: '',
        component: 'AutoSelect',
        props: {
          formKey: 'Job',
          modelDataKey: 'Job',
          modelDataGroup: 'Dmr',
          className: '',
          formItemStyle: { width: '100%' },
        },
      },
      w: 3,
      sm: {
        w: 7,
      },
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '婚姻',
        key: 'PatMarrage',
        desc: '',
        suffix: '',
        component: 'AutoSelect',
        props: {
          formKey: 'PatMarrage',
          modelDataKey: 'HY',
          modelDataGroup: 'Dmr',
          className: '',
          formItemStyle: { width: '100%' },
        },
      },
      w: 3,
      sm: {
        w: 7,
      },
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
  ],
  [
    {
      data: {
        prefix: '现住址',
        key: 'CurAddressSeparateSelector',
        desc: '',
        suffix: '',
        component: 'ProvinceSeparateSelector',
        props: {
          formKey: 'CurAddress',
          itemFormKeys: [
            'CurAddressProv',
            'CurAddressCity',
            'CurAddressCoty',
            'CurAddressHousNum',
          ],
          showDetailAddress: true,
          fullSelection: true,
        },
      },
      w: 12,
      md: {
        w: 13,
      },
      sm: {
        w: 14,
        h: 1.3,
        data: {
          itemClassName: 'grid-item-start-container',
        },
      },
      xs: {
        h: 1.7,
        data: {
          itemClassName: 'grid-item-start-container',
        },
      },
      xxs: {
        h: 1.7,
        data: {
          itemClassName: 'grid-item-start-container',
        },
      },
    },
    {
      data: {
        prefix: '电话',
        key: 'CurTel',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
        },
      },
      w: 3,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '邮编',
        key: 'CurPost',
        desc: '',
        suffix: '',
        component: 'PostCode',
        props: {
          codeLength: 6,
          bordered: false,
        },
      },
      w: 3,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
  ],
  [
    {
      data: {
        prefix: '户口地址',
        key: 'RegistAddressSeparateSelector',
        desc: '',
        suffix: '',
        component: 'ProvinceSeparateSelector',
        props: {
          formKey: 'RegistAddress',
          itemFormKeys: [
            'RegistAddrProv',
            'RegistAddrCity',
            'RegistAddrCoty',
            'RegistAddrSubd',
            'RegistAddrHousNum',
          ],
          showDetailAddress: true,
          fullSelection: true,
        },
      },
      w: 15,
      md: {
        w: 13,
      },
      sm: {
        h: 1.3,
        w: 14,
        data: {
          itemClassName: 'grid-item-start-container',
        },
      },
      xs: {
        h: 1.7,
        data: {
          itemClassName: 'grid-item-start-container',
        },
      },
      xxs: {
        h: 1.7,
        data: {
          itemClassName: 'grid-item-start-container',
        },
      },
    },
    {
      data: {
        prefix: '邮编',
        key: 'RegistPost',
        desc: '',
        suffix: '',
        component: 'PostCode',
        props: {
          bordered: false,
        },
      },
      w: 3,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
  ],
  [
    {
      data: {
        prefix: '工作单位及地址',
        key: 'WorkAddress',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
        },
      },
      w: 11,
      md: {
        w: 9,
      },
      sm: {
        w: 7,
      },
      xs: {
        w: 6,
      },
    },
    {
      data: {
        prefix: '单位电话',
        key: 'WorkTel',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '邮编',
        key: 'WorkPost',
        desc: '',
        suffix: '',
        component: 'PostCode',
        props: {
          codeLength: 6,
          bordered: false,
        },
      },
      w: 3,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
  ],
  [
    {
      data: {
        prefix: '联系人姓名',
        key: 'ContactName',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
        },
      },
      w: 3,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '关系',
        key: 'Relationship',
        desc: '',
        suffix: '',
        component: 'AutoSelect',
        props: {
          formKey: 'Relationship',
          modelDataKey: 'GX',
          modelDataGroup: 'Dmr',
          formItemStyle: { width: '100%' },
        },
      },
      w: 3,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '地址',
        key: 'ContactAddress',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
        },
      },
      w: 9,
      md: {
        w: 7,
      },
      sm: {
        w: 5,
      },
      xs: {
        w: 8,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '电话',
        key: 'ContactTel',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
        },
      },
      w: 3,
      xxs: {
        w: 5,
      },
    },
  ],
];

console.error('base', base);

export const sectionHeader = (
  key: string,
  title: string,
  borderColor?: string,
  backgroundColor?: string,
) => {
  return [
    {
      data: {
        key: `SectionHeader-${key}`,
        desc: '',
        suffix: '',
        component: 'SectionHeader',
        props: {
          formKey: `SectionHeader-${key}`,
          label: title,
          sectionHeaderFontColor: '#1D1D1D',
          sectionHeaderBackgroundColor: backgroundColor ?? '#b4dbb5',
          sectionHeaderBorderColor: borderColor ?? '#b4dbb5',
        },
      },
      w: 18,
      h: 1,
    },
  ];
};

export const sectionBottom = (key: string, borderColor?: string) => {
  return [
    {
      data: {
        key: `SectionBottom-${key}`,
        desc: '',
        suffix: '',
        component: 'SectionBottom',
        props: {
          formKey: `SectionBottom-${key}`,
          sectionBottomBorderColor: borderColor ?? '#b4dbb5',
        },
      },
      w: 18,
      h: 1,
    },
  ];
};

export const contentData: IGridItem[][] = [
  ...base,
  inHospital,
  outHospital,
  otpDiagnosis,
  inHospitalIcde,
  inHospitalOthers,
  [diagnosisTable],
  [
    {
      data: {
        prefix: '入院病情：',
        key: 'InHospCondition',
        desc: '',
        suffix: '',
        component: 'InputSuffix',
        props: {
          bordered: false,
          hideInput: true,
          formKey: 'InHospCondition',
          suffixModuleKey: 'RYBQ',
          suffixModuleGroup: 'Dmr',
          className: '',
        },
      },
      w: 24,
    },
  ],
  poisons,
  outHospitalType,
  returnPlan,
  unconscious,
  allergy,
  ...doctors,
  qualityControl,
  [operationTable],
  ...pathologicalDiagnosis,
  ...tumor,
  blood,
  ...bloodExtra,
  ...others,
  [icuTable],
  ...fees,
];
