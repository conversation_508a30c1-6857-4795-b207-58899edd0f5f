import { TableProps, Form, message, InputNumber, Switch } from 'antd';
import { UniSelect } from '@uni/components/src';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import {
  DictionaryItem,
  RespVO,
  TableColumns,
} from '@uni/commons/src/interfaces';
import React, { useEffect, useRef, useState } from 'react';
import UniEditableTable from '@uni/components/src/table/edittable';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { v4 as uuidv4 } from 'uuid';
import IconBtn from '@uni/components/src/iconBtn';
import { HierarchyBedBaseColumns } from './columns';

const HierarchyBedIndex = (props) => {
  const ref = useRef<any>();
  const [form] = Form.useForm();
  const [hierarchyBedTableDataSource, setHierarchyBedTableDataSource] =
    useState([]);
  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);
  const [hierarchyBedColumns, setHierarchyBedColumns] = useState([]);
  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
    sorter: 'Edate desc',
  });

  const backTableOnChange: TableProps<any>['onChange'] = (
    pagi,
    filters,
    sorter: any,
    extra,
  ) => {
    let sorterStr = sorter
      ? `${sorter?.field} ${sorter?.order === 'descend' ? 'desc' : 'asc'}`
      : '';
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
      sorter: sorterStr,
    });

    getHierarchyBedReq(pagi.current, pagi.pageSize, sorterStr);
  };

  const { run: getHierarchyBedReq, loading } = useRequest(
    (current, pageSize, sorter) => {
      let params = {};
      params = {
        HierarchyCode: [props.data?.Code],
        HospCode: [props.data?.HospCode],
        Sdate: props?.data?.Sdate || '1000-01-01',
        Edate: props?.data?.Edate || '9999-01-01',
        DtParam: {
          Draw: 1,
          Start: (current - 1) * pageSize,
          Length: pageSize,
        },
        Sorting: sorter,
      };
      return uniCommonService(`Api/Dyn-ddr/HierarchyBedAmts/GetList`, {
        method: 'GET',
        params: params,
      });
    },
    {
      manual: true,
      formatResult: (response: any) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setHierarchyBedTableDataSource(
            response?.data.Items.map((record) => {
              record['id'] = uuidv4();
              return record;
            }),
          );
          setBackPagination({
            ...backPagination,
            total: response?.data?.TotalCount || 0,
          });
        }
      },
    },
  );

  const { run: getHierarchyBedColumnsReq } = useRequest(
    () => {
      return uniCommonService(`Api/Dyn-ddr/HierarchyBedAmts/GetList`, {
        method: 'GET',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          setHierarchyBedColumns(
            tableColumnBaseProcessor(
              HierarchyBedBaseColumns,
              response?.data?.Columns,
            ),
          );
        } else {
          setHierarchyBedColumns([]);
        }
      },
    },
  );

  useEffect(() => {
    getHierarchyBedColumnsReq();
  }, []);

  useEffect(() => {
    if (props.data?.Code) {
      getHierarchyBedReq(
        backPagination?.current || 1,
        backPagination?.pageSize || 10,
        backPagination?.sorter || 'Edate desc',
      );
    }
  }, [props.data]);

  const { run: createReq } = useRequest(
    (values) => {
      let data = {};
      data = {
        HospCode: props.data?.HospCode,
        HierarchyCode: props.data?.Code,
        HierarchyName: props.data?.Name,
        ...values,
      };
      return uniCommonService(`Api/Dyn-ddr/HierarchyBedAmts/Create`, {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: any) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          getHierarchyBedReq(
            backPagination?.current || 1,
            backPagination?.pageSize || 10,
            backPagination?.sorter || 'Edate desc',
          );
        }
      },
    },
  );

  const { run: updateReq } = useRequest(
    (values) => {
      return uniCommonService(
        `Api/Dyn-ddr/HierarchyBedAmts/Update?id=${values?.Id}`,
        {
          method: 'POST',
          data: values,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: any) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          getHierarchyBedReq(
            backPagination?.current || 1,
            backPagination?.pageSize || 10,
            backPagination?.sorter || 'Edate desc',
          );
        }
      },
    },
  );

  const { run: deleteReq } = useRequest(
    (values) => {
      let data = {};
      data = [values.Id];
      return uniCommonService('Api/Dyn-ddr/HierarchyBedAmts/Delete', {
        method: 'DELETE',
        data: data,
        requestType: 'json',
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('删除成功');
        }
      },
      onSuccess: (response: any, params) => {
        getHierarchyBedReq(
          backPagination?.current || 1,
          backPagination?.pageSize || 10,
          backPagination?.sorter || 'Edate desc',
        );
      },
    },
  );

  useEffect(() => {
    Emitter.on(ConfigurationEvents.HIERARCHY_BED_DELETE, (data) => {
      if (data?.index > -1) {
        deleteReq(data.record);
      }
    });
    return () => {
      Emitter.off(ConfigurationEvents.HIERARCHY_BED_DELETE);
    };
  }, [hierarchyBedTableDataSource]);

  return (
    <UniEditableTable
      actionRef={ref}
      id={`cli-depts-dictionary-table`}
      className={'cli-depts-dictionary-table'}
      rowKey={'id'}
      scroll={{ y: 540, x: 'max-content' }}
      bordered={true}
      loading={loading}
      columns={hierarchyBedColumns}
      value={hierarchyBedTableDataSource}
      clickable={false}
      toolBarRender={null}
      recordCreatorProps={{
        position: 'bottom',
        record: () => ({
          id: uuidv4(),
        }),
      }}
      backendPagination
      pagination={backPagination}
      onTableChange={backTableOnChange}
      editable={{
        form: form,
        type: 'multiple',
        editableKeys: editableColumnKeys,
        onSave: async (rowKey, data, row) => {
          if (data?.Id) {
            updateReq(data);
          } else {
            createReq(data);
          }
        },
        actionRender: (row, config, defaultDoms) => {
          return [defaultDoms.save, defaultDoms.cancel];
        },
        onChange: setEditableColumnKeys,
      }}
    />
  );
};

export default HierarchyBedIndex;
