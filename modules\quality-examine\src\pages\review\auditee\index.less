.auditee-container {
  display: flex;
  flex-direction: column;

  .batch-label {
    font-size: 14px;
    color: #7d87b3;
    white-space: nowrap;
  }

  .form {
    .ant-form-item {
      margin-bottom: 10px;
    }
    .ant-form-item-label > label {
      white-space: nowrap;
      font-size: 14px;
      display: flex;
      color: #7d87b3;
      min-width: 100px;
      width: 100px;
      justify-content: flex-end;
    }
    .ant-form-item-control {
      flex: 1;
      min-width: 250px;
    }
  }

  .batch-range-container {
    display: flex;
    flex-direction: row;
    background: #ffffff;
    padding: 24px;
    margin-bottom: 10px;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #e9e9e9;
  }

  .ant-select-disabled.ant-select:not(.ant-select-customize-input)
    .ant-select-selector {
    background-color: #fff;
    color: rgba(0, 0, 0, 0.85);
  }

  .search-btn {
    width: 100px;
  }
}

.stats-container {
  margin-bottom: 20px;
  .ant-spin-nested-loading,
  .ant-spin-container,
  .single-stat-card {
    height: 100%;
  }
}
.review-person-summary-container {
  background-color: #ffffff;
  .stats-item {
    padding: 16px;
    padding-bottom: 8px;
  }
  .stats-card {
    display: flex;
    overflow-x: auto;
    .ant-pro-checkcard {
      flex: 1 1 0;
      min-width: 140px;
    }
  }
  .edit-btn {
    float: right;
    margin-bottom: 8px;
  }
}
