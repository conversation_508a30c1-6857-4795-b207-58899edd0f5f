import { PropertyItem } from '@/pages/configuration/interfaces';
import {
  admsIcdeColumns,
  icdeColumns,
  icuColumns,
  operationColumns,
  otpsIcdeColumns,
  pathologyIcdeColumns,
  tcmIcdeColumns,
} from '@/pages/dmr/columns';
import {
  departmentTransferColumns,
  departmentTransferWardColumns,
} from '@uni/grid/src/common/columns';

export const tableBasicProperties: PropertyItem[] = [
  {
    key: 'data.props.id',
    label: '表格id',
    component: 'Input',
    required: true,
    fieldProps: {
      disabled: true,
    },
  },
  {
    key: 'data.props.tableId',
    label: '表格id',
    component: 'Select',
    hidden: true,
    fieldProps: {},
  },
  {
    key: 'data.props.parentId',
    label: '上层ID',
    component: 'Input',
    hidden: true,
    fieldProps: {},
  },
];

export const DragTableEmrDataTableProperties: PropertyItem[] = [
  {
    key: 'data.props.emrDataTable',
    label: '显示医生端表格',
    component: 'Switch',
    required: false,
    fieldProps: {},
  },
  {
    key: 'data.props.emrTableWidth',
    label: '医生端表格宽度',
    description: '默认为50%，可填写百分比或者数字，建议填写百分比；例：40%',
    component: 'Input',
    component: 'Input',
    width: 'sm',
    fieldProps: {},
  },
];

export const IcdeDragTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  DragTableEmrDataTableProperties,
  [
    {
      key: 'data.props.columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: icdeColumns,
      },
    },
    {
      key: 'data.props.emrColumns',
      label: '医生端首页表格列',
      description: '医生端首页表格列',
      component: 'DragTable',
      fieldProps: {
        defaultColumns: icdeColumns,
      },
    },
  ],
];

export const OperationDragTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  DragTableEmrDataTableProperties,
  [
    {
      key: 'data.props.columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: operationColumns,
      },
    },
    {
      key: 'data.props.emrColumns',
      label: '医生端首页表格列',
      description: '医生端首页表格列',
      component: 'DragTable',
      fieldProps: {
        defaultColumns: operationColumns,
      },
    },
  ],
];

export const IcuDragTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  [
    {
      key: 'data.props.columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: icuColumns,
      },
    },
  ],
];

export const PathologyIcdeDragTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  DragTableEmrDataTableProperties,
  [
    {
      key: 'data.props.columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: pathologyIcdeColumns,
      },
    },
    {
      key: 'data.props.emrColumns',
      label: '医生端首页表格列',
      description: '医生端首页表格列',
      component: 'DragTable',
      fieldProps: {
        defaultColumns: pathologyIcdeColumns,
      },
    },
  ],
];

export const AdmsIcdeDragTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  DragTableEmrDataTableProperties,
  [
    {
      key: 'data.props.columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: admsIcdeColumns(),
      },
    },
    {
      key: 'data.props.emrColumns',
      label: '医生端首页表格列',
      description: '医生端首页表格列',
      component: 'DragTable',
      fieldProps: {
        defaultColumns: admsIcdeColumns(),
      },
    },
  ],
];

export const OtpsIcdeDragTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  DragTableEmrDataTableProperties,
  [
    {
      key: 'data.props.columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: otpsIcdeColumns(),
      },
    },
    {
      key: 'data.props.emrColumns',
      label: '医生端首页表格列',
      description: '医生端首页表格列',
      component: 'DragTable',
      fieldProps: {
        defaultColumns: otpsIcdeColumns(),
      },
    },
  ],
];

export const TcmIcdeDragTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  [
    {
      key: 'data.props.columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: tcmIcdeColumns,
      },
    },
  ],
];

export const DepartmentTransferTableProperties: PropertyItem[][] = [
  [
    {
      key: 'data.props.transferTile',
      label: '是否平铺',
      component: 'Switch',
      required: false,
      fieldProps: {},
    },
  ],
  [
    {
      key: 'data.props.columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: departmentTransferColumns(false),
      },
    },
    {
      key: 'data.props.wardColumns',
      label: '病区表格列',
      description: '病区表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: departmentTransferWardColumns(false),
      },
    },
  ],
];
