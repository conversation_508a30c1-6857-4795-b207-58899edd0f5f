import { omitBy, isNil, isEmpty } from 'lodash';
import {
  CardBundleInfo,
  DmrInsuranceHqmsIcdeItem,
  IcdeOperItem,
  IcdeOperResp,
  IcdeResp,
  IcdeRespItem,
  TcmIcdeResp,
} from '@/pages/dmr/network/interfaces';
import {
  admsDiagnosisTableRequestParamProcessor,
  admsDiagnosisTableResponseProcessor,
  diagnosisItemRequestParamProcessor,
  diagnosisItemResponseProcessor,
  diagnosisTableRequestParamProcessor,
  diagnosisTableResponseProcessor,
  injuryPoisonedItemRequestParamProcessor,
  injuryPoisonedItemResponseProcessor,
  isHospitalDiagnosisRequestParamProcessor,
  isHospitalDiagnosisResponseProcessor,
  otpsDiagnosisTableRequestParamProcessor,
  otpsDiagnosisTableResponseProcessor,
  pathologicalDiagnosisItemRequestParamProcessor,
  pathologicalDiagnosisItemResponseProcessor,
  pathologicalDiagnosisTableRequestParamProcessor,
  pathologicalDiagnosisTableResponseProcessor,
  tumorItemRequestParamProcessor,
  tumorItemResponseProcessor,
} from '@/pages/dmr/processors/diagnosis';
import {
  operationTableRequestParamProcessor,
  operationTableResponseProcessor,
} from '@/pages/dmr/processors/operation';
import {
  departmentTransferTableRequestParamProcessor,
  departmentTransferTableResponseProcessor,
} from '@/pages/dmr/processors/department-transfer';
import {
  addressPlaceProcessor,
  isMainInVisibleSetFirstLineIsMainTrue,
  newBornAgeRequestParamProcessor,
  timeRangeStatsTransformRequestParamProcessor,
  timeRangeStatsTransformResponseProcessor,
} from '@/pages/dmr/processors/others';
import {
  icuTableRequestParamProcessor,
  icuTableResponseParamProcessor,
} from '@/pages/dmr/processors/icu';
import { isEmptyValues } from '@uni/utils/src/utils';
import { v4 as uuidv4 } from 'uuid';
import {
  babysRequestParamProcessor,
  babyIcdeTableResponseProcessor,
  cardBabysResponseProcessor,
  saveBabysParamProcessor,
} from '@/pages/dmr/processors/baby';
import {
  tcmDiagnosisItemRequestParamProcessor,
  tcmDiagnosisItemResponseProcessor,
  tcmDiagnosisMainItemRequestParamProcessor,
  tcmDiagnosisMainItemResponseProcessor,
  tcmDiagnosisTableRequestParamProcessor,
  tcmDiagnosisTableResponseProcessor,
  tcmIsHospitalDiagnosisRequestParamProcessor,
  tcmIsHospitalDiagnosisResponseProcessor,
} from '@/pages/dmr/processors/tcm-diagnosis';
import { TableItemExtraConfig } from '@/pages/configuration/components/form/table-columns/extra';
import {
  tableTimeDataProcess,
  tableTimeExtraPropsProcess,
} from '@/pages/dmr/processors/table-extra';

const tableAutoAddNewLine =
  (window as any).externalConfig?.['dmr']?.tableAutoAddNewLine ?? false;

const babyWithExtra =
  (window as any).externalConfig?.['dmr']?.babyWithExtra ?? false;

export class DmrProcessor {
  #layouts: any;

  setLayouts = (layouts: any) => {
    this.#layouts = layouts;
  };

  cardIcdeInsuranceHqmsMetaDataProcessor = (
    icdeOperItems: DmrInsuranceHqmsIcdeItem[],
    insuranceMetaData?: IcdeOperItem[],
    hqmsMetaData?: IcdeOperItem[],
  ) => {
    return icdeOperItems?.map((item) => {
      item['insuranceMetaData'] = insuranceMetaData?.find(
        (metaItem) => item?.IcdeCode === metaItem?.Code,
      );
      item['hqmsMetaData'] = hqmsMetaData?.find(
        (metaItem) => item?.IcdeCode === metaItem?.Code,
      );

      return item;
    });
  };

  cardOperInsuranceHqmsMetaDataProcessor = (
    icdeOperItems: DmrInsuranceHqmsIcdeItem[] | any[],
    insuranceMetaData?: IcdeOperItem[],
    hqmsMetaData?: IcdeOperItem[],
  ) => {
    return icdeOperItems?.map((item) => {
      item['insuranceMetaData'] = insuranceMetaData?.find(
        (metaItem) => item?.OperCode === metaItem?.Code,
      );
      item['hqmsMetaData'] = hqmsMetaData?.find(
        (metaItem) => item?.OperCode === metaItem?.Code,
      );

      return item;
    });
  };

  // 反向解析
  // 等一个医保数据过来
  cardIcdeResponseProcessor = async (
    formFieldValue,
    icdeItems: IcdeResp,
    insuranceIcdeItems: DmrInsuranceHqmsIcdeItem[],
    hqmsIcdeItems: DmrInsuranceHqmsIcdeItem[],
    icdeMetaData?: IcdeOperResp,
  ) => {
    if (this.checkFormKeyAndComponentInLayout('IcdeAdmsItem', 'IcdeSelect')) {
      isHospitalDiagnosisResponseProcessor(formFieldValue, icdeItems);
    }

    // 入院表格
    if (
      this.checkFormKeyAndComponentInLayout(
        'admsDiagnosisTable',
        'AdmsIcdeDragTable',
      )
    ) {
      admsDiagnosisTableResponseProcessor(formFieldValue, icdeItems);
    }

    if (this.checkFormKeyAndComponentInLayout('IcdeOtpsItem', 'IcdeSelect')) {
      diagnosisItemResponseProcessor(formFieldValue, icdeItems);
    }

    // 门急诊表格
    if (
      this.checkFormKeyAndComponentInLayout(
        'otpsDiagnosisTable',
        'OtpsIcdeDragTable',
      )
    ) {
      otpsDiagnosisTableResponseProcessor(formFieldValue, icdeItems);
    }

    if (this.checkFormKeyAndComponentInLayout('IcdeDamgsItem')) {
      injuryPoisonedItemResponseProcessor(formFieldValue, icdeItems);
    }

    if (this.checkFormKeyAndComponentInLayout('IcdePathosItem', 'IcdeSelect')) {
      pathologicalDiagnosisItemResponseProcessor(formFieldValue, icdeItems);
    }
    tumorItemResponseProcessor(formFieldValue, icdeItems);

    if (
      this.checkFormKeyAndComponentInLayout(
        'pathologicalDiagnosisTable',
        'PathologyIcdeDragTable',
      )
    ) {
      pathologicalDiagnosisTableResponseProcessor(formFieldValue, icdeItems);
    }

    if (this.checkFormKeyAndComponentInLayout('diagnosisTable')) {
      await diagnosisTableResponseProcessor(
        formFieldValue,
        icdeItems,
        insuranceIcdeItems,
        hqmsIcdeItems,
        icdeMetaData,
      );
    }

    return formFieldValue;
  };

  cardTcmIcdeResponseProcessor = (
    formFieldValue,
    tcmIcdeItems: TcmIcdeResp,
    icdeMetaData?: IcdeOperResp,
  ) => {
    if (this.checkFormKeyAndComponentInLayout('TcmIcdeAdmsItem')) {
      tcmIsHospitalDiagnosisResponseProcessor(formFieldValue, tcmIcdeItems);
    }

    //门急诊中医证候
    if (this.checkFormKeyAndComponentInLayout('TcmIcdeOtpsItem')) {
      tcmDiagnosisItemResponseProcessor(formFieldValue, tcmIcdeItems);
    }

    // 门急诊中医诊断
    if (this.checkFormKeyAndComponentInLayout('TcmIcdeOtpsMainItem')) {
      tcmDiagnosisMainItemResponseProcessor(formFieldValue, tcmIcdeItems);
    }

    if (this.checkFormKeyAndComponentInLayout('tcmDiagnosisTable')) {
      tcmDiagnosisTableResponseProcessor(
        formFieldValue,
        tcmIcdeItems,
        icdeMetaData,
      );
    }

    return formFieldValue;
  };

  cardOperationResponseProcessor = async (
    formFieldValue,
    operationItems: any[],
    insuranceOperationItems: any[],
    hqmsOperationItems: any[],
    operMetaData?: IcdeOperResp,
  ) => {
    if (this.checkFormKeyAndComponentInLayout('operationTable')) {
      await operationTableResponseProcessor(
        formFieldValue,
        operationItems,
        insuranceOperationItems,
        hqmsOperationItems,
        operMetaData,
      );
    }

    return formFieldValue;
  };

  cardTransferResponseProcessor = (
    formFieldValue,
    departmentTransfers: any,
  ) => {
    if (this.checkFormKeyAndComponentInLayout('TransDept')) {
      departmentTransferTableResponseProcessor(
        formFieldValue,
        departmentTransfers,
      );
    }

    return formFieldValue;
  };

  cardIcuResponseProcessor = (formFieldValue, icus: any) => {
    if (this.checkFormKeyAndComponentInLayout('icuTable')) {
      icuTableResponseParamProcessor(formFieldValue, icus);
    }

    return formFieldValue;
  };

  cardBabyResponseProcessor = (
    formFieldValue,
    babys: any,
    babyIcdes: IcdeRespItem[],
  ) => {
    cardBabysResponseProcessor(formFieldValue, babys, babyIcdes);

    return formFieldValue;
  };

  cardExtraPropertiesResponseProcessor = (
    formFieldValue,
    extraProperties: any,
  ) => {
    this.#layouts['lg']
      ?.filter((item) => item?.data?.includeExtraProperties === true)
      ?.forEach((item) => {
        if (!isEmptyValues(item?.data?.props?.formKey)) {
          formFieldValue[item?.data?.props?.formKey] =
            extraProperties?.[item?.data?.props?.formKey] ?? '';
        }
      });
  };

  cardTimeRangeStatsTransformProcessor = (formFieldValue) => {
    this.#layouts['lg']
      ?.filter((item) => item?.data?.component === 'TimeRangeStats')
      ?.forEach((item) => {
        // 当且仅当 只有小时的时候才处理 并且仅是
        if (item?.data?.props?.hourOnly === true) {
          formFieldValue = timeRangeStatsTransformResponseProcessor(
            item?.data?.props?.hourOnlyItems,
            item?.data?.props?.items,
            formFieldValue,
          );
        }
      });

    return formFieldValue;
  };

  cardSaveCheckParamProcessor = async (
    data,
    originCardInfo: CardBundleInfo,
    formFieldsValue,
    modelData,
  ) => {
    data['CardFlat'] = {
      ...originCardInfo.CardFlat,
      ...this.saveCardInfoParamProcessor(formFieldsValue, modelData),
    };

    this.extraPropertiesRequestParamProcessor(
      formFieldsValue,
      data,
      originCardInfo,
    );

    // 当IsMain 不显示的时候 设定 手术 / 出院诊断 的第一条为IsMain = true 同时设定其他项目的IsMain = false
    isMainInVisibleSetFirstLineIsMainTrue(formFieldsValue);

    // TODO 表格内日期 转换
    this.tableTimeDataExtraProcess(formFieldsValue, originCardInfo);

    // 手术
    // 诊断
    // 转科
    let icdeOperationSaveParams =
      this.saveCardIcdeOperationParamProcessor(formFieldsValue);
    data['IcdeResult'] = {
      IcdeAdms: this.mergeIcdeOperItemWithUniqueId(
        icdeOperationSaveParams?.['CardIcdeAdms'],
        originCardInfo?.IcdeResult?.['IcdeAdms'],
      ),
      IcdeDamgs: this.mergeIcdeOperItemWithUniqueId(
        icdeOperationSaveParams?.['CardIcdeDamgs'],
        originCardInfo?.IcdeResult?.['IcdeDamgs'],
      ),
      IcdeDscgs: this.mergeIcdeOperItemWithUniqueId(
        icdeOperationSaveParams?.['CardIcdeDscgs'],
        originCardInfo?.IcdeResult?.['IcdeDscgs'],
      ),
      IcdeOtps: this.mergeIcdeOperItemWithUniqueId(
        icdeOperationSaveParams?.['CardIcdeOtps'],
        originCardInfo?.IcdeResult?.['IcdeOtps'],
      ),
      IcdePathos: this.mergeIcdeOperItemWithUniqueId(
        icdeOperationSaveParams?.['CardIcdePathos'],
        originCardInfo?.IcdeResult?.['IcdePathos'],
      ),
      // IcdeTcms 暂时没有processor对应
      IcdeTcms: originCardInfo?.IcdeResult?.IcdeTcms,
    };
    data['Opers'] = this.mergeIcdeOperItemWithUniqueId(
      icdeOperationSaveParams?.['CardOpers'],
      originCardInfo?.Opers,
    );

    // 中医诊断
    let tcmIcdeSaveParams = this.saveCardTcmIcdeParamProcessor(formFieldsValue);

    console.log('tcmIcdeSaveParams', tcmIcdeSaveParams);

    data['TcmIcdeResult'] = {
      TcmIcdeAdms: this.mergeIcdeOperItemWithUniqueId(
        tcmIcdeSaveParams?.['CardTcmIcdeAdms'],
        originCardInfo?.TcmIcdeResult?.['TcmIcdeAdms'],
      ),

      TcmIcdeDscgs: this.mergeIcdeOperItemWithUniqueId(
        tcmIcdeSaveParams?.['CardTcmIcdeDscgs'],
        originCardInfo?.TcmIcdeResult?.['TcmIcdeDscgs'],
      ),

      TcmIcdeOtps: this.mergeIcdeOperItemWithUniqueId(
        [
          ...(tcmIcdeSaveParams?.['CardTcmIcdeOtpsMain'] ?? []),
          ...(tcmIcdeSaveParams?.['CardTcmIcdeOtps'] ?? []),
        ],
        originCardInfo?.TcmIcdeResult?.['TcmIcdeOtps'],
      ),
    };

    data['Icus'] = this.mergeTableItemWithId(
      icdeOperationSaveParams?.['CardIcus'],
      originCardInfo?.Icus,
    );

    data['Transfers'] = this.mergeTableItemWithId(
      this.saveCardTransferParamProcessor(formFieldsValue)?.['CardTransfers'],
      originCardInfo?.Transfers,
    );

    // 医保诊断手术
    data['InsurIcdeDscgs'] = this.mergeIcdeOperItemWithUniqueId(
      this.saveCardInsuranceIcdeParamProcessor(formFieldsValue),
      originCardInfo?.InsurIcdeDscgs,
    );
    data['InsurOpers'] = this.mergeIcdeOperItemWithUniqueId(
      this.saveCardInsuranceOperParamProcessor(formFieldsValue),
      originCardInfo?.InsurOpers,
    );
    // HQMS诊断手术
    data['HqmsIcdeDscgs'] = this.mergeIcdeOperItemWithUniqueId(
      this.saveCardHqmsIcdeParamProcessor(formFieldsValue),
      originCardInfo?.HqmsIcdeDscgs,
    );
    data['HqmsOpers'] = this.mergeIcdeOperItemWithUniqueId(
      this.saveCardHqmsOperParamProcessor(formFieldsValue),
      originCardInfo?.HqmsOpers,
    );

    if (babyWithExtra === true) {
      // 新生儿附页
      let { babys, babyIcdes } = babysRequestParamProcessor(
        formFieldsValue,
        true,
      );

      data['IcdeResult']['IcdeBabys'] = this.mergeIcdeOperItemWithUniqueId(
        babyIcdes,
        originCardInfo?.IcdeResult?.['IcdeBabys'],
      );
      data['Babys'] = babys;
    } else {
      if (
        formFieldsValue?.['PatAge'] &&
        parseInt(formFieldsValue?.['PatAge']) > 1
      ) {
        data['Babys'] = this.mergeTableItemWithId(
          this.saveBabysParamProcessor(formFieldsValue),
          originCardInfo?.Babys,
        );
      }
    }

    data['Preg'] = formFieldsValue?.['pregnantItem'];

    return data;
  };

  saveCardInfoParamProcessor = (formFieldValues, modelData?: any) => {
    let data = Object.assign({}, formFieldValues);

    //TODO 部分字段需要 重写
    // 重写过敏药物
    if (data['AllergyDrugs']) {
      data['AllergyDrugs'] = data['AllergyDrugs'].replaceAll('，', ',');
    }

    if (data['Autopsy'] && data['Autopsy'] === '-') {
      data['Autopsy'] = undefined;
    }

    // main icde outcome processor
    // 判定layout中是否存在MainIcdeOutcome 字段 不存在的话置为 null
    if (
      this.#layouts['lg']?.find(
        (item) => item?.data?.key === 'MainIcdeOutcome',
      ) === undefined
    ) {
      data['MainIcdeOutcome'] = null;
    }

    // 重写NativePlace BirthPlace CurAddress RegistAddress
    addressPlaceProcessor(data, formFieldValues, modelData);

    this.#layouts['lg']
      ?.filter((item) => item?.data?.component === 'TimeRangeStats')
      ?.forEach((item) => {
        // 当且仅当 只有小时的时候才处理 并且仅是
        if (item?.data?.props?.hourOnly === true) {
          timeRangeStatsTransformRequestParamProcessor(
            item?.data?.props?.hourOnlyItems,
            item?.data?.props?.items,
            data,
          );
        }
      });

    this.#layouts['lg']
      ?.filter((item) => item?.data?.key === 'NwbAge')
      ?.forEach((item) => {
        newBornAgeRequestParamProcessor(data);
      });

    return data;
  };

  extraPropertiesRequestParamProcessor = (
    formFieldsValue: any,
    data: any,
    originCardInfo: any,
  ) => {
    let extraProperties = {};
    this.#layouts['lg']
      ?.filter((item) => item?.data?.includeExtraProperties === true)
      ?.forEach((item: any) => {
        if (!isEmptyValues(item?.data?.props?.formKey)) {
          let extraKeyValue = formFieldsValue?.[item?.data?.props?.formKey];
          extraProperties[item?.data?.props?.formKey] = extraKeyValue ?? '';

          delete data['CardFlat']?.[item?.data?.props?.formKey];
        }
      });

    data['CardFlat']['ExtraProperties'] = {
      ...(originCardInfo?.['CardFlat']?.['ExtraProperties'] ?? {}),
      ...extraProperties,
    };
  };

  tableTimeDataExtraProcess = (formFieldValues: any, originCardInfo: any) => {
    if (this.checkFormKeyAndComponentInLayout('operationTable')) {
      let operationColumns = this.#layouts['lg']?.find(
        (item) => item?.data?.key === 'operationTable',
      )?.data?.props?.columns;

      tableTimeDataProcess(
        operationColumns,
        formFieldValues?.['operation-table'],
        originCardInfo?.Opers,
      );
    }

    if (this.checkFormKeyAndComponentInLayout('icuTable')) {
      let icuColumns = this.#layouts['lg']?.find(
        (item) => item?.data?.key === 'icuTable',
      )?.data?.props?.columns;

      tableTimeDataProcess(
        icuColumns,
        formFieldValues?.['icu-table'],
        originCardInfo?.Icus,
      );
    }
  };

  chsGroupParamProcessor = (formFieldValues) => {
    let chsGroupKeys = [
      'PatBirth',
      'PatSex',
      'PatAge',
      'PatDay',
      'OutType',
      'InDate',
      'OutDate',
      'InPeriod',
      'BabyIw',
      'BabyBw',
      'MedfeeSumamt',
    ];

    let data = {};
    chsGroupKeys.forEach((key) => {
      if (formFieldValues[key]) {
        data[key] = formFieldValues[key];
      }
    });

    data['InsurCardIcdes'] = this.saveCardInsuranceIcdeParamProcessor(
      formFieldValues,
    )?.filter((item) => {
      return item?.IsReported;
    });

    data['InsurCardOpers'] = this.saveCardInsuranceOperParamProcessor(
      formFieldValues,
    )?.filter((item) => {
      return item?.IsReported;
    });

    return omitBy(data, isNil);
  };

  saveCardIcdeOperationParamProcessor = (
    formFieldValues,
    ignoreNull?: boolean,
  ) => {
    let data = {};

    if (this.checkFormKeyAndComponentInLayout('IcdeAdmsItem', 'IcdeSelect')) {
      isHospitalDiagnosisRequestParamProcessor(
        formFieldValues,
        data,
        ignoreNull,
      );
    }

    // 入院表格
    if (
      this.checkFormKeyAndComponentInLayout(
        'admsDiagnosisTable',
        'AdmsIcdeDragTable',
      )
    ) {
      admsDiagnosisTableRequestParamProcessor(
        formFieldValues,
        data,
        ignoreNull,
      );
    }

    if (this.checkFormKeyAndComponentInLayout('IcdeOtpsItem', 'IcdeSelect')) {
      diagnosisItemRequestParamProcessor(formFieldValues, data, ignoreNull);
    }

    // 门急诊 表格
    if (
      this.checkFormKeyAndComponentInLayout(
        'otpsDiagnosisTable',
        'OtpsIcdeDragTable',
      )
    ) {
      otpsDiagnosisTableRequestParamProcessor(
        formFieldValues,
        data,
        ignoreNull,
      );
    }

    if (this.checkFormKeyAndComponentInLayout('IcdeDamgsItem')) {
      injuryPoisonedItemRequestParamProcessor(
        formFieldValues,
        data,
        ignoreNull,
      );
    }

    if (this.checkFormKeyAndComponentInLayout('IcdePathosItem', 'IcdeSelect')) {
      pathologicalDiagnosisItemRequestParamProcessor(
        formFieldValues,
        data,
        ignoreNull,
      );
      tumorItemRequestParamProcessor(formFieldValues, data);
    }

    if (
      this.checkFormKeyAndComponentInLayout(
        'pathologicalDiagnosisTable',
        'PathologyIcdeDragTable',
      )
    ) {
      pathologicalDiagnosisTableRequestParamProcessor(
        formFieldValues,
        data,
        ignoreNull,
      );
    }

    if (this.checkFormKeyAndComponentInLayout('diagnosisTable')) {
      diagnosisTableRequestParamProcessor(formFieldValues, data, ignoreNull);
    }

    if (this.checkFormKeyAndComponentInLayout('operationTable')) {
      operationTableRequestParamProcessor(formFieldValues, data, ignoreNull);
    }

    if (this.checkFormKeyAndComponentInLayout('icuTable')) {
      icuTableRequestParamProcessor(formFieldValues, data, ignoreNull);
    }

    return data;
  };

  saveCardTcmIcdeParamProcessor = (formFieldValues, ignoreNull?: boolean) => {
    let data = {};

    if (this.checkFormKeyAndComponentInLayout('TcmIcdeAdmsItem')) {
      tcmIsHospitalDiagnosisRequestParamProcessor(
        formFieldValues,
        data,
        ignoreNull,
      );
    }

    //门急诊中医证候
    if (this.checkFormKeyAndComponentInLayout('TcmIcdeOtpsItem')) {
      tcmDiagnosisItemRequestParamProcessor(formFieldValues, data, ignoreNull);
    }

    // 门急诊中医诊断
    if (this.checkFormKeyAndComponentInLayout('TcmIcdeOtpsMainItem')) {
      tcmDiagnosisMainItemRequestParamProcessor(
        formFieldValues,
        data,
        ignoreNull,
      );
    }

    if (this.checkFormKeyAndComponentInLayout('tcmDiagnosisTable')) {
      tcmDiagnosisTableRequestParamProcessor(formFieldValues, data, ignoreNull);
    }

    return data;
  };

  saveCardTransferParamProcessor = (formFieldValues, ignoreNull?: boolean) => {
    let data = {};

    if (this.checkFormKeyAndComponentInLayout('TransDept')) {
      departmentTransferTableRequestParamProcessor(
        formFieldValues,
        data,
        ignoreNull,
      );
    }

    return data;
  };

  saveCardInsuranceIcdeParamProcessor = (formFieldValues) => {
    return (
      (formFieldValues?.['diagnosis-table'] || [])
        .filter((item) => filterIdAddAndAllCellEmptyRow(item))
        // 顺序 IsMain优先级最高
        .sort((a, b) =>
          (a?.IsMain ?? false) === (b?.IsMain ?? false)
            ? 0
            : a?.IsMain ?? false
            ? -1
            : 1,
        )
        .map((item, index) => {
          let insuranceIcdeItem = Object.assign({}, item);

          insuranceIcdeItem['IcdeSort'] = index;

          // 首页
          insuranceIcdeItem['SrcIcdeCode'] = item['IcdeCode'];
          insuranceIcdeItem['SrcIcdeName'] = item['IcdeName'];
          // 医保
          insuranceIcdeItem['IcdeCode'] = item['InsurCode'];
          insuranceIcdeItem['IcdeName'] = item['InsurName'];

          if (
            typeof insuranceIcdeItem['id'] === 'string' ||
            insuranceIcdeItem['id'] instanceof String
          ) {
            delete insuranceIcdeItem['id'];
          }

          return insuranceIcdeItem;
        })
    );
  };

  saveCardInsuranceOperParamProcessor = (formFieldValues) => {
    return (formFieldValues?.['operation-table'] || [])
      .filter((item) => filterIdAddAndAllCellEmptyRow(item))
      .sort((a, b) =>
        (a?.IsMain ?? false) === (b?.IsMain ?? false)
          ? 0
          : a?.IsMain ?? false
          ? -1
          : 1,
      )
      .map((item, index) => {
        let insuranceOperItem = Object.assign({}, item);

        insuranceOperItem['OperSort'] = index + 1;

        // 首页
        insuranceOperItem['SrcOperCode'] = item['OperCode'];
        insuranceOperItem['SrcOperName'] = item['OperName'];
        insuranceOperItem['SrcOperRate'] = item['OperRate'];
        // 医保
        insuranceOperItem['OperCode'] = item['InsurCode'];
        insuranceOperItem['OperName'] = item['InsurName'];

        if (
          typeof insuranceOperItem['id'] === 'string' ||
          insuranceOperItem['id'] instanceof String
        ) {
          delete insuranceOperItem['id'];
        }

        return insuranceOperItem;
      });
  };

  saveCardHqmsIcdeParamProcessor = (formFieldValues) => {
    return (formFieldValues?.['diagnosis-table'] || [])
      .filter((item) => filterIdAddAndAllCellEmptyRow(item))
      .map((item, index) => {
        let hqmsIcdeItem = Object.assign({}, item);
        hqmsIcdeItem['IcdeSort'] = index;
        if (index === 0) {
          hqmsIcdeItem['IsMain'] = item?.['HqmsIsMain'] ?? true;
        } else {
          hqmsIcdeItem['IsMain'] = item?.['HqmsIsMain'] ?? false;
        }
        hqmsIcdeItem['IsReported'] = item?.['HqmsIsReported'] ?? true;
        // 首页
        hqmsIcdeItem['SrcIcdeCode'] = item['IcdeCode'];
        hqmsIcdeItem['SrcIcdeName'] = item['IcdeName'];
        // hqms
        hqmsIcdeItem['IcdeCode'] = item['HqmsCode'];
        hqmsIcdeItem['IcdeName'] = item['HqmsName'];

        if (
          typeof hqmsIcdeItem['id'] === 'string' ||
          hqmsIcdeItem['id'] instanceof String
        ) {
          delete hqmsIcdeItem['id'];
        }

        return hqmsIcdeItem;
      });
  };

  saveCardHqmsOperParamProcessor = (formFieldValues) => {
    return (formFieldValues?.['operation-table'] || [])
      .filter((item) => filterIdAddAndAllCellEmptyRow(item))
      .map((item, index) => {
        let hqmsOperItem = Object.assign({}, item);
        hqmsOperItem['OperSort'] = index + 1;
        if (index === 0) {
          hqmsOperItem['IsMain'] = item?.['HqmsIsMain'] ?? true;
        } else {
          hqmsOperItem['IsMain'] = item?.['HqmsIsMain'] ?? false;
        }
        hqmsOperItem['IsReported'] = item?.['HqmsIsReported'] ?? true;
        // 首页
        hqmsOperItem['SrcOperCode'] = item['OperCode'];
        hqmsOperItem['SrcOperName'] = item['OperName'];
        hqmsOperItem['SrcOperRate'] = item['OperRate'];
        // 医保
        hqmsOperItem['OperCode'] = item['HqmsCode'];
        hqmsOperItem['OperName'] = item['HqmsName'];

        if (
          typeof hqmsOperItem['id'] === 'string' ||
          hqmsOperItem['id'] instanceof String
        ) {
          delete hqmsOperItem['id'];
        }

        return hqmsOperItem;
      });
  };

  saveBabysParamProcessor = (formFieldValues) => {
    return saveBabysParamProcessor(formFieldValues);
  };

  checkFormKeyAndComponentInLayout = (
    formKey: string,
    componentName?: string,
  ) => {
    /**
     * 现在只有
     * 入院诊断: IcdeAdmsItem
     * 门急诊： IcdeOtpsItem
     * 损伤：IcdeDamgsItem
     * 病理： IcdePathosItem
     * 肿瘤： TODO
     * 出院诊断： diagnosisTable
     * 手术表格： operationTable
     * 病理表格： pathologicalDiagnosisTable
     * 转科表格： TransDept
     * ICU表格： icuTable
     */
    let layoutItem = this.#layouts['lg']?.find(
      (item) => item?.data?.key === formKey,
    );

    if (layoutItem) {
      // 存在componentName 需要判定当前item是否和componentName一样
      if (componentName) {
        return layoutItem?.data?.component === componentName;
      }

      return true;
    }

    return false;
  };

  mergeIcdeOperItemWithUniqueId = (formItems: any[], sourceItems: any[]) => {
    let mergedItems = [];

    // 当且仅当formItems === undefined || null 时才返回sourceItems
    // 像是中医字段不是每家医院都有的 目前仅这里会出这个问题
    if (formItems === undefined || formItems === null) {
      return sourceItems;
    }

    formItems?.forEach((formItem) => {
      if (isEmptyValues(formItem?.['UniqueId'])) {
        formItem['UniqueId'] = uuidv4();
      }

      let sourceItem = sourceItems?.find(
        (item) =>
          item?.UniqueId?.toLowerCase() === formItem?.UniqueId?.toLowerCase(),
      );
      mergedItems.push({
        ...(sourceItem ?? {}),
        ...formItem,
      });
    });

    return mergedItems;
  };

  mergeTableItemWithId = (formItems: any[], sourceItems: any[]) => {
    let mergedItems = [];

    // 当且仅当formItems === undefined || null 时才返回sourceItems
    if (formItems === undefined || formItems === null) {
      return sourceItems;
    }

    formItems?.forEach((formItem) => {
      let sourceItem = sourceItems?.find(
        (item) =>
          (item?.id ?? item?.Id)?.toString()?.toLowerCase() ===
          (formItem?.id ?? formItem?.Id)?.toString()?.toLowerCase(),
      );
      mergedItems.push({
        ...(sourceItem ?? {}),
        ...formItem,
      });
    });

    return mergedItems;
  };
}

export const icdeOperationTableNoDataAddOne = (tableData) => {
  if (tableAutoAddNewLine !== true) {
    return tableData;
  }

  if (tableData?.length > 0) {
    return tableData;
  }

  return [
    {
      id: Math.round(Date.now() / 1000),
      // 删除对 医保主手术的 设定 source: DMRDEV-816
      IsMain: false,
      IsReported: true,
      UniqueId: uuidv4(),
    },
  ];
};

export const filterIcdeCodeEmpty = (tableItem) => {
  return !isEmptyValues(tableItem?.IcdeCode);
};

export const filterIdAddAndAllCellEmptyRow = (tableItem) => {
  if (tableItem?.id === 'ADD') {
    return false;
  }

  let filterIndexes = ['id', 'IsMain', 'IsReported', 'UniqueId'];

  // 除了ID 每一个都不存在
  if (
    Object.keys(tableItem)
      ?.filter((key) => !filterIndexes?.includes(key))
      ?.every((key) => isEmptyValues(tableItem[key]))
  ) {
    return false;
  }

  return true;
};
