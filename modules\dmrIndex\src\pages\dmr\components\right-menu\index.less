.dmr-menu-container-doctor-emr-enable {
  right: 20px;
  left: unset !important;
}

.dmr-menu-container-right {
  right: 20px;
  left: unset !important;
}

.dmr-menu-container {
  display: flex;
  flex-direction: column;
  position: absolute;
  //right: 0px;
  left: 20px;
  top: 200px;
  z-index: 2;

  .ant-steps-item-icon {
    height: 7px !important;
    width: 7px !important;
    margin-right: 5px !important;
  }

  .ant-steps-item-title {
    line-height: 30px;
    padding-right: 10px;
    font-size: 0.95rem;
  }

  .ant-steps-icon-dot {
    top: -0.5px !important;
    left: 0px !important;
  }

  .menus-container {
    margin: 16px 0;
    padding-left: 0;
    font-size: 12px;
    list-style: none;
    border-left: 1px solid rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
  }

  .ant-steps
    .ant-steps-item:not(.ant-steps-item-active)
    > .ant-steps-item-container[role='button']:hover
    .ant-steps-item-title {
    color: initial;
    //color: rgba(0, 0, 0, 0.45);
  }
}
